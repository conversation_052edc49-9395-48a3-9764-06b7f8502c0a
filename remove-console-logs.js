const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);

// Extensions to process
const extensions = ['.js', '.jsx', '.ts', '.tsx'];

async function getFiles(dir) {
  const subdirs = await readdir(dir);
  const files = await Promise.all(
    subdirs.map(async (subdir) => {
      const res = path.resolve(dir, subdir);
      return (await stat(res)).isDirectory() ? getFiles(res) : res;
    })
  );
  return files
    .flat()
    .filter((file) => 
      extensions.includes(path.extname(file)) && 
      !file.includes('node_modules') && 
      !file.includes('.next')
    );
}

async function removeConsoleLogs(filePath) {
  try {
    const content = await readFile(filePath, 'utf8');
    
    const patterns = [
      /console\.log\s*\([^)]*\)\s*;?/g,                 
      /console\.log\s*\([^)]*\)\s*,/g,                  
      /console\.log\s*\(`[^`]*`\)\s*;?/g,               
      /console\.log\s*\(`[^`]*`\s*,\s*[^)]*\)\s*;?/g,   
      /console\.log\s*\("[^"]*"\s*,\s*[^)]*\)\s*;?/g,  
      /console\.log\s*\('[^']*'\s*,\s*[^)]*\)\s*;?/g,  
    ];
    
    let newContent = content;
    
    for (const pattern of patterns) {
      newContent = newContent.replace(pattern, '');
    }
    
    if (newContent !== content) {
      await writeFile(filePath, newContent, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error);
    return false;
  }
}

async function main() {
  try {
    const srcDir = path.join(__dirname, 'src');
    const files = await getFiles(srcDir);    
    let modifiedCount = 0;
    
    for (const file of files) {
      const modified = await removeConsoleLogs(file);
      if (modified) {
        modifiedCount++;
      }
    }    
  } catch (error) {
    console.error('Error:', error);
  }
}

main();
