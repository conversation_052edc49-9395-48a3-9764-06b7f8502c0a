#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);

// Extensions to process
const extensions = ['.js', '.jsx', '.ts', '.tsx', '.css', '.scss'];

// Mapping of hardcoded gray colors to theme variables
const grayColorMappings = {
  // Tailwind gray classes to semantic gray classes
  'text-gray-50': 'text-semantic-gray-50',
  'text-gray-100': 'text-semantic-gray-100',
  'text-gray-200': 'text-semantic-gray-200',
  'text-gray-300': 'text-semantic-gray-300',
  'text-gray-400': 'text-semantic-gray-400',
  'text-gray-500': 'text-semantic-gray-500',
  'text-gray-600': 'text-semantic-gray-600',
  'text-gray-700': 'text-semantic-gray-700',
  'text-gray-800': 'text-semantic-gray-800',
  'text-gray-900': 'text-semantic-gray-900',
  
  'bg-gray-50': 'bg-semantic-gray-50',
  'bg-gray-100': 'bg-semantic-gray-100',
  'bg-gray-200': 'bg-semantic-gray-200',
  'bg-gray-300': 'bg-semantic-gray-300',
  'bg-gray-400': 'bg-semantic-gray-400',
  'bg-gray-500': 'bg-semantic-gray-500',
  'bg-gray-600': 'bg-semantic-gray-600',
  'bg-gray-700': 'bg-semantic-gray-700',
  'bg-gray-800': 'bg-semantic-gray-800',
  'bg-gray-900': 'bg-semantic-gray-900',
  
  'border-gray-50': 'border-semantic-gray-50',
  'border-gray-100': 'border-semantic-gray-100',
  'border-gray-200': 'border-semantic-gray-200',
  'border-gray-300': 'border-semantic-gray-300',
  'border-gray-400': 'border-semantic-gray-400',
  'border-gray-500': 'border-semantic-gray-500',
  'border-gray-600': 'border-semantic-gray-600',
  'border-gray-700': 'border-semantic-gray-700',
  'border-gray-800': 'border-semantic-gray-800',
  'border-gray-900': 'border-semantic-gray-900',
  
  'hover:text-gray-50': 'hover:text-semantic-gray-50',
  'hover:text-gray-100': 'hover:text-semantic-gray-100',
  'hover:text-gray-200': 'hover:text-semantic-gray-200',
  'hover:text-gray-300': 'hover:text-semantic-gray-300',
  'hover:text-gray-400': 'hover:text-semantic-gray-400',
  'hover:text-gray-500': 'hover:text-semantic-gray-500',
  'hover:text-gray-600': 'hover:text-semantic-gray-600',
  'hover:text-gray-700': 'hover:text-semantic-gray-700',
  'hover:text-gray-800': 'hover:text-semantic-gray-800',
  'hover:text-gray-900': 'hover:text-semantic-gray-900',
  
  'hover:bg-gray-50': 'hover:bg-semantic-gray-50',
  'hover:bg-gray-100': 'hover:bg-semantic-gray-100',
  'hover:bg-gray-200': 'hover:bg-semantic-gray-200',
  'hover:bg-gray-300': 'hover:bg-semantic-gray-300',
  'hover:bg-gray-400': 'hover:bg-semantic-gray-400',
  'hover:bg-gray-500': 'hover:bg-semantic-gray-500',
  'hover:bg-gray-600': 'hover:bg-semantic-gray-600',
  'hover:bg-gray-700': 'hover:bg-semantic-gray-700',
  'hover:bg-gray-800': 'hover:bg-semantic-gray-800',
  'hover:bg-gray-900': 'hover:bg-semantic-gray-900',
  
  'hover:border-gray-50': 'hover:border-semantic-gray-50',
  'hover:border-gray-100': 'hover:border-semantic-gray-100',
  'hover:border-gray-200': 'hover:border-semantic-gray-200',
  'hover:border-gray-300': 'hover:border-semantic-gray-300',
  'hover:border-gray-400': 'hover:border-semantic-gray-400',
  'hover:border-gray-500': 'hover:border-semantic-gray-500',
  'hover:border-gray-600': 'hover:border-semantic-gray-600',
  'hover:border-gray-700': 'hover:border-semantic-gray-700',
  'hover:border-gray-800': 'hover:border-semantic-gray-800',
  'hover:border-gray-900': 'hover:border-semantic-gray-900',
};

// Hex color mappings to HSL theme variables
const hexColorMappings = {
  '#F9FAFB': 'hsl(var(--semantic-gray-50))',
  '#F3F4F6': 'hsl(var(--semantic-gray-100))',
  '#E5E7EB': 'hsl(var(--semantic-gray-200))',
  '#D1D5DB': 'hsl(var(--semantic-gray-300))',
  '#9CA3AF': 'hsl(var(--semantic-gray-400))',
  '#6B7280': 'hsl(var(--semantic-gray-500))',
  '#4B5563': 'hsl(var(--semantic-gray-600))',
  '#374151': 'hsl(var(--semantic-gray-700))',
  '#1F2937': 'hsl(var(--semantic-gray-800))',
  '#111827': 'hsl(var(--semantic-gray-900))',
  '#2A3439': 'hsl(var(--semantic-gray-700))', // Custom gray used in codebase
  '#e0e0e0': 'hsl(var(--semantic-gray-300))', // Gutter background
  '#231f20': 'hsl(var(--semantic-gray-900))', // Dark theme background
};

// Additional complex patterns to replace
const complexPatterns = [
  // Focus ring patterns
  {
    pattern: /focus:ring-gray-(\d+)/g,
    replacement: 'focus:ring-semantic-gray-$1'
  },
  // Ring offset patterns
  {
    pattern: /ring-gray-(\d+)/g,
    replacement: 'ring-semantic-gray-$1'
  },
  // Placeholder patterns
  {
    pattern: /placeholder-gray-(\d+)/g,
    replacement: 'placeholder-semantic-gray-$1'
  },
  // Divide patterns
  {
    pattern: /divide-gray-(\d+)/g,
    replacement: 'divide-semantic-gray-$1'
  },
  // From/to gradient patterns
  {
    pattern: /from-gray-(\d+)/g,
    replacement: 'from-semantic-gray-$1'
  },
  {
    pattern: /to-gray-(\d+)/g,
    replacement: 'to-semantic-gray-$1'
  },
  {
    pattern: /via-gray-(\d+)/g,
    replacement: 'via-semantic-gray-$1'
  },
  // Shadow patterns
  {
    pattern: /shadow-gray-(\d+)/g,
    replacement: 'shadow-semantic-gray-$1'
  },
  // Decoration patterns
  {
    pattern: /decoration-gray-(\d+)/g,
    replacement: 'decoration-semantic-gray-$1'
  },
  // Outline patterns
  {
    pattern: /outline-gray-(\d+)/g,
    replacement: 'outline-semantic-gray-$1'
  }
];

async function getFiles(dir) {
  const subdirs = await readdir(dir);
  const files = await Promise.all(
    subdirs.map(async (subdir) => {
      const res = path.resolve(dir, subdir);
      return (await stat(res)).isDirectory() ? getFiles(res) : res;
    })
  );
  return files
    .flat()
    .filter((file) => 
      extensions.includes(path.extname(file)) && 
      !file.includes('node_modules') && 
      !file.includes('.next') &&
      !file.includes('replace-hardcoded-gray-colors.js') // Exclude this script
    );
}

function replaceGrayColors(content, filePath) {
  let modifiedContent = content;
  let replacementCount = 0;
  const replacements = [];

  // Replace Tailwind gray classes
  for (const [oldClass, newClass] of Object.entries(grayColorMappings)) {
    const regex = new RegExp(`\\b${oldClass.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'g');
    const matches = modifiedContent.match(regex);
    if (matches) {
      modifiedContent = modifiedContent.replace(regex, newClass);
      replacementCount += matches.length;
      replacements.push(`${oldClass} → ${newClass} (${matches.length} times)`);
    }
  }

  // Replace complex patterns
  for (const { pattern, replacement } of complexPatterns) {
    const matches = modifiedContent.match(pattern);
    if (matches) {
      const oldContent = modifiedContent;
      modifiedContent = modifiedContent.replace(pattern, replacement);
      replacementCount += matches.length;
      replacements.push(`Pattern ${pattern.source} → ${replacement} (${matches.length} times)`);
    }
  }

  // Replace hex colors
  for (const [hexColor, themeVar] of Object.entries(hexColorMappings)) {
    const regex = new RegExp(hexColor.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
    const matches = modifiedContent.match(regex);
    if (matches) {
      modifiedContent = modifiedContent.replace(regex, themeVar);
      replacementCount += matches.length;
      replacements.push(`${hexColor} → ${themeVar} (${matches.length} times)`);
    }
  }

  // Replace CSS color properties with hardcoded grays
  const cssColorPatterns = [
    {
      pattern: /color:\s*#([0-9A-Fa-f]{6}|[0-9A-Fa-f]{3})\s*;/g,
      check: (match) => {
        const color = match.match(/#([0-9A-Fa-f]{6}|[0-9A-Fa-f]{3})/)[0].toUpperCase();
        return hexColorMappings[color];
      }
    },
    {
      pattern: /background-color:\s*#([0-9A-Fa-f]{6}|[0-9A-Fa-f]{3})\s*;/g,
      check: (match) => {
        const color = match.match(/#([0-9A-Fa-f]{6}|[0-9A-Fa-f]{3})/)[0].toUpperCase();
        return hexColorMappings[color];
      }
    },
    {
      pattern: /border-color:\s*#([0-9A-Fa-f]{6}|[0-9A-Fa-f]{3})\s*;/g,
      check: (match) => {
        const color = match.match(/#([0-9A-Fa-f]{6}|[0-9A-Fa-f]{3})/)[0].toUpperCase();
        return hexColorMappings[color];
      }
    }
  ];

  for (const { pattern, check } of cssColorPatterns) {
    const matches = modifiedContent.match(pattern);
    if (matches) {
      for (const match of matches) {
        const replacement = check(match);
        if (replacement) {
          const newMatch = match.replace(/#([0-9A-Fa-f]{6}|[0-9A-Fa-f]{3})/i, replacement);
          modifiedContent = modifiedContent.replace(match, newMatch);
          replacementCount++;
          replacements.push(`CSS: ${match} → ${newMatch}`);
        }
      }
    }
  }

  return {
    content: modifiedContent,
    replacementCount,
    replacements
  };
}

async function createBackup() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = `./backup-${timestamp}`;

  try {
    await fs.promises.mkdir(backupDir, { recursive: true });

    const files = await getFiles('./src');
    for (const file of files) {
      const relativePath = path.relative('./src', file);
      const backupPath = path.join(backupDir, 'src', relativePath);
      const backupDirPath = path.dirname(backupPath);

      await fs.promises.mkdir(backupDirPath, { recursive: true });
      await fs.promises.copyFile(file, backupPath);
    }

    console.log(`📦 Backup created: ${backupDir}`);
    return backupDir;
  } catch (error) {
    console.warn(`⚠️  Warning: Could not create backup: ${error.message}`);
    return null;
  }
}

async function main() {
  const dryRun = !process.argv.includes('--apply');
  const createBackupFlag = process.argv.includes('--backup');

  console.log('🎨 Gray Color Replacement Script');
  console.log('================================');
  console.log(`Mode: ${dryRun ? 'DRY RUN (preview only)' : 'APPLY CHANGES'}`);
  console.log('');

  try {
    // Create backup if requested and not in dry run mode
    if (!dryRun && createBackupFlag) {
      await createBackup();
      console.log('');
    }

    const files = await getFiles('./src');
    console.log(`📁 Found ${files.length} files to process\n`);

    let totalReplacements = 0;
    let totalFiles = 0;

    for (const file of files) {
      try {
        const content = await readFile(file, 'utf8');
        const result = replaceGrayColors(content, file);

        if (result.replacementCount > 0) {
          console.log(`📄 ${file}:`);
          console.log(`   ${result.replacementCount} replacements found`);

          if (!dryRun) {
            await writeFile(file, result.content);
            console.log(`   ✅ Changes applied`);
          } else {
            result.replacements.forEach(replacement => {
              console.log(`   - ${replacement}`);
            });
          }

          totalReplacements += result.replacementCount;
          totalFiles++;
          console.log('');
        }
      } catch (error) {
        console.warn(`⚠️  Warning: Could not process ${file}: ${error.message}`);
      }
    }

    console.log('📊 Summary:');
    console.log(`   Files modified: ${totalFiles}`);
    console.log(`   Total replacements: ${totalReplacements}`);

    if (dryRun) {
      console.log('\n💡 This was a dry run. To apply changes, run:');
      console.log('   node replace-hardcoded-gray-colors.js --apply');
      console.log('   Add --backup to create a backup before applying changes');
    } else {
      console.log('\n✅ Successfully replaced all hardcoded gray colors!');
      console.log('\n📝 Next steps:');
      console.log('   1. Test your application to ensure all colors display correctly');
      console.log('   2. Check that both light and dark themes work properly');
      console.log('   3. Run your build process to ensure no compilation errors');
      console.log('   4. Check the browser console for any missing CSS classes');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

function showHelp() {
  console.log('🎨 Gray Color Replacement Script');
  console.log('================================');
  console.log('');
  console.log('This script replaces hardcoded gray colors with theme variables.');
  console.log('');
  console.log('Usage:');
  console.log('  node replace-hardcoded-gray-colors.js [options]');
  console.log('');
  console.log('Options:');
  console.log('  --apply     Apply changes to files (default: dry run)');
  console.log('  --backup    Create backup before applying changes');
  console.log('  --help      Show this help message');
  console.log('');
  console.log('Examples:');
  console.log('  node replace-hardcoded-gray-colors.js                    # Preview changes');
  console.log('  node replace-hardcoded-gray-colors.js --apply            # Apply changes');
  console.log('  node replace-hardcoded-gray-colors.js --apply --backup   # Apply with backup');
  console.log('');
  console.log('What it replaces:');
  console.log('  • Tailwind gray classes (text-gray-500 → text-semantic-gray-500)');
  console.log('  • Hex gray colors (#6B7280 → hsl(var(--semantic-gray-500)))');
  console.log('  • CSS properties with gray colors');
  console.log('  • Complex Tailwind patterns (hover:, focus:, etc.)');
}

if (require.main === module) {
  if (process.argv.includes('--help')) {
    showHelp();
  } else {
    main();
  }
}

module.exports = { replaceGrayColors, grayColorMappings, hexColorMappings, complexPatterns };
