 importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
 
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

const firebaseConfig = {
    apiKey: "AIzaSyDp4qfUKz4feDl8T5OtUi-HpvYzhBggr4Y",
    authDomain: "kavia-467d8.firebaseapp.com",
    projectId: "kavia-467d8",
    storageBucket: "kavia-467d8.firebasestorage.app",
    messagingSenderId: "701815814656",
    appId: "1:701815814656:web:cf787f7c9988c9e03f36e5",
    measurementId: "G-50TD7RCF0W"
};

 
firebase.initializeApp(firebaseConfig);
 
const messaging = firebase.messaging();

messaging.onBackgroundMessage(function(payload) {
    self.clients.matchAll({ includeUncontrolled: true }).then((clients) => {
        if (clients && clients.length) {
           
          clients[0].postMessage({ type: 'NOTIFICATION_RECEIVED', payload });
        }
      });
    const notificationTitle = payload["data"]["pinpoint.notification.title"];
    const notificationOptions = {
        title: notificationTitle,
        body: payload["data"]["pinpoint.notification.body"],
        icon: '/logo/kavia_light_logo.svg',
        badge: '/logo/kavia_light_logo.svg', 
        tag: 'notification-' + Date.now() 
    };
    return self.registration.showNotification(notificationTitle, notificationOptions);
});