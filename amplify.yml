version: 1
frontend:
  phases:
    preBuild:
      commands:
        - nvm use 20
        - npm install --legacy-peer-deps
        - npm i canvas
        - npm i --save lodash
    build:
      commands:
        - NODE_OPTIONS="--max-old-space-size=8192" npm run build
        - rm -rf node_modules/canvas
  artifacts:
    baseDirectory: .next
    files:
      - '**/*'
  cache:
    paths:
      - .next/cache/**/*
      - .npm/**/*
      - node_modules/**/*
