import { default as withBundleAnalyzer } from '@next/bundle-analyzer';

const withBundleAnalyzerConfig = withBundleAnalyzer({
  enabled: process.env.ANALYZE === 'true',
});

/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config) => {
    config.resolve.alias.canvas = false;
    config.resolve.fallback = {
      ...config.resolve.fallback,
      canvas: false,
    };
    return config;
  },
  headers: async () => [
    {
      source: '/home',
      headers: [
        {
          key: 'Cache-Control',
          value: 'no-store, no-cache, must-revalidate, proxy-revalidate',
        },
        {
          key: 'Pragma',
          value: 'no-cache',
        },
        {
          key: 'Expires',
          value: '0',
        },
      ],
    },
  ],
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: process.env.NEXT_PUBLIC_API_URL + '/:path*',
      },
    ];
  },
  compress: true,
  swcMinify: true,
  optimizeFonts: true,
  images: {
    dangerouslyAllowSVG: true,
    remotePatterns: [
      'figma-alpha-api.s3.us-west-2.amazonaws.com',
      'via.placeholder.com',
      'ui-avatars.com',
    ],
    remotePatterns: [
      {
        protocol: 'http',
        hostname: 'i.ibb.co',
      },
      {
        protocol: 'https',
        hostname: 'i.ibb.co',
      },
      {
        protocol: 'http',
        hostname: 'ui-avatars.com',
      },
      {
        protocol: 'https',
        hostname: 'ui-avatars.com',
      },
      {
        protocol: 'https',
        hostname: '*',
      },
    ],
  },
    compiler: {
      removeConsole: { exclude: ['error'] },
    },
  reactStrictMode: false,
};

export default withBundleAnalyzerConfig(nextConfig);
