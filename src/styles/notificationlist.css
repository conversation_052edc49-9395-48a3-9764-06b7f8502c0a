.notification-container {
  @apply flex flex-col h-full bg-custom-bg-primary;
  }
  .notification-scroll-area {
  @apply overflow-y-auto flex-grow;
  }
  /* Date header */
  .notification-date-header {
  @apply font-medium text-custom-text-secondary capitalize text-sm;
  }
  /* Group container */
  .notification-group {
  @apply p-4 pb-0 space-y-3;
  }
  /* Notification item */
  /* Notification content layout */
  /* Bell icon container */
  /* Unread indicator dot */
  /* Text content */
  /* Delete button */
  /* Footer */
  .notification-footer {
  @apply flex justify-center items-center py-2 bg-custom-bg-primary border-t border-custom-border hover:bg-custom-bg-secondary;
  }
  .mark-all-read-button {
  @apply font-medium text-custom-text-secondary text-center cursor-pointer hover:text-primary;
  }
  /* Clear all button */
  .clear-all-button {
  @apply absolute bottom-4 right-4 bg-custom-bg-secondary hover:bg-custom-bg-muted text-custom-text-primary font-medium py-2 px-3 rounded-lg shadow-sm transition-colors duration-200 flex items-center space-x-2 text-sm border border-custom-border;
  }
  .clear-all-icon {
  @apply w-4 h-4 transition-transform duration-200 ease-in-out group-hover:rotate-90;
  }