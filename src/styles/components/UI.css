/* search-container */
.search-container {
    @apply sticky top-0 z-10 border border-primary-200 px-4 py-2 flex items-center;
  }
  .search-icon {
    @apply absolute left-6 top-1/2 -translate-y-1/2 text-primary-400 pointer-events-none;
  }
  .search-icon-without-container {
    @apply absolute left-3 top-1/2 -translate-y-1/2 text-primary-400 pointer-events-none;
  }
  .search-custom-input::placeholder {
    @apply text-primary-400;
  }
  /* IconButton */
  .icon-button {
    @apply flex items-center gap-2 rounded-md transition-colors duration-150 ease-in-out hover:bg-primary-100;
  }
  /* Secondary Tab */
.secondary-tab {
  @apply text-custom-md font-semibold flex items-center justify-center px-3 py-2 cursor-pointer transition-transform duration-150 ease-in-out;
  white-space: nowrap;
  flex-shrink: 0;
}
.active-tab {
  @apply rounded-custom-md shadow-[0.059375rem_0.059375rem_0.1625rem_hsl(var(--semantic-gray-900)/0.15)] border border-custom-border bg-white text-semantic-gray-900;
}
.inactive-tab {
  @apply bg-custom-bg-secondary rounded-custom-md text-custom-text-secondary;
}