@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Toaster Container */
  .toaster-container {
    position: fixed;
    top: 1.5rem;
    right: 1.5rem;
    z-index: 9999;
    pointer-events: none;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .toaster-container > * {
    pointer-events: auto;
  }

  /* Base Toaster Styles - Thin and Vibrant */
  .toaster {
    min-width: 20rem !important;
    max-width: 24rem !important;
    padding: 0.875rem 1rem !important;
    border-radius: 0.5rem !important;
    font-family: 'Inter', sans-serif !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1) !important;
    transform: translateX(0) !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 10000 !important;
  }

  /* Toaster Animation States */
  .toaster-enter {
    opacity: 0;
    transform: translateX(100%);
  }

  .toaster-exit {
    opacity: 0;
    transform: translateX(100%);
  }

  .toaster:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 12px 35px -8px hsl(var(--semantic-gray-900) / 0.25),
                0 8px 20px -4px hsl(var(--semantic-gray-900) / 0.15),
                0 0 0 1px hsl(var(--semantic-gray-900) / 0.08) inset;
  }

  /* Toaster Content Layout - Thin Design */
  .toaster-content {
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
  }

  .toaster-icon {
    flex-shrink: 0 !important;
    width: 1.125rem !important;
    height: 1.125rem !important;
  }

  .toaster-body {
    flex: 1 !important;
    min-width: 0 !important;
  }

  .toaster-message {
    font-weight: 500 !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    margin: 0 !important;
  }

  .toaster-close {
    flex-shrink: 0 !important;
    width: 1.25rem !important;
    height: 1.25rem !important;
    border-radius: 0.25rem !important;
    border: none !important;
    background: transparent !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.2s ease-in-out !important;
    color: white !important;
  }

  .toaster-close:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
  }

  .toaster-close:focus {
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5) !important;
  }

  /* Vibrant Toaster Variants - Thin and Bright */
  div.toaster.toaster-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
    border: 2px solid #047857 !important;
    color: white !important;
    box-shadow: 0 8px 25px -5px rgba(16, 185, 129, 0.6),
                0 0 0 1px rgba(5, 150, 105, 0.3) inset !important;
  }

  div.toaster.toaster-error,
  div.toaster.toaster-danger {
    background: linear-gradient(135deg, #f43f5e 0%, #e11d48 100%) !important;
    border: 2px solid #be123c !important;
    color: white !important;
    box-shadow: 0 8px 25px -5px rgba(244, 63, 94, 0.6),
                0 0 0 1px rgba(225, 29, 72, 0.3) inset !important;
  }

  div.toaster.toaster-warning {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%) !important;
    border: 2px solid #d97706 !important;
    color: white !important;
    box-shadow: 0 8px 25px -5px rgba(251, 191, 36, 0.6),
                0 0 0 1px rgba(245, 158, 11, 0.3) inset !important;
  }

  div.toaster.toaster-info,
  div.toaster.toaster-default {
    background: linear-gradient(135deg, #fb923c 0%, #f97316 100%) !important;
    border: 2px solid #ea580c !important;
    color: white !important;
    box-shadow: 0 8px 25px -5px rgba(251, 146, 60, 0.6),
                0 0 0 1px rgba(249, 115, 22, 0.3) inset !important;
  }

  /* Common styling for all variants - Thin design */
  div.toaster .toaster-icon {
    color: white !important;
  }

  div.toaster .toaster-message {
    color: white !important;
    opacity: 1 !important;
    font-weight: 500 !important;
  }

  div.toaster .toaster-close {
    color: white !important;
  }

  div.toaster .toaster-close:hover {
    background-color: rgba(255, 255, 255, 0.25) !important;
  }





  /* Responsive Design */
  @media (max-width: 640px) {
    .toaster-container {
      top: 1rem;
      right: 1rem;
      left: 1rem;
    }

    .toaster {
      min-width: auto;
      max-width: none;
      width: 100%;
    }
  }

  /* Accessibility */
  @media (prefers-reduced-motion: reduce) {
    .toaster {
      transition: none;
    }

    .toaster:hover {
      transform: none;
    }
  }
}
