/* Styles for sticky action buttons */
/* Ensure buttons don't get too small on smaller screens */
@media (max-width: 768px) {
  /* Make sure the Auto Configure button is always visible */
  .sticky-action-buttons button {
    white-space: nowrap;
    min-width: fit-content;
  }
}
/* Add a subtle gradient effect to indicate there's more content */
@media (min-width: 769px) {
  .sticky-action-buttons::before {
    content: '';
    position: absolute;
    left: -20px;
    top: 0;
    height: 100%;
    width: 20px;
    background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
  }
}
/* Architecture layout responsive improvements */
.architecture-layout .flex.items-center.justify-between {
  flex-wrap: nowrap;
}
@media (max-width: 768px) {
  .architecture-layout .flex.items-center.justify-between {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
  .architecture-layout .flex.items-center.justify-between > div:last-child {
    align-self: flex-end;
  }
}