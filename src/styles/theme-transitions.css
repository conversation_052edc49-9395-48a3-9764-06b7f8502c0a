/* Theme transition styles */
.theme-transition {
  transition-property: background-color, color, border-color, fill, stroke, opacity, box-shadow, transform;
  transition-duration: 0.2s;
  transition-timing-function: ease-in-out;
  will-change: background-color, color, border-color, fill, stroke, opacity, box-shadow, transform;
}

/* Apply transitions only to designated theme elements */
.theme-transition .theme-element {
  transition-property: background-color, color, border-color, fill, stroke, opacity, box-shadow, transform;
  transition-duration: 0.2s;
  transition-timing-function: ease-in-out;
  will-change: background-color, color, border-color, fill, stroke, opacity, box-shadow, transform;
}

/* Fade-in animation for page transitions */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(6px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.4s ease-out forwards;
  will-change: opacity, transform;
}

/* Specific transitions for home page after login */
.home-page-transition {
  transition: all 0.2s cubic-bezier(0.16, 1, 0.3, 1);
  will-change: transform, opacity;
}

.home-page-transition-content {
  opacity: 0;
  transform: translateY(6px);
  animation: fadeIn 0.2s ease-out 0.05s forwards;
  will-change: opacity, transform;
}

/* Gradient transition effects */
.gradient-transition {
  transition: background-image 0.8s ease-in-out;
  background-size: 200% 200%;
  animation: gradientPosition 2s ease infinite;
  will-change: background-image;
}

@keyframes gradientPosition {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Content transition for architecture interfaces */
.architecture-content-transition {
  transition: all 0.2s ease-in-out;
  will-change: opacity, transform, height;
  min-height: 74vh;
  opacity: 1;
}

.architecture-content-transition.loading {
  opacity: 0.8;
}
