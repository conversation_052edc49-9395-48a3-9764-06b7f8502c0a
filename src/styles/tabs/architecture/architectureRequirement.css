/* architectureRequirement page alignment starts */
/* .architectureRequirementContainer{ */
/* .architectureRequirementMainTab{ */
/* .architectureRequirementHeader { */
.architecture-requirement-related-child-nodes {
    @apply mt-3
}
/* architectureRequirement css alignment ends */
/* architecture requirements child node css alignments start */
/* .architecture-requirement-child-node-outer-border {
    @apply border rounded-lg p-4 shadow-lg
} */
.architecture-requirement-child-node-description-wrapper {
    @apply mt-2
}
.architecture-requirement-child-node-description {
    @apply mt-1
}
.architecture-requirement-child-node-related-node {
    @apply mt-8
}
/* architecture requirements child node css alignments ends */