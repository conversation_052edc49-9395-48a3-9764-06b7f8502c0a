/* design tab css alignment start */
.design-tab-data-not-found {
    @apply text-center flex justify-center  items-center mt-4 overflow-hidden
}
/* low level design table css alignment start */
/* low level design table css alignment ends */
/* full screen loder css for generate code tab starts*/
.loader-overlay{
    @apply fixed inset-0 bg-semantic-gray-900 bg-opacity-5 flex items-center justify-center z-50
}
.loader-container {
    @apply bg-custom-bg-primary p-5 rounded-lg shadow-lg w-[97%] h-[93%]
}
.loader-content{
    @apply px-4 py-2 text-center list-none h-32 mt-9 flex flex-col justify-center items-center h-full
}
.loader-spinner{
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-semantic-gray-900 mb-3
}
.loader-text {
    @apply text-center
}
/* full screen loder css for generate code tab ends */
/* design tab css alignment end*/