/* architecture tab header alignment starts */
/* .systemContextContentWrapper { */
/* .systemContextSubContainer { */
/* .systemContextOuterBorder { */
/* .systemContextHeader { */
.badge {
    @apply py-1 ml-2
}
.buttons {
    @apply flex items-center justify-end flex-shrink-0
}
.relatedComponentDiv {
    /* @apply mt-8 mx-5 */
}
.notFound {
    @apply flex items-center justify-center -mt-[5px]
}
/* system context empty state */
.system-context-empty-state {
    @apply flex items-center justify-center h-full w-full
}