/* Professional Requirements Page Styling - Ultra Compact */
.requirementContainerWrapper {
    @apply flex flex-col justify-between min-h-[60vh] max-h-[78vh] overflow-y-auto bg-white;
    font-family: 'Inter', sans-serif;
}

.requirementSearchTab {
    @apply mt-2 mb-3;
}

.requirementTableMainDiv {
    @apply border border-semantic-gray-200 rounded-lg shadow-custom-card bg-white;
    transition: box-shadow 0.2s ease-in-out;
}

.requirementTableMainDiv:hover {
    @apply shadow-custom-hover;
}

/* Requirements child item page alignments */
.requirementDetailsTabHeaderDiv {
    @apply flex items-center justify-between w-full border-b border-semantic-gray-200 top-0 left-0 sticky py-3;
    background-color: hsl(var(--background) / 0.95);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
}

.arrowIconsDiv {
    @apply flex gap-2;
}

.headerTabButtons {
    @apply flex relative gap-3 z-10;
}

.mainContentDetailsWrapper {
    @apply rounded-lg overflow-auto max-h-[70vh] border border-semantic-gray-200;
}

.typeBadge {
    @apply py-0.5 px-1 rounded text-xs font-medium w-fit flex justify-center items-center gap-0.5 border transition-all duration-200 hover:shadow-sm;
}

/* Enhanced table styling for better readability - Compact Design */
.requirements-table-wrapper {
    @apply bg-white rounded-lg border border-semantic-gray-200 shadow-custom-card overflow-hidden;
}

.requirements-table-header {
    @apply bg-semantic-gray-50 border-b border-semantic-gray-200;
}

.requirements-table-row {
    @apply transition-colors duration-150 hover:bg-semantic-gray-50;
}

.requirements-table-cell {
    @apply px-2 py-1.5 text-sm font-normal text-semantic-gray-900;
}

.requirements-table-header-cell {
    @apply px-2 py-1.5 text-xs font-semibold text-semantic-gray-600 uppercase tracking-wide;
}

/* Search input improvements */
.requirements-search-input {
    @apply w-full px-4 py-2.5 text-base font-normal text-semantic-gray-900 bg-white border border-semantic-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-colors placeholder:text-semantic-gray-500;
    font-family: 'Inter', sans-serif;
}

/* Badge improvements - Ultra Compact Design */
.priority-badge {
    @apply inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium border transition-all duration-200;
}

.status-badge {
    @apply inline-flex items-center px-1 py-0.5 rounded text-xs font-medium border transition-all duration-200;
}

.type-badge {
    @apply inline-flex items-center px-1 py-0.5 rounded text-xs font-medium border transition-all duration-200;
}
.typeBadgeIcon {
    @apply -mt-[0.5px] w-2 h-2 ml-2
}
.priorityBadge {
    @apply py-0.5 rounded-md text-sm font-semibold  w-[60px] flex justify-center items-center
}
.priorityBadgeChevron {
    @apply cursor-pointer ml-2
}