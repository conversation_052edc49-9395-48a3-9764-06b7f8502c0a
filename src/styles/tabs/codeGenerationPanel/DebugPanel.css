@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Main Container */
  .debug-panel {
    @apply flex flex-col h-full bg-semantic-gray-50 max-h-[80vh];
  }

  /* Header */
  .debug-header {
    @apply flex flex-col bg-custom-bg-primary border-b border-custom-border;
  }

  .debug-header-content {
    @apply flex items-center justify-between px-4 py-2;
  }

  .debug-title-section {
    @apply flex-col items-center space-x-2;
  }

  .debug-title {
    @apply font-medium;
  }

  /* Controls Section */
  .debug-controls {
    @apply flex items-center space-x-2;
  }

  .debug-type-filters {
    @apply px-4 py-1 border-t flex flex-wrap gap-2;
  }

  .debug-type-button {
    @apply px-2 py-0.5 rounded-full text-xs transition-colors;
  }

  .debug-type-button-inactive {
    @apply bg-semantic-gray-100 text-semantic-gray-600 hover:bg-semantic-gray-200;
  }

  .debug-search {
    @apply w-[200px];
  }

  /* Messages Container */
  .debug-messages {
    @apply flex-1 h-[80vh] max-h-[74vh];
  }

  .debug-messages-content {
    @apply p-4 space-y-2;
  }

  /* Message Item */
  .debug-message {
    @apply  text-sm bg-white rounded p-2 transition-colors border border-gray-100 relative;
  }

  .debug-message:hover {
    @apply border-gray-300;
  }

  .debug-message-header {
    @apply flex items-center justify-between;
  }

  .debug-message-meta {
    @apply flex items-center space-x-2;
  }

  .debug-message-time {
    @apply text-gray-500 text-xs;
  }

  .debug-message-content {
    @apply mt-1 text-gray-700 whitespace-pre-wrap break-words overflow-x-auto;
  }

  .debug-message-copy {
    @apply opacity-0 p-1 hover:bg-gray-100 rounded transition-opacity;
  }

  /* Empty States */
  .debug-empty-state {
    @apply flex items-center justify-center text-center;
  }

  .debug-no-results {
    @apply text-gray-500;
  }

  /* Footer */
  .debug-footer {
    @apply px-2 py-0.5 bg-white border-t -mb-1 flex items-center justify-between;
  }

  .debug-message-count {
    @apply text-xs text-gray-600;
  }

  .debug-footer-controls {
    @apply flex items-center space-x-2;
  }

  .debug-scroll-button {
    @apply w-8 h-8 flex items-center justify-center transition-colors duration-200
           hover:bg-gray-100 rounded;
  }

  .debug-scroll-icon {
    @apply text-gray-500 hover:text-gray-600;
  }

  .debug-clear-button {
    @apply w-8 h-8 flex items-center justify-center hover:bg-red-50 rounded transition-colors;
  }

  .debug-clear-icon {
    @apply w-5 h-5 text-red-500 hover:text-red-600;
  }

  /* Message Types */
  .type-agent_message {
    @apply text-primary-500 bg-primary-500/10;
  }

  .type-terminal_output {
    @apply text-terminal-green bg-terminal-green/10;
  }

  .type-function_call {
    @apply text-terminal-purple bg-terminal-purple/10;
  }

  .type-browser_output {
    @apply text-terminal-yellow bg-terminal-yellow/10;
  }

  .type-progress_update {
    @apply text-terminal-cyan bg-terminal-cyan/10;
  }

  .type-cost_update {
    @apply text-primary-500 bg-primary-500/10;
  }

  .type-status_update {
    @apply text-terminal-red bg-terminal-red/10;
  }

  .type-file_watch {
    @apply text-terminal-green bg-terminal-green/10;
  }

  /* Connection Status */
  .status-icon {
    @apply w-4 h-4;
  }

  .status-icon-connected {
    @apply text-terminal-green;
  }

  .status-icon-connecting {
    @apply text-yellow-500 animate-spin;
  }

  .status-icon-error {
    @apply text-red-500;
  }

  .status-text {
    @apply text-sm text-gray-600 capitalize;
  }
}