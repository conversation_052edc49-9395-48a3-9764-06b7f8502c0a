@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Container Styles */
  .code-container {
    @apply relative bg-custom-bg-primary overflow-hidden;
    height: 80vh;
    max-height: 100%;
    max-width: 100%;
  }

  /* IFrame Styles */
  .code-iframe {
    @apply border-none;
    width: 100%;
    height: 100%;
    background: hsl(var(--background));
  }

  /* Loading and Error States */
  .code-loading-overlay {
    @apply absolute inset-0 bg-custom-bg-primary flex items-center justify-center;
    z-index: 15;
  }

  .code-error-state {
    @apply absolute inset-0 bg-custom-bg-primary flex items-center justify-center;
    z-index: 25;
  }

  /* Refresh Button */
  .code-refresh-button {
    @apply absolute top-4 right-4 p-2 bg-custom-bg-primary shadow-md rounded-md border border-custom-border;
    @apply hover:bg-custom-bg-secondary transition-colors duration-200;
    z-index: 30;
  }

  .code-refresh-button:disabled {
    @apply opacity-50 cursor-not-allowed;
  }

  /* Animations */
  .code-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .code-container {
      height: 70vh;
    }

    .code-refresh-button {
      @apply top-2 right-2 p-1.5;
    }
  }
}