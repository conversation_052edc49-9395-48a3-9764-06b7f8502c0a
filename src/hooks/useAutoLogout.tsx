"use client";

import { useContext } from "react";
import { useRouter } from "next/navigation";
import { TopBarContext } from "@/components/Context/TopBarContext";

export default function useAuth() {
  const { clearTabsFromLocalStorage } = useContext(TopBarContext);
  const router = useRouter();

  const deleteAllCookies = () => {
    document.cookie.split(";").forEach((cookie) => {
      const name = cookie.split("=")[0].trim();
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
    });
  };

  const handleLogout = () => {
    setTimeout(() => {
      deleteAllCookies();
      clearTabsFromLocalStorage();
      if (typeof window !== 'undefined') {
        sessionStorage.removeItem('tabs');
      }
      router.push("/users/login");
    }, 100);
  };

  return { handleLogout };
}
