'use client';

import { useEffect, useState, useCallback } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useUser } from '@/components/Context/UserContext';

export const LOCKED_TABS = [
  'requirements',
  'architecture',
  'ui_ux',
  'documents',
  'testcase',
  'test_execution',
  'deployment'
];


const FreePlanRestriction = ({ children }) => {
  const { is_free_user } = useUser();
  const pathname = usePathname();
  const router = useRouter();
  const [isAllowed, setIsAllowed] = useState(true);
  const [isChecking, setIsChecking] = useState(true);

  const checkIfLockedTab = useCallback(() => {
    if (!is_free_user || !pathname) return false;

    const isProjectRoute = pathname.startsWith('/project/');
    if (!isProjectRoute) return false;

    const pathParts = pathname.split('/');
    const projectSection = pathParts.length > 3 ? pathParts[3] : '';

    return LOCKED_TABS.some(tab =>
      projectSection === tab || projectSection.startsWith(tab + '/')
    );
  }, [is_free_user, pathname]);

  const getProjectId = useCallback(() => {
    const pathParts = pathname.split('/');
    return pathParts.length > 3 ? pathParts[3] : '';
  }, [pathname]);

  useEffect(() => {
    setIsChecking(true);
    setIsAllowed(true);

    if (is_free_user === null) {
      setIsChecking(false);
      return;
    }

    const isLockedTab = checkIfLockedTab();
    if (isLockedTab) {
      setIsAllowed(false);
      const projectId = getProjectId();
      const { buildProjectUrl } = require('@/utils/navigationHelpers');
      const currentPath = window.location.pathname;
      const pathParts = currentPath.split('/');
      const organizationId = pathParts[1];
      const type = pathParts[2];
      router.replace(buildProjectUrl(projectId, 'overview', type, organizationId));

      setTimeout(() => {
        setIsChecking(false);
      }, 100);
    } else {
      setIsChecking(false);
    }
  }, [pathname, is_free_user, router, checkIfLockedTab, getProjectId]);


  if (!isAllowed) {
    return <div className="relative h-full w-full flex-grow overflow-auto"></div>;
  }

  return (
    <div className="relative h-full w-full flex-grow overflow-auto">
      {children}
    </div>
  );
};

export default FreePlanRestriction;
