import React from 'react'

const ConnectionIndicator = React.forwardRef(({ isConnected }, ref) => {
  return (
    <div ref={ref} className="relative">
      <div className={`w-2.5 h-2.5 rounded-full ${isConnected ? 'animate-ping bg-green-500' : 'bg-red-500'}`}></div>
      <div className={`w-2.5 h-2.5 rounded-full absolute top-0 left-0 ${isConnected ? 'bg-green-500': 'bg-red-500' }`}></div>
    </div>
  );
});

ConnectionIndicator.displayName = 'ConnectionIndicator';

export default ConnectionIndicator