"use client";
import React, { useEffect, useState, useRef, useCallback } from "react";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import { Loading2 } from "../../Loaders/Loading";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import ErrorView from "@/components/Modal/ErrorViewModal";
import "@/styles/tabs/codeGenerationPanel/TerminalPanel.css"
const SCROLL_THRESHOLD = 20;
const MAX_CACHED_MESSAGES = 5;
const INITIAL_SCROLL_DELAY = 100;

const TerminalContent = React.memo(({ content, terminalRef, onScroll }) => (
  <div
    ref={terminalRef}
    onScroll={onScroll}
    className="terminal-container"
  >
    <div
      className="terminal-content"
      dangerouslySetInnerHTML={{ __html: content }}
    />
    <div className="terminal-cursor">▋</div>
  </div>
));

TerminalContent.displayName = 'TerminalContent';

const TerminalPanel = () => {
  const {
    terminalOutput,
    historicalTerminalOutput,
    fetchHistoricalTerminalOutput,
    error,
  } = useCodeGeneration();

  const [displayedText, setDisplayedText] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  
  const terminalRef = useRef(null);
  const shouldAutoScrollRef = useRef(true);
  const previousLengthRef = useRef(0);
  const lastMessagesRef = useRef([]);
  const initialLoadDoneRef = useRef(false);
  const initialScrollDoneRef = useRef(false);

  const normalizeText = useCallback((text) => {
    if (!text) return "";
    return text
      .replace(/\r\n/g, "\n")
      .replace(/\n{3,}/g, "\n\n")
      .trim()
      .split("\n")
      .filter(Boolean)
      .map(line => {
        const indent = line.match(/^\s*/)[0];
        return indent + line.trim();
      })
      .join("\n");
  }, []);

  const formatTerminalLine = useCallback((text) => {
    if (!text) return "";
    const timestamp = new Date().toISOString().split("T")[1].split(".")[0];
    return `<span class="terminal-prefix">kavia-ai@terminal:[${timestamp}]$</span> <span class="terminal-text">${text}</span>`;
  }, []);

  const scrollToBottom = useCallback((force = false) => {
    if (!terminalRef.current || (!shouldAutoScrollRef.current && !force)) return;

    const scrollToEnd = () => {
      if (terminalRef.current) {
        const { scrollHeight, clientHeight } = terminalRef.current;
        terminalRef.current.scrollTop = scrollHeight - clientHeight;
      }
    };

    scrollToEnd();
    requestAnimationFrame(scrollToEnd);
    setTimeout(scrollToEnd, 50);
  }, []);

  useEffect(() => {
    const loadData = async () => {
      try {
        await fetchHistoricalTerminalOutput();
      } finally {
        setIsLoading(false);
        initialLoadDoneRef.current = true;
        setTimeout(() => {
          scrollToBottom(true);
          initialScrollDoneRef.current = true;
        }, INITIAL_SCROLL_DELAY);
      }
    };
    
    loadData();
  }, [fetchHistoricalTerminalOutput, scrollToBottom]);

  useEffect(() => {
    if (terminalOutput?.length > 0 && initialScrollDoneRef.current) {
      scrollToBottom(true);
    }
  }, [terminalOutput, scrollToBottom]);

  const allTerminalOutput = React.useMemo(() => {
    const historical = historicalTerminalOutput || [];
    const live = terminalOutput || [];

    const formatOutput = (output) => ({
      output: typeof output === "string" ? output : output.output,
      raw: typeof output === "string" ? output : output.output,
      timestamp: new Date().getTime(),
    });

    return [
      ...historical.map(formatOutput),
      ...live
        .filter(msg => {
          const isDuplicate = lastMessagesRef.current.some(
            lastMsg => normalizeText(lastMsg.raw || lastMsg.output) === normalizeText(msg.raw || msg.output)
          );
          if (!isDuplicate) {
            lastMessagesRef.current.push(msg);
            if (lastMessagesRef.current.length > MAX_CACHED_MESSAGES) {
              lastMessagesRef.current.shift();
            }
            return true;
          }
          return false;
        })
        .map(formatOutput),
    ];
  }, [historicalTerminalOutput, terminalOutput, normalizeText]);

  useEffect(() => {
    if (!allTerminalOutput?.length) return;

    const processNewEntries = () => {
      const entries = allTerminalOutput.slice(previousLengthRef.current);
      if (entries.length === 0) return;

      const newHtml = entries
        .map(entry => formatTerminalLine(normalizeText(entry.raw || entry.output)))
        .join("\n");

      setDisplayedText(prev => {
        const separator = prev ? "\n" : "";
        return `${prev}${separator}${newHtml}`;
      });

      previousLengthRef.current = allTerminalOutput.length;
    };

    processNewEntries();
  }, [allTerminalOutput, formatTerminalLine, normalizeText]);

  const handleScroll = useCallback(() => {
    if (!terminalRef.current || !initialLoadDoneRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = terminalRef.current;
    const scrollBottom = scrollHeight - clientHeight;
    const isAtBottom = Math.abs(scrollBottom - scrollTop) < SCROLL_THRESHOLD;
    
    shouldAutoScrollRef.current = isAtBottom;
  }, []);

  useEffect(() => {
    if (displayedText && !initialScrollDoneRef.current) {
      setTimeout(() => {
        scrollToBottom(true);
        initialScrollDoneRef.current = true;
      }, INITIAL_SCROLL_DELAY);
    }
  }, [displayedText, scrollToBottom]);

  useEffect(() => {
    if (!terminalRef.current) return;

    const observer = new ResizeObserver(() => {
      if (shouldAutoScrollRef.current || !initialScrollDoneRef.current) {
        scrollToBottom(true);
      }
    });

    observer.observe(terminalRef.current);
    return () => observer.disconnect();
  }, [scrollToBottom]);

  if (isLoading) return <Loading2 />;

  if (error && !allTerminalOutput?.length) {
    return (
      <ErrorView
        title="Unable to Load Terminals"
        message={error}
        onRetry={() => fetchHistoricalTerminalOutput()}
        panelType="main"
      />
    );
  }

  if (!allTerminalOutput?.length) {
    return <div className="terminal-empty"><EmptyStateView type="noTerminalFound" /></div>;
  }

  return (
    <div className="terminal-wrapper">
      <TerminalContent 
        content={displayedText}
        terminalRef={terminalRef}
        onScroll={handleScroll}
      />
    </div>
  );
};

export default TerminalPanel;