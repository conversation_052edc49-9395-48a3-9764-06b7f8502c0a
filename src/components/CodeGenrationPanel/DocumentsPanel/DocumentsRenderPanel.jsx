'use client';

import { useEffect, useState } from "react";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import { useSearchParams } from "next/navigation";
import DocumentView from "@/app/(panel)/[organization_id]/[type]/[projectId]/query/components/content/DocumentView";
import { fetchSavedDocuments } from "@/utils/documentationAPI";

const DocumentLoader = () => {
  return (
    <div className="flex items-center justify-center h-full">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
    </div>
  );
};

const DocumentsRenderPanel = ({ activeTab, projectId, taskId }) => {
    const [documents, setDocuments] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [completeDocuments, setCompleteDocuments] = useState([]);
    const [loadingDocuments, setLoadingDocuments] = useState({});
    const [accordionKeys, setAccordionKeys] = useState(new Date().toISOString());
    const [savedDocuments, setSavedDocuments] = useState([]);

    useEffect(() => {
      const fetchData = async () => {
        const savedDocs = await fetchSavedDocuments(projectId, "SAVED", "", taskId);
        if(savedDocs){
          setSavedDocuments(savedDocs.files);
        }
      }
  
      fetchData();
    }, [taskId]);

    const searchParams = useSearchParams();
    const { documentContent } = useCodeGeneration();

    const parseDocumentSections = (content) => {
        if (!content) return [];
    
        const lines = content.split('\n');
        const sections = [];
        let currentSection = null;
        let isInCodeBlock = false;
        let codeContent = '';
        let codeLanguage = '';
    
        lines.forEach((line, index) => {
          // Handle code blocks
          if (line.trim().startsWith('```')) {
            if (!isInCodeBlock) {
              // Starting a code block
              if (currentSection) {
                sections.push(currentSection);
                currentSection = null;
              }
              isInCodeBlock = true;
              codeLanguage = line.trim().slice(3).trim();
              codeContent = '';
            } else {
              // Ending a code block
              if (codeContent.trim()) {
                sections.push({
                  type: codeLanguage.toLowerCase() === 'mermaid' ? 'mermaid' : 'code',
                  language: codeLanguage,
                  content: codeContent.trim()
                });
              }
              isInCodeBlock = false;
            }
            return;
          }
    
          if (isInCodeBlock) {
            codeContent += line + '\n';
            return;
          }
    
          // Handle regular content
          if (line.startsWith('#')) {
            if (currentSection) {
              sections.push(currentSection);
            }
            currentSection = line;
          } else if (line.trim()) {
            if (currentSection) {
              currentSection += '\n' + line;
            } else {
              currentSection = line;
            }
          }
        });
    
        // Add the last section
        if (currentSection) {
          sections.push(currentSection);
        }
    
        return sections;
    };

    // Update documents when documentContent changes
    useEffect(() => {
        if (documentContent) {
            setIsLoading(true);
            
            // Check if this document is already in our list (by title)
            const exists = documents.some(doc => doc.title === documentContent.title);
            
            if (!exists) {
                // Add the new document to our existing documents (don't replace)
                setDocuments(prevDocs => [...prevDocs, documentContent]);
            } else {
                // If the document already exists, update its content
                setDocuments(prevDocs => 
                    prevDocs.map(doc => 
                        doc.title === documentContent.title 
                            ? { ...doc, content: documentContent.content } 
                            : doc
                    )
                );
            }
            
            setIsLoading(false);
        }
    }, [documentContent]);

    // Process documents to create formatted complete documents
    useEffect(() => {
        if (documents && documents.length > 0) {
            setCompleteDocuments(documents.map((document) => {
                if (document.content) {
                    const documentContent = document.content
                        .split('\n')  // Split into lines
                        .map(line => {
                            // Preserve leading whitespace by not trimming
                            // Only trim trailing whitespace
                            return line.replace(/\s+$/, '');
                        })
                        .filter(line => line !== '')
                        .join('\n')
                        .replace(/\n+$/, ''); // Trim only trailing newlines
                    
                    const sections = parseDocumentSections(documentContent);
                    
                    // Extract title from first section if it starts with #
                    let title = document.title;
                
                    const documentDetails = {
                        title: title,
                        sections: sections,
                        id: document.title, // Use title as ID to help with document updates
                        isComplete: true // Since we're capturing everything after 'Document:'
                    };

                    return documentDetails;
                } else {
                    const documentDetails = {
                        title: document.title,
                        sections: [],
                        id: document.title, // Use title as ID to help with document updates
                        isComplete: true
                    };
                    return documentDetails;
                }
            }));
        }
    }, [documents]);

    useEffect(() => {
      setAccordionKeys(new Date().toISOString());
    }, [completeDocuments]); //set a new key to rerender after change in complete documents

    return (
        <div className="h-full overflow-y-auto">
            {isLoading ? <DocumentLoader /> : 
                documents.length === 0 ? (
                    <div className="h-full flex justify-center items-center">
                        <EmptyStateView />
                    </div>
                ) : (
                    <DocumentView 
                        key={accordionKeys}  //to have a new re-render everytime new document changes to open the latest accordion
                        documentData={completeDocuments} 
                        accordionKey={accordionKeys} 
                        loadingDocuments={loadingDocuments}
                        folderId={taskId}
                        projectId={projectId}
                        savedDocuments={savedDocuments}
                        setSavedDocuments={setSavedDocuments}
                    />
                )
            }
        </div>
    );
};

export default DocumentsRenderPanel;