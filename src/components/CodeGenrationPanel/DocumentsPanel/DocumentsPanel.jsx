'use client';

import { useEffect, useState } from "react";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import { useSearchParams } from "next/navigation";
import DocumentView from "@/app/(panel)/[organization_id]/[type]/[projectId]/query/components/content/DocumentView";
import { usePathname } from "next/navigation";
import { fetchSavedDocuments } from "@/utils/documentationAPI";

const DocumentLoader = () => {
  return (
    <div className="flex items-center justify-center h-full">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
  );
};


const DocumentsPanel = ({activeTab, documentContent}) => {
    const [documents, setDocuments] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [completeDocuments, setCompleteDocuments] = useState([]);
    const searchParams = useSearchParams()
    const pathname = usePathname();
    const projectId = pathname.split('/')[3];
    const task_id = searchParams.get('task_id');
    const [savedDocuments, setSavedDocuments] = useState([]);

    const { 
        wsConnection,
        newDocAlert, 
        setNewDocAlert,
    } = useCodeGeneration();

    const parseDocumentSections = (content) => {
        if (!content) return [];
    
        const lines = content.split('\n');
        const sections = [];
        let currentSection = null;
        let isInCodeBlock = false;
        let codeContent = '';
        let codeLanguage = '';
    
        lines.forEach((line, index) => {
          // Handle code blocks
          if (line.trim().startsWith('```')) {
            if (!isInCodeBlock) {
              // Starting a code block
              if (currentSection) {
                sections.push(currentSection);
                currentSection = null;
              }
              isInCodeBlock = true;
              codeLanguage = line.trim().slice(3).trim();
              codeContent = '';
            } else {
              // Ending a code block
              if (codeContent.trim()) {
                sections.push({
                  type: codeLanguage.toLowerCase() === 'mermaid' ? 'mermaid' : 'code',
                  language: codeLanguage,
                  content: codeContent.trim()
                });
              }
              isInCodeBlock = false;
            }
            return;
          }
    
          if (isInCodeBlock) {
            codeContent += line + '\n';
            return;
          }
    
          // Handle regular content
          if (line.startsWith('#')) {
            if (currentSection) {
              sections.push(currentSection);
            }
            currentSection = line;
          } else if (line.trim()) {
            if (currentSection) {
              currentSection += '\n' + line;
            } else {
              currentSection = line;
            }
          }
        });
    
        // Add the last section
        if (currentSection) {
          sections.push(currentSection);
        }
    
        return sections;
    };

    useEffect(() => {
      const fetchData = async () => {
        const savedDocs = await fetchSavedDocuments(projectId, "SAVED", "", task_id);
        if(savedDocs){
          setSavedDocuments(savedDocs.files);
        }
      }
      fetchData();
    }, []);

    useEffect(() => {
        const getData = () => {
            if (wsConnection?.readyState === WebSocket.OPEN) {
                wsConnection.send(JSON.stringify({
                  type: "get_documents",
                  task_id: searchParams.get('task_id'),
                  user_id: Cookies.get('userId')
                }));
              }
        }
        if(activeTab=='Document'){
        getData();
      }

    }, [activeTab]);

    const getDocumentContent = (docIndex, title) => {
      if(documents[docIndex].content === ''){
        if (wsConnection?.readyState === WebSocket.OPEN) {
          wsConnection.send(JSON.stringify({
            type: "get_document_content",
            task_id: searchParams.get('task_id'),
            title: title,
            user_id: Cookies.get('userId')
          }));
        }
      }
    }

    useEffect(() => {   
      setIsLoading(true);  
      if (wsConnection) {
        const handleMessage = (event) => {
          const data = JSON.parse(event.data);
          
          if (data.type === 'existing_document_names') {
            setDocuments((prev) => {
              const newDocs = data.data.filter(item => !prev.some(doc => doc.title === item)).map(item => ({'title': item, 'content': ''}));
              return prev.length === 0 ? newDocs : [...prev, ...newDocs];           
            })
          }
          else if(data.type === 'document_content') {
            setDocuments((prev) => {
              return prev.map((doc) => {
                if (doc.title === data.data.title){
                  doc.content = data.data.content;
                }
                return doc;
              })
            })
          }
          
          setIsLoading(false);
          if (newDocAlert){
            setNewDocAlert(false);
          }
        };
  
        wsConnection.addEventListener('message', handleMessage);
  
        return () => wsConnection.removeEventListener('message', handleMessage);
      }
      else{
        setIsLoading(false);
      }
    }, [wsConnection]);

    useEffect(() => {
      if(documents){
          setCompleteDocuments(documents.map((document) => {
            if(document.content){
              const documentContent = document.content
              .split('\n')  // Split into lines
              .map(line => {
                  // Preserve leading whitespace by not trimming
                  // Only trim trailing whitespace
                  return line.replace(/\s+$/, '');
              })
              .filter(line => line !== '')
              .join('\n')
              .replace(/\n+$/, ''); // Trim only trailing newlines
              
              const sections = parseDocumentSections(documentContent);
              
              // Extract title from first section if it starts with #
              let title = document.title;
          
              const documentDetails = {
                title: title,
                sections: sections,
                id: Date.now().toString(),
                isComplete: true // Since we're capturing everything after 'Document:'
              };

              return documentDetails;
            }
            else{
              const documentDetails = {
                title: document.title,
                sections: [],
                id: Date.now().toString(),
                isComplete: true // Since we're capturing everything after 'Document:'
              };

              

              return documentDetails;
            }
          }
        ))
      }
    }, [documents]);

    return (
        <div className="h-full overflow-y-auto">
            {isLoading ? <DocumentLoader /> : 
                documents.length == 0? (
                        <div className="h-full flex justify-center items-center">
                            <EmptyStateView />
                        </div>
                ) : (
                    <DocumentView documentData={completeDocuments} accordionKey={"accordion"} fetchContent={getDocumentContent} projectId={projectId} folderId={task_id} savedDocuments={savedDocuments} setSavedDocuments={setSavedDocuments}/>
                )
            }
        </div>
    );
}

export default DocumentsPanel;