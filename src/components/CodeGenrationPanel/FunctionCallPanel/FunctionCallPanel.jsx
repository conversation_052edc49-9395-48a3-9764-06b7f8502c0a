"use client";
import React, { useRef, useEffect } from "react";
import { useCodeGeneration } from "@/components/Context/CodeGenerationContext";
import { Loading2 } from "../../Loaders/Loading";
import { renderHTML } from "@/utils/helpers";
import { formatUTCToLocal } from "@/utils/datetime";
import { processFunctionCalls } from "@/utils/functionCallUtils";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import ErrorView from "@/components/Modal/ErrorViewModal";
import "@/styles/tabs/codeGenerationPanel/FunctionCallPanel.css";
import { X } from "lucide-react";

const ModalArguments = ({ args }) => {
  if (!args) return null;

  return Object.entries(args).map(([key, value]) => {
    if (key === "content") {
      return (
        <div key={key} className="modal-argument">
          <strong className="modal-argument-key">{key}</strong>
          <div dangerouslySetInnerHTML={{ __html: renderHTML(value) }} />
        </div>
      );
    }

    const renderedValue = Array.isArray(value) ? (
      <ul className="modal-argument-list">
        {value.map((item, index) => (
          <li key={index}>
            {typeof item === "object" ? JSON.stringify(item) : String(item)}
          </li>
        ))}
      </ul>
    ) : typeof value === "object" && value !== null ? (
      <span className="modal-argument-value">{JSON.stringify(value)}</span>
    ) : (
      <span className="modal-argument-value">{String(value)}</span>
    );

    return (
      <div key={key} className="modal-argument">
        <strong className="modal-argument-key">{key}</strong>
        {renderedValue}
      </div>
    );
  });
};

const CustomModal = ({ isOpen, onClose, content }) => {
  const modalRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const renderArguments = (args) => {
    if (!args) {
      return null;
    }
    return Object.entries(args).map(([key, value]) => {
      let renderedValue;
      if (key === "content") {
        return (
          <div key={key} className="mb-2">
            <strong className="text-primary">{key}</strong>
            <div dangerouslySetInnerHTML={{ __html: value }} />
          </div>
        );
      }
      if (Array.isArray(value)) {
        renderedValue = (
          <ul className="list-disc list-inside ml-4">
            {value.map((item, index) => (
              <li key={index}>
                {typeof item === "object" ? JSON.stringify(item) : String(item)}
              </li>
            ))}
          </ul>
        );
      } else if (typeof value === "object" && value !== null) {
        renderedValue = <span className="ml-2">{JSON.stringify(value)}</span>;
      } else {
        renderedValue = <span className="ml-2">{String(value)}</span>;
      }

      return (
        <div key={key} className="mb-2">
          <strong className="text-primary">{key}</strong>
          {renderedValue}
        </div>
      );
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
      <div
        ref={modalRef}
        className="bg-white rounded-lg w-[600px] h-[80vh] flex flex-col"
      >
        <div className="flex items-center justify-between p-4 border-b border-gray-200 sticky top-0 bg-white z-10">
          <h2 className="typography-heading-2 font-weight-bold">{content.function_name}</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors duration-200"
            aria-label="Close"
          >
            <X size={24} />
          </button>
        </div>
        <div className="p-6 overflow-auto flex-grow">
          <div className="mb-4">
            <h3 className="typography-heading-4 font-weight-semibold text-gray-700">Reason</h3>
            <p>{content.reason || "N/A"}</p>
          </div>

          <div className="mb-4">
            <h3 className="typography-heading-4 font-weight-semibold text-gray-700">
              Observations
            </h3>
            <p>{content.observations || "N/A"}</p>
          </div>

          <div className="mb-4">
            <h3 className="typography-heading-4 font-weight-semibold text-gray-700">Arguments</h3>
            {renderArguments(content.arguments)}
          </div>
        </div>
      </div>
    </div>
  );
};

const FunctionCallItem = ({ call, isLatestCall, onClick }) => (
  <div 
    className={`function-call-item ${isLatestCall ? 'function-call-item-latest' : ''}`}
    onClick={onClick}
  >
    <div className="function-call-content">
      <div className="function-call-info">
        <div className="function-call-field">
          <label className="function-call-label">Function Name</label>
          <p className={`function-call-value ${isLatestCall ? 'function-call-value-latest' : ''}`}>
            {call.function_name}
          </p>
        </div>
        <div className="function-call-field">
          <label className="function-call-label">Reason</label>
          <p className={`function-call-reason ${isLatestCall ? 'function-call-reason-latest' : ''}`}>
            {call.reason || "N/A"}
          </p>
        </div>
        <div className="function-call-field">
          <label className="function-call-label">Observations</label>
          <p className={`function-call-observation ${isLatestCall ? 'function-call-observation-latest' : ''}`}>
            {call.observations || "N/A"}
          </p>
        </div>
      </div>
      <div className="function-call-timestamp">
        {call.timestamp ? formatUTCToLocal(call.timestamp) : ""}
      </div>
    </div>
  </div>
);

const FunctionCallPanel = () => {
  const {
    functionCalls,
    historicalFunctionCalls,
    error,
    fetchHistoricalFunctionCalls,
  } = useCodeGeneration();

  const [modalContent, setModalContent] = React.useState(null);
  const [isLoading, setIsLoading] = React.useState(true);

  const processedCalls = React.useMemo(() => {
    const currentCalls = Array.isArray(functionCalls) ? functionCalls : [];
    const historicalCalls = Array.isArray(historicalFunctionCalls) ? historicalFunctionCalls : [];
    return processFunctionCalls([...currentCalls, ...historicalCalls]);
  }, [functionCalls, historicalFunctionCalls]);

  const latestTimestamp = React.useMemo(() => 
    processedCalls[0]?.timestamp || null, 
    [processedCalls]
  );

  useEffect(() => {
    const fetchData = async () => {
      try {
        await fetchHistoricalFunctionCalls();
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, [fetchHistoricalFunctionCalls]);

  if (isLoading) return <Loading2 />;

  if (error && !functionCalls) {
    return (
      <ErrorView
        title="Unable to Load Function calls"
        message={error}
        onRetry={() => fetchHistoricalFunctionCalls()}
        panelType="main"
      />
    );
  }

  if (processedCalls.length === 0) {
    return (
      <div className="function-calls-empty">
        <EmptyStateView type="noFunctionCallsAvailable" />
      </div>
    );
  }

  return (
    <div className="function-calls-container">
      {processedCalls.map((call, index) => (
        <FunctionCallItem
          key={`${call.function_name}-${call.timestamp}-${index}`}
          call={call}
          isLatestCall={call.timestamp === latestTimestamp}
          onClick={() => setModalContent(call)}
        />
      ))}

      <CustomModal
        isOpen={modalContent !== null}
        onClose={() => setModalContent(null)}
        content={modalContent}
      />
    </div>
  );
};

export default FunctionCallPanel;