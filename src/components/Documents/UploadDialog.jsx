import { useState } from 'react';
import { useParams } from 'next/navigation';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  CircularProgress,
  Box,
  Alert,
  Typography,
  IconButton
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  CloudUpload as CloudUploadIcon,
  Trash2 as DeleteIcon,
  File as InsertDriveFileIcon
} from 'lucide-react';
import { uploadDocument } from '@/utils/documentationAPI';

const VisuallyHiddenInput = styled('input')`
  clip: rect(0 0 0 0);
  clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  bottom: 0;
  left: 0;
  white-space: nowrap;
  width: 1px;
`;

const UploadArea = styled(Box)(({ theme, isDragging }) => ({
  border: `2px dashed ${isDragging ? theme.palette.primary.main : theme.palette.grey[300]}`,
  borderRadius: theme.shape.borderRadius,
  padding: theme.spacing(3),
  transition: 'all 0.2s ease-in-out',
  backgroundColor: isDragging ? theme.palette.action.hover : 'transparent',
  cursor: 'pointer',
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
    borderColor: theme.palette.primary.main,
  },
  position: 'relative',
  minHeight: '160px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center'
}));

export function UploadDialog({ open, onClose, currentType, onUploadSuccess }) {
  const params = useParams();
  const [file, setFile] = useState(null);
  const [description, setDescription] = useState('');
  const [version, setVersion] = useState('');
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');
  const [isDragging, setIsDragging] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!file || !version) return;

    try {
      setUploading(true);
      setError('');

      await uploadDocument(
        parseInt(params.projectId),
        currentType,
        version,
        file,
        description
      );

      setFile(null);
      setDescription('');
      setVersion('');
      onClose();
      onUploadSuccess?.();
    } catch (error) {

      setError('Failed to upload document. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files?.length) {
      setFile(e.dataTransfer.files[0]);
    }
  };

  const handleFileChange = (event) => {
    if (event.target.files?.length) {
      setFile(event.target.files[0]);
    }
  };

  const handleClose = () => {
    setFile(null);
    setDescription('');
    setVersion('');
    setError('');
    onClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          p: 1,
        }
      }}
    >
      <DialogTitle sx={{ fontSize: 'var(--font-size-xl)', fontWeight: 'var(--font-weight-semibold)' }}>
        Upload Document
      </DialogTitle>

      <form onSubmit={handleSubmit}>
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
            <UploadArea
              isDragging={isDragging}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              component="label"
            >
              <VisuallyHiddenInput
                type="file"
                onChange={handleFileChange}
                accept=".pdf,.doc,.docx,.txt"
              />

              {file ? (
                <Box sx={{ textAlign: 'center' }}>
                  <InsertDriveFileIcon
                    size={40}
                    className="text-primary mb-1" // primary.main is the orange theme color
                  />
                  <Typography variant="subtitle1" sx={{ fontWeight: 'var(--font-weight-medium)' }}>
                    {file.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {(file.size / 1024 / 1024).toFixed(2)} MB
                  </Typography>
                  <IconButton
                    onClick={(e) => {
                      e.preventDefault();
                      setFile(null);
                    }}
                    size="small"
                    sx={{ mt: 1 }}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center' }}>
                  <CloudUploadIcon
                    size={40}
                    color="gray"  // equivalent to text.secondary
                    className="mb-1"  // using Tailwind for margin-bottom
                  />
                  <Typography variant="subtitle1" sx={{ fontWeight: 'var(--font-weight-medium)' }}>
                    Choose a file or drag it here
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    PDF, DOC, DOCX or TXT (max 10MB)
                  </Typography>
                </Box>
              )}
            </UploadArea>

            <TextField
              label="Document Type"
              value={currentType}
              disabled
              fullWidth
              variant="outlined"
            />

            <TextField
              label="Version"
              value={version}
              onChange={(e) => setVersion(e.target.value)}
              placeholder="e.g., 1.0.0"
              required
              fullWidth
              variant="outlined"
            />

            <TextField
              label="Description"
              multiline
              rows={3}
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              fullWidth
              variant="outlined"
              placeholder="Enter a brief description..."
            />
          </Box>
        </DialogContent>

        <DialogActions sx={{ p: 2, pt: 0 }}>
          <Button
            onClick={handleClose}
            variant="outlined"
            color="inherit"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={!file || !version || uploading}
            sx={{ minWidth: 100 }}
          >
            {uploading ? (
              <>
                <CircularProgress
                  size={20}
                  color="inherit"
                  sx={{ mr: 1 }}
                />
                Uploading...
              </>
            ) : (
              'Upload'
            )}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
}