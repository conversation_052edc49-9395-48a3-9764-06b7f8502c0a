'use client';
import React from 'react';
import { formatDistanceToNow } from 'date-fns';
import { FileText, FileSpreadsheet, FileCog, Download } from 'lucide-react';
import { useRouter, useParams, useSearchParams } from 'next/navigation';

export function DocumentList({ documents, onDownload }) {
  const router = useRouter();
  const searchParams = useSearchParams()

  const getFileIcon = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase();
    switch (extension) {
      case 'pdf':
        return FileCog;
      case 'doc':
      case 'docx':
        return FileSpreadsheet;
      default:
        return FileText;
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const projectId = useParams().projectId
  const currentType = searchParams.get('type', 'PRD')
  const handleDocumentClick = (document) => {
    router.push(`documents/view?projectId=${projectId}&docType=${currentType}&version=${document.version}&folder_path=${document.folder_path}&fileName=${encodeURIComponent(document.file_name)}`);
  };

  return (
    <div className="relative rounded-lg border border-gray-200">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="sticky top-0 px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider bg-gray-50">
                Name
              </th>
              <th scope="col" className="sticky top-0 px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider bg-gray-50">
                Last Modified
              </th>
              <th scope="col" className="sticky top-0 px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider bg-gray-50">
                Size
              </th>
              <th scope="col" className="sticky top-0 px-6 py-3 bg-gray-50">
                <span className="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {documents.map((document, index) => {
              const FileIcon = getFileIcon(document.file_name);
              return (
                <tr
                  key={`${document.file_name}-${index}`}
                  className="hover:bg-gray-50"
                >
                  <td
                    className="px-6 py-4 cursor-pointer"
                    onClick={() => handleDocumentClick(document)}
                  >
                    <div className="flex items-center">
                      <FileIcon
                        className="h-10 w-10 text-gray-400"
                      />
                      <div className="ml-4">
                        <div className="typography-body-sm font-weight-medium text-gray-900">
                          {document.file_name}
                        </div>
                        <div className="typography-body-sm text-gray-500">
                          Version {document.version}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 typography-body-sm text-gray-500">
                    {formatDistanceToNow(new Date(document.last_modified), { addSuffix: true })}
                  </td>
                  <td className="px-6 py-4 typography-body-sm text-gray-500">
                    {formatFileSize(document.size)}
                  </td>
                  <td className="px-6 py-4 text-right typography-body-sm font-weight-medium">
                    <button
                      onClick={() => onDownload(document)}
                      className="text-primary hover:text-primary-900"
                    >
                      <Download width="20" height="20" />
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
}