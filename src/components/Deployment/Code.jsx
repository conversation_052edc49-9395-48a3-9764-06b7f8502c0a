import { useState, useContext } from 'react';
import Editor from '@monaco-editor/react';
import { DiscussionTabContext } from '@/components/Context/DiscussionTabContext';

export default function Code() {
 const [selectedFile, setSelectedFile] = useState('main');
 const { overviewData, isLoading } = useContext(DiscussionTabContext);

 if (isLoading) return <div className="h-64 bg-gray-200 rounded animate-pulse" />;

 const files = {
   main: {
     name: 'Main Configuration',
     content: overviewData?.deployments[0]?.properties?.main_tf
   }, 
   variables: {
     name: 'Variables', 
     content: overviewData?.deployments[0]?.properties?.variables_tf
   },
   providers: {
     name: 'Providers',
     content: overviewData?.deployments[0]?.properties?.providers_tf
   },
   outputs: {
     name: 'Outputs',
     content: overviewData?.deployments[0]?.properties?.outputs_tf
   }
 };

 return (
   <div className="flex min-h-[72vh] max-h-[75vh]">
     <div className="w-44 border-r">
       {Object.entries(files).map(([key, {name}]) => (
         <button
           key={key}
           className={`w-full p-2 text-left hover:bg-gray-100 ${
             selectedFile === key ? 'bg-primary-50 border-l-4 border-primary' : ''
           }`}
           onClick={() => setSelectedFile(key)}
         >
           {name}
         </button>
       ))}
     </div>
     <div className="flex-1">
       <Editor
         height="100%"
         language="hcl"
         theme="vs-dark" 
         value={files[selectedFile].content?.replace(/\"\"\"/g, '')}
         options={{ readOnly: true }}
       />
     </div>
   </div>
 );
}