import React, { lazy } from 'react';;
import "swagger-ui-react/swagger-ui.css";
const SwaggerUI = lazy(() => import('swagger-ui-react'));

interface SwaggerUIProps {
  spec?: any;
  url?: string;
}

export const SwaggerUIComponent: React.FC<SwaggerUIProps> = ({ spec, url }) => {
  return (
    <div>
      <SwaggerUI
        spec={spec}
        url={url}
        supportedSubmitMethods={[]}
      />
    </div>
  );
};

export default SwaggerUIComponent;
