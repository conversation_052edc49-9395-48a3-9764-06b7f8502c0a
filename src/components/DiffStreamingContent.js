import React, { useState, useEffect, useRef, useCallback } from 'react';

const DiffStreamingContent = React.memo(function DiffStreamingContent({ diffContent, speed = 1.5 }) {
  const [visibleHtml, setVisibleHtml] = useState('');
  const [isComplete, setIsComplete] = useState(false);
  const containerRef = useRef(null);

  const scrollToBottom = useCallback(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, []);

  useEffect(() => {
    if (!diffContent) return;

    // Split and clean up the content
    let diffLines = diffContent
      .split('\n')
      .filter(Boolean)
      .map(line => line.replace(/\r$/, ''));

    setVisibleHtml('');
    setIsComplete(false);

    let currentIndex = 0;
    let accumulated = '';

    const addNextLine = () => {
      if (currentIndex < diffLines.length) {
        const currentLine = diffLines[currentIndex];
        accumulated += currentIndex === 0 ? currentLine : '\n' + currentLine;
        setVisibleHtml(accumulated);
        currentIndex++;
        timeoutId = setTimeout(addNextLine, speed);
        setTimeout(scrollToBottom, 0);
      } else {
        setIsComplete(true);
      }
    };

    let timeoutId = setTimeout(addNextLine, speed);

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [diffContent, speed, scrollToBottom]);

  if (!diffContent) return null;

  return (
    <div className="relative">
      <div
        ref={containerRef}
        className="diff-container"
        style={{
          fontSize: 'var(--font-size-code)',
          lineHeight: '1.4',
          maxHeight: '600px',
          overflow: 'auto'
        }}
        dangerouslySetInnerHTML={{ __html: visibleHtml }}
      />
      {!isComplete && (
        <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent" />
      )}
    </div>
  );
});

DiffStreamingContent.displayName = 'DiffStreamingContent';

export default DiffStreamingContent;