import React, { useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { X, Check, AlertCircle,Clock, TriangleAlert } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import FileTree from './ReconfigFileTree';
import { getNodeVersions, fetchChildNodes, fetchNodeById } from '@/utils/api';
import { usePathname } from 'next/navigation';
import Cookies from 'js-cookie';
import { DynamicButton } from '../UIComponents/Buttons/DynamicButton';
import { TreeData, TreeNode, ContentVersion, Metadata } from '@/types/reconfig';
import VersionRender from '../UiMetadata/VersionRender';
import EmptyStateView from './EmptyStateModal';

// VersionPanel props
interface VersionPanelProps {
  title: string;
  version: ContentVersion | undefined;
  isPrevious: boolean;
}

const VersionPanel: React.FC<VersionPanelProps> = ({ title, version, isPrevious }) => {
  const { description, properties } = version || {};
  const scopeItems = Array.isArray(properties?.scope) ? properties.scope : ['No scope data available.'];
  const objectiveItems = Array.isArray(properties?.objective) ? properties.objective : ['No objective data available.'];

  return (
    <div className={`w-1/2 p-4 ${isPrevious ? 'border-r border-gray-200' : ''}`}>
      <div className="mb-2">
        <h3 className="typography-body-lg font-weight-semibold text-gray-900">{title}</h3>
      </div>
      <div className="mb-4">
        <h4 className="text-md font-weight-medium text-gray-700">Description</h4>
        <p className="text-gray-600">{description || 'No description available.'}</p>
      </div>
      <div>
        <h4 className="text-md font-weight-medium text-gray-700">Scope</h4>
        <ul className="list-disc list-inside text-gray-600">
          {scopeItems.map((item, index) => (
            <li key={index} className="ml-4">{item}</li>
          ))}
        </ul>
      </div>
      <div className="mt-4">
        <h4 className="text-md font-weight-medium text-gray-700">Objective</h4>
        <ul className="list-disc list-inside text-gray-600">
          {objectiveItems.map((item, index) => (
            <li key={index} className="ml-4">{item}</li>
          ))}
        </ul>
      </div>
    </div>
  );
};

interface CloseModalProps {
  onReject: () => void;
  onClose: () => void;
}

const CloseModal: React.FC<CloseModalProps> = ({ onReject, onClose }) => {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto">
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={() => onClose()}
      />
      <div className="relative bg-white rounded-lg shadow-xl w-full max-w-md mx-4 my-12 overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="typography-heading-4 font-weight-semibold text-gray-900">
            Close Confirmation
          </h2>
          <DynamicButton
            variant="ghost"
            size="sqSmall"
            icon={X}
            onClick={() => onClose()}
            tooltip="Close"
          />
        </div>
        <div className="p-6 space-y-5">
          <p className="text-gray-600">Are you sure you want to close?</p>
          <div className="flex items-start p-4 rounded-lg bg-red-50 text-red-800">
            <TriangleAlert className="w-5 h-5 mt-0.5 flex-shrink-0" />
            <p className="ml-3 typography-body-sm">Closing will discard all the data from the current re-configuration.</p>
          </div>
        </div>
        <div className="flex justify-end gap-3 px-6 py-5 bg-gray-50">
          <DynamicButton
            variant="secondary"
            onClick={() => onClose()}
            text="Cancel"
          />
          <DynamicButton
            variant="danger"
            onClick={() => onReject()}
            text="Confirm"
          />
        </div>
      </div>
    </div>
  );
};

interface ReconfigModalProps {
  onClose: () => void;
  onApprove: () => void;
}

const ReconfigModal: React.FC<ReconfigModalProps> = ({ onClose, onApprove }) => {
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [isTreeCollapsed, setIsTreeCollapsed] = useState(false);
  const [loadingContent, setLoadingContent] = useState(false);
  const [content, setContent] = useState<{ base_version?: ContentVersion; reconfigured_version?: ContentVersion } | null>(null);
  const pathname = usePathname();
  const projectId = pathname.split("/")[3];
  const tenantId = Cookies.get('tenant_id');
  const [metaData, setMetaData] = useState<Metadata>({});
  const [treeData, setTreeData] = useState<TreeData[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCloseModal, setShowCloseModal] = useState(false);
  const [projectName, setProjectName] = useState<string>("Loading...");

  // scroll synchronization
  const oldContentRef = useRef<HTMLDivElement>(null);
  const updatedContentRef = useRef<HTMLDivElement>(null);
  const [isScrolling, setIsScrolling] = useState(false);

  
  const defaultContentVersion: ContentVersion = {
    description: 'No description available.',
    properties: {
      scope: ['No scope data available.'],
      objective: ['No objective data available.'],
    },
    type: 'project',
    title: 'Default Project',
  };

  // Helper function to find a node by ID in the tree
  const findNodeById = (nodes: TreeNode[], id: string): TreeNode | undefined => {
    for (const node of nodes) {
      if (node.id === id) return node;
      if (node.children) {
        const found = findNodeById(node.children, id);
        if (found) return found;
      }
    }
    return undefined;
  };

  const fetchNodeVersions = async (nodeId: string, nodeType: string) => {
    setLoadingContent(true);
    try {
      
      const response = await getNodeVersions(projectId, nodeId, nodeType.charAt(0).toUpperCase() + nodeType.slice(1), tenantId);
      const normalizeVersion = (version: ContentVersion | undefined): ContentVersion | undefined => {
        if (!version) return undefined;
        return {
          ...version,
          title: version.title || version.title || 'Untitled', 
          description: version.description || 'No description available.',
          properties: {
            ...version.properties,
            scope: Array.isArray(version.properties?.scope) ? version.properties.scope : ['No scope data available.'],
            objective: Array.isArray(version.properties?.objective) ? version.properties.objective : ['No objective data available.'],
          },
          type: version.type || nodeType.toLowerCase(), 
          metadata: version.metadata || {},
        };
      };

      const transformedResponse = {
        base_version: normalizeVersion(response.base_version),
        reconfigured_version: normalizeVersion(response.reconfigured_version),
      };

      
      setContent(transformedResponse);
      setMetaData(response?.reconfigured_version?.ui_metadata ||response?.base_version?.ui_metadata||{});
    } catch (error) {
      
      setContent(null); // Reset content on error
    } finally {
      setLoadingContent(false);
    }
  };

  const fetchNodeDetails = async () => {
    setLoading(true);
    try {
      const project = await fetchNodeById(projectId, "Project");
      setProjectName(project.properties?.Title || "Unnamed Project");

      const workitemRoot = await fetchChildNodes(projectId, 'Project', 'WorkItemRoot');
      const requirementRoot = await fetchChildNodes(projectId, 'Project', "RequirementRoot");
      const architectureRoot = await fetchChildNodes(projectId, "Project", "Architecture");
      const architectureRequirements = await fetchChildNodes(architectureRoot[0].id,"Project","ArchitecturalRequirement")
       
      const systemContextRoot = await fetchChildNodes(projectId, "Project", "SystemContext");
      const documentation = await fetchNodeById(projectId, "Documentation");
    

      let workItemsChildren: TreeNode[] = [];
      let requirementsChildren: TreeNode[] = [];
      let architecturalChildren: TreeNode[] = [];
      let systemContextChildren: TreeNode[] = [];
      let documentationChildren: TreeNode[] = [];

      if (workitemRoot.length > 0) {
        const workItems = await fetchChildNodes(workitemRoot[0].id, "WorkItemRoot", "WorkItem");
        
        workItemsChildren = workItems.map((workItem: TreeNode) => ({
          id: workItem.id,
          name: workItem.properties?.Title || "Unnamed Work Item",
          type: "workItem",
          status: "pending",
        }));
      }

      if (requirementRoot.length > 0) {
        const epics = await fetchChildNodes(requirementRoot[0].id, "RequirementRoot", "Epic");
        const epicChildren = await Promise.all(
          epics.map(async (epic: TreeNode) => {
            const userStories = await fetchChildNodes(epic.id, "Epic", "UserStory");
            return {
              id: epic.id,
              name: epic.properties?.Title || "Unnamed Epic",
              type: "epic",
              status: "pending",
              children: userStories.map((story: TreeNode) => ({
                id: story.id,
                name: story.properties?.Title || "Unnamed User Story",
                type: "userStory",
                status: "pending",
              })),
            };
          })
        );
        requirementsChildren = [
          {
            id: requirementRoot[0].id,
            name: "Epics",
            type: "epic",
            status: "pending",
            children: epicChildren,
          },
        ];
      }

      if (architectureRoot.length > 0) {
        
        const functionalReqs = await fetchChildNodes(architectureRequirements[0].id, "Architecture", "FunctionalRequirement");
     
        const nonFunctionalReqs = await fetchChildNodes(architectureRequirements[0].id, "Architecture", "NonFunctionalRequirement");
    
        architecturalChildren = [
          {
            id: architectureRoot[0].id,
            name: "Functional Requirements",
            type: "functionalRequirement",
            status: "pending",
            children: functionalReqs.map((req: TreeNode) => ({
              id: req.id,
              name: req.properties?.Title || "Unnamed Functional Requirement",
              type: "functionalRequirement",
              status: "pending",
            })),
          },
          {
            id: architectureRoot[0].id,
            name: "Non-Functional Requirements",
            type: "nonFunctionalRequirement",
            status: "pending",
            children: nonFunctionalReqs.map((req: TreeNode) => ({
              id: req.id,
              name: req.properties?.Title || "Unnamed Non-Functional Requirement",
              type: "nonFunctionalRequirement",
              status: "pending",
            })),
          },
        ];
      }

      if (systemContextRoot.length > 0) {
        const containers = await fetchChildNodes(systemContextRoot[0].id, "SystemContext", "Container");
        const containerChildren = await Promise.all(
          containers.map(async (container: TreeNode) => {
            const components = await fetchChildNodes(container.id, "Container", "Component");
            const componentChildren = await Promise.all(
              components.map(async (component: TreeNode) => {
                const designs = await fetchChildNodes(component.id, "Component", "Design");
                const interfaces = await fetchChildNodes(component.id, "Component", "Interface");
                return {
                  id: component.id,
                  name: component.properties?.Title || "Unnamed Component",
                  type: "component",
                  status: "pending",
                  children: [
                    ...designs.map((design: TreeNode) => ({
                      id: design.id,
                      name: design.properties?.Title || "Unnamed Design",
                      type: "design",
                      status: "pending",
                    })),
                    ...interfaces.map((interfaceNode: TreeNode) => ({
                      id: interfaceNode.id,
                      name: interfaceNode.properties?.Title || "Unnamed Interface",
                      type: "interface",
                      status: "pending",
                    })),
                  ],
                };
              })
            );
            return {
              id: container.id,
              name: container.properties?.Title || "Unnamed Container",
              type: "container",
              status: "pending",
              children: componentChildren,
            };
          })
        );
        systemContextChildren = [
          {
            id: systemContextRoot[0]?.id || "system-context",
            name: "System Context",
            type: "systemContext",
            description: "Overview of system interactions",
            status: "pending",
            children: containerChildren,
          },
        ];
      }

      if (documentation) {
        documentationChildren = [
          {
            id: "prd",
            name: "PRD",
            type: "prd",
            description: "Product Requirements Document",
            status: "pending",
          },
          {
            id: "sad",
            name: "SAD",
            type: "sad",
            description: "System Architecture Document",
            status: "pending",
          },
        ];
      }

      const structuredData: TreeData[] = [
        {
          id: project.id,
          name: "Project",
          type: "project",
          status: "completed",
          children: [
            {
              id: workitemRoot[0]?.id || "work-items",
              name: "Work Items",
              type: "workItem",
              description: "Map deliverables into actionable tasks",
              status: "completed",
              children: workItemsChildren,
            },
            {
              id: requirementRoot[0]?.id || "requirements",
              name: "Requirements",
              type: "requirement",
              description: "Transform needs into clear specifications",
              status: "completed",
              children: requirementsChildren,
            },
            {
              id: architectureRequirements[0]?.id || "architecture",
              name: "Architectural Requirements",
              type: "architecturalRequirement",
              description: "Define system requirements",
              status: "in-progress",
              children: architecturalChildren,
            },
            ...systemContextChildren,
            {
              id: documentation?.id || "documentation",
              name: "Documentation",
              type: "documentation",
              description: "Project documentation overview",
              status: "pending",
              children: documentationChildren,
            },
          ],
        },
      ];

      setTreeData(structuredData);
      // Automatically select the first node and fetch its versions
      if (structuredData[0]?.id) {
        setSelectedNodeId(structuredData[0].id);
        fetchNodeVersions(structuredData[0].id, structuredData[0].type);
      }
    } catch (error) {
      
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchNodeDetails();
  }, []);

  const handleNodeSelect = (nodeId: string) => {
    if (nodeId === selectedNodeId) return;
    setSelectedNodeId(nodeId);
    // Fetch versions for the selected node
    const flatNodes = treeData.flatMap(node => [node, ...(node.children || [])]);
    const selectedNode = findNodeById(flatNodes, nodeId);
    if (selectedNode) {
      fetchNodeVersions(nodeId, selectedNode.type);
    }
  };

  const contentVariants = {
    initial: { opacity: 0, transition: { duration: 0.15 } },
    animate: { opacity: 1, transition: { duration: 0.15 } },
    exit: { opacity: 0, transition: { duration: 0.15 } },
  };

  // Synchronized scrolling handler
  const handleScroll = (e: React.UIEvent<HTMLDivElement>, source: string) => {
    if (isScrolling) return;
    setIsScrolling(true);
    const sourceElement = e.target as HTMLDivElement;
    const targetElement = source === 'old' ? updatedContentRef.current : oldContentRef.current;
    if (sourceElement && targetElement) {
      targetElement.scrollTop = sourceElement.scrollTop;
      targetElement.scrollLeft = sourceElement.scrollLeft;
    }
    setTimeout(() => setIsScrolling(false), 10);
  };




  return (
    <div>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.2 }}
        className="fixed inset-0 bg-gray-900/50 backdrop-blur-[2px] flex items-center justify-center"
        style={{ zIndex: 50 }}
      >
        <motion.div
          initial={{ scale: 0.98, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.98, opacity: 0 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
          className="w-[95vw] h-[90vh] bg-white rounded-xl shadow-2xl flex flex-col"
          style={{ zIndex: 51 }}
        >
          <div className="flex items-center justify-between px-6 py-4 border-b bg-white rounded-t-xl space-y-2">
            <div className="flex items-center gap-4">
              <h2 className="typography-body-lg font-weight-semibold text-gray-900">Reconfig - Review and Approve</h2>
            </div>
            <button
              onClick={() => setShowCloseModal(true)}
              className="p-2 hover:bg-gray-100 rounded-md transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          <div className="flex-1 flex overflow-hidden bg-gray-50">
            <motion.div
              layout
              initial={false}
              animate={{ width: isTreeCollapsed ? 48 : 400, transition: { type: "spring", stiffness: 300, damping: 30 } }}
              className="flex-shrink-0 border-r border-gray-200"
            >
              {loading ? (
                 <div className="flex flex-col h-full w-full p-4 bg-gray-50">
                 {/* Project header */}
                 <div className="flex items-center space-x-3 mb-4 animate-pulse">
                   <div className="w-6 h-6 bg-gray-200 rounded-md"></div>
                   <div className="h-5 w-24 bg-gray-200 rounded"></div>
                 </div>
           
                 {/* Vertical timeline line */}
                 <div className="flex flex-col space-y-6 relative">
                   <div className="absolute left-3 top-3 w-0.5 h-[calc(100%-24px)] bg-primary-100"></div>
                   
                   {/* Work Items */}
                   <div className="flex flex-col">
                     <div className="flex items-center space-x-3 animate-pulse">
                       <div className="w-6 h-6 bg-gray-200 rounded z-10"></div>
                       <div className="flex flex-col">
                         <div className="h-5 w-28 bg-gray-200 rounded mb-1"></div>
                         <div className="h-4 w-56 bg-gray-100 rounded"></div>
                       </div>
                     </div>
                   </div>
           
                   {/* Requirements */}
                   <div className="flex flex-col">
                     <div className="flex items-center space-x-3 animate-pulse">
                       <div className="w-6 h-6 bg-gray-200 rounded z-10"></div>
                       <div className="flex flex-col">
                         <div className="h-5 w-32 bg-gray-200 rounded mb-1"></div>
                         <div className="h-4 w-64 bg-gray-100 rounded"></div>
                       </div>
                     </div>
                   </div>
           
                   {/* Architectural Requirements */}
                   <div className="flex flex-col">
                     <div className="flex items-center space-x-3 animate-pulse">
                       <div className="w-6 h-6 bg-gray-200 rounded z-10"></div>
                       <div className="flex flex-col">
                         <div className="h-5 w-48 bg-gray-200 rounded mb-1"></div>
                         <div className="h-4 w-52 bg-gray-100 rounded"></div>
                       </div>
                     </div>
                   </div>
           
                   {/* System Context */}
                   <div className="flex flex-col">
                     <div className="flex items-center space-x-3 animate-pulse">
                       <div className="w-6 h-6 bg-gray-200 rounded z-10"></div>
                       <div className="flex flex-col">
                         <div className="h-5 w-36 bg-gray-200 rounded mb-1"></div>
                         <div className="h-4 w-60 bg-gray-100 rounded"></div>
                       </div>
                     </div>
           
                     {/* System Context Children */}
                     <div className="ml-8 mt-6 space-y-6">
                       {/* Calculator App */}
                       <div className="flex items-center space-x-3 animate-pulse relative">
                         <div className="absolute -left-8 top-3 w-6 h-0.5 bg-primary-100"></div>
                         <div className="w-6 h-6 bg-gray-200 rounded z-10"></div>
                         <div className="h-5 w-32 bg-gray-200 rounded"></div>
                       </div>
           
                       {/* Main Component */}
                       <div className="flex flex-col space-y-6">
                         <div className="flex items-center space-x-3 animate-pulse relative">
                           <div className="absolute -left-8 top-3 w-6 h-0.5 bg-primary-100"></div>
                           <div className="w-6 h-6 bg-gray-200 rounded z-10"></div>
                           <div className="h-5 w-36 bg-gray-200 rounded"></div>
                         </div>
           
                         {/* Design for Main Component */}
                         <div className="ml-8 flex items-center space-x-3 animate-pulse relative">
                           <div className="absolute -left-8 top-3 w-6 h-0.5 bg-primary-100"></div>
                           <div className="w-6 h-6 bg-gray-200 rounded z-10"></div>
                           <div className="h-5 w-44 bg-gray-200 rounded"></div>
                         </div>
                       </div>
                     </div>
                   </div>
           
                   {/* Documentation */}
                   <div className="flex flex-col">
                     <div className="flex items-center space-x-3 animate-pulse">
                       <div className="w-6 h-6 bg-gray-200 rounded z-10"></div>
                       <div className="flex flex-col">
                         <div className="h-5 w-32 bg-gray-200 rounded mb-1"></div>
                         <div className="h-4 w-56 bg-gray-100 rounded"></div>
                       </div>
                     </div>
                   </div>
                 </div>
               </div>
              ) : (
                <FileTree
                  data={treeData}
                  onSelect={handleNodeSelect}
                  selectedNodeId={selectedNodeId}
                  fetchNodeVersions={fetchNodeVersions}
                  isCollapsed={isTreeCollapsed}
                  onToggleCollapse={() => setIsTreeCollapsed(!isTreeCollapsed)}
                  projectName={projectName}
                />
              )}
            </motion.div>
            <motion.div layout className="flex-1 relative">
              <AnimatePresence mode="wait">
                {loadingContent ? (
                  <motion.div
                    key="loading"
                    variants={contentVariants}
                    initial="initial"
                    animate="animate"
                    exit="exit"
                    className="absolute inset-0 flex items-center justify-center bg-gray-50"
                  >
                    <div className="flex flex-col items-center gap-2">
                      <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                      <span className="typography-body-sm text-gray-500">Loading changes...</span>
                    </div>
                  </motion.div>
                ) : content ? (
                  <motion.div
                    key="content"
                    variants={contentVariants}
                    initial="initial"
                    animate="animate"
                    exit="exit"
                    className="h-full flex"
                  >
                    <div 
                      className="w-1/2 border-r border-gray-200 overflow-y-auto p-4" 
                      ref={oldContentRef}
                      onScroll={(e) => handleScroll(e, 'old')}
                    >
                      {/* <h3 className="typography-body-lg font-weight-semibold mb-2">Previous Version</h3> */}
                      <div className='p-4'>
                          <div className="flex items-center justify-between">
                            <h3 className="typography-body-lg font-weight-medium text-gray-900">
                             Title: {content.base_version?.properties?.Title || 'Title not available'}
                            </h3>
                            <div className="flex items-center gap-2">
                              <Clock className="w-4 h-4 text-gray-400" />
                              <span className="typography-body-sm text-gray-500">
                                {'Previous Version'}
                              </span>
                            </div>
                          </div>
                          <div className='mt-4'>
                          <VersionRender
                           properties={content.reconfigured_version?.properties ?? defaultContentVersion}
                           metadata={metaData}
                           to_skip={["configuration_state", "Type", "Title"]}
                           compareWithProperties={content.base_version?.properties  ?? {}}
                           isPreviousVersion={true}
                           /></div>
                      </div>
                      {(!content.base_version || !content.base_version.description) && (
                       <EmptyStateView type="noPreviousVersionFound" onClick={()=> {}}/>
                      )}
                    </div>
                    <div 
                      className="w-1/2 overflow-y-auto p-4"
                      ref={updatedContentRef}
                      onScroll={(e) => handleScroll(e, 'updated')}
                    >
                      {/* <h3 className="typography-body-lg font-weight-semibold mb-2">Updated Version</h3> */}
                      <div className='p-4'>
                          <div className="flex items-center justify-between">
                            <h3 className="typography-body-lg font-weight-medium text-gray-900">
                               Title: {content.reconfigured_version?.properties?.Title || 'Title not available'}
                            </h3>
                            <div className="flex items-center gap-2">
                              <Clock className="w-4 h-4 text-gray-400" />
                              <span className="typography-body-sm text-gray-500">
                                {'Updated Version'}
                              </span>
                            </div>
                          </div>
                          <div className='mt-4'>
                          <VersionRender
                           properties={content.reconfigured_version?.properties ?? defaultContentVersion}
                           metadata={metaData}
                           to_skip={["configuration_state", "Type", "Title"]}
                           compareWithProperties={content.base_version?.properties ?? {}}
                           isPreviousVersion={false}
                           /></div>
                      </div>
                      {(!content.reconfigured_version || !content.reconfigured_version.description) && (
                       <EmptyStateView type="noCurentVersionFound" onClick={()=> {}}/>
                      )}
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    key="empty"
                    variants={contentVariants}
                    initial="initial"
                    animate="animate"
                    exit="exit"
                    className="h-full flex items-center justify-center"
                  >
                    <div className="text-center max-w-md">
                      <AlertCircle className="w-10 h-10 text-gray-600 mx-auto mb-3" />
                      <h3 className="typography-body font-weight-medium text-gray-900 mb-1">No Content Selected</h3>
                      <p className="typography-body-sm text-gray-500">Select an item from the tree to view its changes.</p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          </div>
          <div className="px-6 py-3 border-t bg-gray-50/50 backdrop-blur-sm">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2 typography-body-sm text-gray-600">
                {selectedNodeId ? (
                  <div className="flex items-center gap-2">
                    <Check className="w-4 h-4 text-green-500" />
                    <span>Ready for review</span>
                  </div>
                ) : (
                  <span>Select an item to start review</span>
                )}
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => setShowCloseModal(true)}
                  className="px-3 py-1 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-all duration-200 typography-body-sm"
                >
                  Reject
                </button>
                <button
                  onClick={onApprove}
                  disabled={!selectedNodeId}
                  className="px-3 py-1 bg-primary text-white rounded-md hover:bg-primary-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed typography-body-sm"
                >
                  Approve
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
      {showCloseModal && <CloseModal onReject={onClose} onClose={() => setShowCloseModal(false)} />}
    </div>
  );
};

ReconfigModal.propTypes = {
  onClose: PropTypes.func.isRequired,
  onApprove: PropTypes.func.isRequired,
};

export default ReconfigModal;