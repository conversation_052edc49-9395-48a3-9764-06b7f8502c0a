"use client"
import React, { useState, useEffect, useContext} from "react";
import axios from "axios";
import { usePathname } from 'next/navigation';
import { TopBarContext } from '../Context/TopBarContext';
import { PanelContext } from "../Context/PanelContext";
import { getHeadersRaw } from "@/utils/api";

const ConfirmationModal = ({
  showModal,
  functionCallData,
  onCancel,
  onConfirm,
}) => {
  const [successMessage, setSuccessMessage] = useState("");
  const [FailureMessage, setFailureMessage] = useState("");
  const [isButtonDisabled, setIsButtonDisabled] = useState(false);
  const [formData, setFormData] = useState({});
  const { updateTabTitle,setActiveTab,tabs ,setTabs} = useContext(TopBarContext);
  const { getProject } = useContext(PanelContext);
  const pathname = usePathname();
  const [id] = pathname.split("/").slice(2); // Extract ID from URL

  const getDiscussionIdFromUrl = () => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get('discussion_id');
    }
    return null;
  };

  useEffect(() => {
    if (functionCallData && functionCallData.details) {
      const initialFormData = {};
      Object.keys(functionCallData.details).forEach((key) => {
        initialFormData[key] = functionCallData.details[key];
      });
      setFormData(initialFormData);
    }
  }, [functionCallData]);

  const handleInputChange = (key, value) => {
    setFormData((prevFormData) => ({
      ...prevFormData,
      [key]: value,
    }));
  };

  // 
  const handleConfirmation = async (confirmed) => {
    setIsButtonDisabled(true); // Disable the button when clicked
    const discussionId = getDiscussionIdFromUrl();

    let { operation, type, details } = functionCallData;
    
    // Handle rollback operation separately - no API call needed, just callback
    if (operation === "rollback") {
      setIsButtonDisabled(false);
      if (confirmed) {
        onConfirm("Rollback confirmed");
      }
      onCancel(); // Close the modal
      return;
    }
    
    try {
      let url = `${process.env.NEXT_PUBLIC_API_URL}/conversation/confirmation/${functionCallData.task_id}?confirm=${confirmed}`;

      // Add discussion_id to the URL if it exists
      if (discussionId) {
        url += `&discussion_id=${discussionId}`;
      }

      const response = await axios.post(
        url,
        {'node_data': formData},
        {
          headers: getHeadersRaw()
        }
      );


      if (response.status === 200) {
        // Parse the stringified JSON in response.config.data
        const parsedConfigData = JSON.parse(response.config.data);

        // Extract the "Name" field from node_data
        const updatedTitle = parsedConfigData.node_data?.Name;

        setIsButtonDisabled(false);
        setSuccessMessage("Successfully completed!");
        setTimeout(async () => { // Ensure async here
          onCancel();
          setSuccessMessage("");
          onConfirm("Executed Successfully");

          if (operation === "delete") {
            window.location.href = "/home?tab=recentprojects";
          } else if (operation === "update") {

              updateTabTitle(id, updatedTitle); // Update title
  
            window.location.reload();
          }
        }, 2000);

      } else {
        setIsButtonDisabled(false);
        
        setFailureMessage("Error in processing query");
        setTimeout(() => {
          onCancel();
          setFailureMessage("");
          onConfirm("Execution failed. Please try again");
        }, 3000);
      }
    } catch (error) {
      setIsButtonDisabled(false);
      setFailureMessage("Error in processing query");
      setTimeout(() => {
        onCancel();
        setFailureMessage("");
        onConfirm("Execution failed. Please try again");
      }, 3000);
      
    }
  };

  const renderModalContent = () => {
    const { operation, type, details } = functionCallData;
    

    switch (operation) {
      case "delete":
        return (
          <>
            <h3
              className="text-xl font-semibold text-gray-900"
              id="modal-title"
            >
              Confirm Delete
            </h3>
            <div className="mt-4">
              <p className="mb-4 text-gray-700">
                Are you sure you want to delete the {type} &quot;{details.Name}
                &quot; with ID &quot;{details.Id}&quot;?
              </p>
            </div>
          </>
        );
        case "update":
          return (
            <>
              <h3 className="text-xl font-semibold text-gray-900" id="modal-title">
                Edit {type}
              </h3>
              <div className="mt-4">
              {Object.entries(details).map(([key, value]) => (
                <div key={key} className="mb-4 flex items-center">
                  <label htmlFor={key} className="block typography-body-sm font-weight-medium text-gray-700 w-1/3">
                    {key}
                  </label>
                  {key === "Description" || key === "Scope" ? (
                    <textarea
                      id={key}
                      name={key}
                      value={formData[key] || ""}
                      onChange={(e) => handleInputChange(key, e.target.value)}
                      className="mt-1 focus:ring-primary focus:border-primary block shadow-sm sm:text-md border-gray-300 rounded-md w-full"
                      readOnly={false}
                    />
                  ) : (
                    <input
                      type="text"
                      id={key}
                      name={key}
                      value={formData[key] || ""} // Use formData for value
                      onChange={(e) => handleInputChange(key, e.target.value)} // Handle change
                      className="mt-1 focus:ring-primary focus:border-primary block shadow-sm sm:typography-body-sm border-gray-300 rounded-md w-full"
                    />
                  )}
                </div>
              ))}

              </div>
            </>
          );
      case "create":
        return (
          <>
            <h3
              className="text-xl font-semibold text-gray-900 text-center"
              id="modal-title"
            >
              Create {type}
            </h3>
            <div className="mt-6">
              {Object.entries(details).map(([key, value]) => (
                <div key={key} className="mb-4 flex items-center">
                  <label
                    htmlFor={key}
                    className="block text-sm font-medium text-gray-700 w-1/3"
                  >
                    {key}
                  </label>
                  {key === "Description" ? (
                    <textarea
                      id={key}
                      name={key}
                      value={formData[key] || ""}
                      onChange={(e) => handleInputChange(key, e.target.value)}
                      className="mt-1 focus:ring-primary focus:border-primary block shadow-sm sm:text-md border-gray-300 rounded-md w-full"
                      readOnly={false}
                    />
                  ) : (
                    <input
                      type="text"
                      id={key}
                      name={key}
                      value={formData[key] || ""}
                      onChange={(e) => handleInputChange(key, e.target.value)}
                      className="mt-1 focus:ring-primary focus:border-primary block shadow-sm sm:text-md border-gray-300 rounded-md w-full"
                      readOnly={false}
                    />
                  )}
                </div>
              ))}
            </div>
          </>
        );
      case "rollback":
        return (
          <>
            <h3
              className="text-xl font-semibold text-gray-900"
              id="modal-title"
            >
              Confirm Rollback
            </h3>
            <div className="mt-4">
              <p className="mb-4 text-gray-700">
                Are you sure you want to rollback to message ID &quot;{details["Message ID"]}&quot;?
              </p>
              <p className="mb-4 typography-body-sm text-gray-600">
                This will stash all current changes in the codebase and rollback to the state before this message was processed.
              </p>
            </div>
          </>
        );
      default:
        return null;
    }
  };

  const handleCancel = () => {
    // handleConfirmation(false)
    setIsButtonDisabled(false); // Enable the button when cancel is clicked
    onCancel(); // Simply close the modal without confirming
  };

  return (
    <>
      {showModal && (
        <div className="fixed inset-0 bg-black/5 flex items-center justify-center p-4 z-50 backdrop-blur-sm">
          <div className="bg-white rounded-xl shadow-xl w-full max-w-lg transform transition-all duration-200">
            <div className="px-6 py-4 border-b border-gray-200">
              {successMessage && (
                <div className="bg-green-600 text-lg border border-green-400 text-white px-4 py-3 rounded relative mb-4">
                  <span className="block sm:inline">{successMessage}</span>
                </div>
              )}
              {FailureMessage && (
                <div className="bg-red-600 border text-lg border-red-400 text-white px-4 py-3 rounded relative mb-4">
                  <span className="block sm:inline">{FailureMessage}</span>
                </div>
              )}
              <div>
                {renderModalContent()}
              </div>
            </div>

            <div className="px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-xl">
              <div className="flex justify-end gap-3">
                <button
                  type="button"
                  onClick={handleCancel}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={() => handleConfirmation(true)}
                  disabled={isButtonDisabled}
                  className={`px-4 py-2 text-sm font-medium text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors ${isButtonDisabled
                    ? "bg-red-400 cursor-not-allowed"
                    : "bg-red-600 hover:bg-red-700"
                    }`}
                >
                  {functionCallData?.operation === "rollback" ? "Confirm Rollback" : "Confirm"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ConfirmationModal;
