import React from 'react';

const GraphModal = ({ isOpen, onClose, children, fullScreen }) => {
  if (!isOpen) return null;

  return (
    <div className={`fixed inset-0 z-50 ${fullScreen ? 'overflow-hidden' : 'overflow-auto'} bg-gray-900 bg-opacity-75`}>
      <div className="flex items-center justify-center min-h-screen text-center">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true"></div>

        <div
          className={`inline-block bg-white rounded-lg shadow-xl transform transition-all duration-300 ease-out ${
            fullScreen ? 'w-full h-full ' : 'w-[calc(100%-20px)] h-[calc(100%-20px)] m-[10px]'
          }`}
        >
          <div className="relative bg-white px-6 py-6 sm:py-8 mb-10"> {/* Added mb-10 for bottom margin */}
            {/* Close Button */}
            <button
              onClick={onClose}
              className="absolute top-4 right-4 text-gray-500 hover:text-gray-800 focus:outline-none focus:ring-2 focus:ring-gray-300 rounded-full transition ease-in-out duration-200"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default GraphModal;