"use client";

import { useState, useContext, useEffect } from "react";
import { Upload } from "lucide-react";
import Logo from "../../../public/logo/kavia_logo.svg";
import Image from "next/image";
import ConfigureModal from "../Modal/ConfigureModel";
import { StateContext } from "../Context/StateContext";
import { DiscussionChatContext } from "../Context/DiscussionChatContext";
import PropertiesRenderer from "../UiMetadata/PropertiesRenderer";
import {
  fetchNodeById,
 
  updateNodeByPriority

} from "@/utils/api";
import { ProjectSetupContext } from "../Context/ProjectSetupContext";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { AlertContext } from '../NotificationAlertService/AlertList';
import en from "@/en.json"

import StatusPanel from "@/components/StatusPanel/StatusPanel";
import { ExecutionContext } from '../Context/ExecutionContext';


export default function ProjectConfigurationStep({ projectData, onClose, setIsProjectConfig, setAutoConfigItems }) {
  const [configMethod, setConfigMethod] = useState("discussion");
  const projectIds = JSON.parse(
    localStorage.getItem("createdProjectIds") || "[]"
  );
  const [configureModel, setConfigureModel] = useState(false);
  const [loadingAutoConfigure, setLoadingAutoConfigure] = useState(false);
  const { openProjectContent } = useContext(DiscussionChatContext);
  const { showAlert } = useContext(AlertContext);
  const { projectId, showConfigModel, setShowConfigModel } = useContext(ProjectSetupContext);
  const flag = sessionStorage.getItem(`openProjectContent-${projectId}`);
  const [updatedProjectData, setUpdatedProjectData] = useState(projectData);
  const { configStatus, currentTaskId, setAutoNavigateEnabled } = useContext(ExecutionContext)
  const [taskStatus, setTaskStatus] = useState("Idle");

  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);

  // Effect to track search parameters
  useEffect(() => {
    // Check if discussion parameter exists
    const hasDiscussionParam = searchParams.has("discussion");
    const isCreatingProject = searchParams.has("is_creating_project");

    // If the discussion parameter was previously present but now removed
    if (!hasDiscussionParam && !isCreatingProject && projectId) {
      // Fetch the updated node data
      const fetchUpdatedNode = async () => {
        try {
          const nodeType = "project";
          const updatedNode = await fetchNodeById(projectId, nodeType);

          if (updatedNode) {
            setUpdatedProjectData(updatedNode);
            // Store flag to indicate project content has been opened
            sessionStorage.setItem(`openProjectContent-${projectId}`, "true");

            // If configuration_state is "configured", update MongoDB
            if (
              updatedNode.properties &&
              updatedNode.properties.configuration_state === "configured"
            ) {
              try {
                // Send just the minimal data needed
                const minimalData = {
                  id: updatedNode.id,
                  properties: {
                    configuration_state:
                      updatedNode.properties.configuration_state,
                  },
                };
                const result = await updateProjectNodeConfigurationStatus(
                  minimalData
                );

              } catch (error) {

              }
            }
          }
        } catch (error) {

        }
      };

      fetchUpdatedNode();
    }
  }, [searchParams, projectId]);

  useEffect(() => {
    // Set a timer to hide the loader after 2 seconds
    const timer = setTimeout(() => {
      setLoading(false); // Hide loader after 2 seconds
    }, 2000);

    // Clean up the timeout if the component unmounts
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (configStatus[currentTaskId]) {

      setTaskStatus(configStatus[currentTaskId].task_status || "Idle");
      setAutoNavigateEnabled(false)

    }
  }, [currentTaskId, configStatus[currentTaskId], projectId])

  // Add this effect to watch for task completion
  useEffect(() => {
    const fetchProjectDataOnComplete = async () => {
      if (taskStatus.toLowerCase() === "complete" && projectId) {
        try {
          // Fetch the latest project data when task is complete
          const updatedNode = await fetchNodeById(projectId, "project");
          if (updatedNode) {
            setUpdatedProjectData(updatedNode);
            setIsProjectConfig(true)
            // Store flag to indicate project content has been opened
            sessionStorage.setItem(`openProjectContent-${projectId}`, "true");

            // if (updatedNode.properties?.configuration_state === "configured") {
            //   showAlert("Project configuration completed successfully", "success");
            // }
          }
        } catch (error) {

          showAlert("Failed to fetch updated project configuration", "error");
        }
      }
    };

    fetchProjectDataOnComplete();
  }, [taskStatus, projectId]);

  const handleCloseModal = () => {
    setConfigureModel(false);
  };

  const handleConfigureClick = () => {
    setConfigMethod("auto");
    setAutoConfigItems(["project"]);
    setConfigureModel(true);
  };
  const handlePropertyUpdate = async (key, value) => {
    try {
      const response = await updateNodeByPriority(projectId, key, value);
     
      setUpdatedProjectData((prev) => ({
        ...prev,
        properties: {
          ...prev.properties,
          [key]: value
        }
      }));

      showAlert("Content updated successfully", "success");
    } catch (err) {
      showAlert("Failed to update content", "error");
    }
  };



  const { setIsVertCollapse } = useContext(StateContext);

  const updateProject = () => {
    setConfigMethod("discussion");
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", projectId);
    newSearchParams.set("node_type", "project");
    newSearchParams.set("is_creating_project", "true");

    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const LoaderComponent = () => {
    return (
      <div className="flex flex-col p-4">
        {/* Configuration Method Title */}
        <div className="mb-4">
          <div className="h-6 bg-gray-100 animate-pulse rounded-lg w-48"></div>
        </div>

        {/* Configuration Method Cards */}
        <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
          {/* Update via discussion card */}
          <div className="col-span-1 md:col-span-1 lg:col-span-2 border border-gray-200 rounded-lg p-6">
            {/* Card Header with Icon */}
            <div className="flex mb-4 items-center space-x-3">
              <div className="py-1 px-1.5 bg-gray-100 animate-pulse rounded-lg h-6 w-6"></div>
              <div className="h-6 bg-gray-100 animate-pulse rounded-lg w-40"></div>
            </div>

            {/* Card Content */}
            <div className="space-y-2">
              <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-full"></div>
              <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-full"></div>
              <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-5/6"></div>
              <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-4/6"></div>
            </div>
          </div>

          {/* Auto Configuration card */}
          <div className="col-span-1 md:col-span-1 lg:col-span-2 border border-gray-200 rounded-lg p-6">
            {/* Card Header with Icon */}
            <div className="flex mb-4 items-center space-x-3">
              <div className="py-1 px-1.5 bg-gray-100 animate-pulse rounded-lg h-6 w-6"></div>
              <div className="h-6 bg-gray-100 animate-pulse rounded-lg w-36"></div>
            </div>

            {/* Card Content */}
            <div className="space-y-2">
              <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-full"></div>
              <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-full"></div>
              <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-5/6"></div>
              <div className="h-3 bg-gray-100 animate-pulse rounded-lg w-4/6"></div>
            </div>
          </div>
        </div>
      </div>
    );
  };
   const shouldShowStatusPanel = () => {
    if (!showConfigModel) return false;
    const hasOngoingTask = currentTaskId && configStatus[currentTaskId];
    
    // Only show status panel for auto configuration tasks
    if (hasOngoingTask) {
      const taskInProgress = taskStatus.toLowerCase() === 'in_progress' || taskStatus.toLowerCase() === 'idle';
      const isConfigured = updatedProjectData?.properties?.configuration_state === "configured";
      const taskNotComplete = taskStatus.toLowerCase() !== 'complete';
      
      return taskInProgress || (isConfigured && taskNotComplete);
    }
    
    // For interactive configuration, never show status panel
    return false;
  };

  return (
    <div className='h-full  overflow-y-auto px-4"'>
      {shouldShowStatusPanel() ? (
      <StatusPanel />
    ) : (
      <>
      
        {configureModel && (
          <ConfigureModal
            id={projectId}
            type={"project"}
            isNodeType={"project"}
            requirementId={projectId}
            isCreateProject={true}
            setShowConfigModel={setShowConfigModel}
            closeModal={handleCloseModal}
            setLoadingAutoConfigure={setLoadingAutoConfigure}
            onSubmitSuccess={() => {
              showAlert(
                `project configuration initiated successfully`,
                "success"
              );
              setIsVertCollapse(false);
            }}
            onClose={onClose}
          />
        )}

      {loading ? (
        <LoaderComponent />
      ): updatedProjectData?.properties?.configuration_state === "configured" && (!currentTaskId || !configStatus[currentTaskId] || taskStatus.toLowerCase() === 'complete')? (
        <div>
          {/* Project Title */}
          {/* {updatedProjectData?.properties?.Title && (
            <div className="typography-body-lg font-weight-medium text-gray-800">
              Project Title: {updatedProjectData.properties.Title}
            </div>
          )} */}

              <PropertiesRenderer
                properties={updatedProjectData.properties}
                metadata={updatedProjectData?.ui_metadata}
                to_skip={["Type", "Title"]}
                onUpdate={handlePropertyUpdate}
              />
            </div>
          ) : (
            <>
              {/* Content */}
              <div className="flex-1 p-4 flex flex-col">
                {/* Configuration Method */}
                <div className="mb-6">
                  <h3 className="typography-body-lg font-weight-medium mb-4">Configuration Method</h3>

                  <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
                    {/* Update via discussion card - takes 2 slots */}
                    <div
                      className={`col-span-1 md:col-span-1 lg:col-span-2 border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md ${configMethod === "discussion"
                        ? "border-orange-500 bg-orange-50"
                        : "border-gray-200 hover:border-orange-200"
                        }`}
                      onClick={updateProject}
                    >
                      <div className="flex mb-4 items-center space-x-3">
                        <div className="py-1 px-1.5 bg-orange-100 rounded-lg flex items-center justify-center">
                          <Image
                            src={Logo}
                            alt="Logo"
                            width="16"
                            height="16"
                            className="text-orange-500"
                          />
                        </div>
                        <h4 className="typography-body-lg font-weight-medium">Interactive configuration</h4>
                      </div>

                      <p className="text-gray-600">
                        {en.ProjectUpdate}
                      </p>
                    </div>

                    <div
                      className={`col-span-1 md:col-span-1 lg:col-span-2 border rounded-lg p-6 cursor-pointer transition-all hover:shadow-md ${configMethod === "auto"
                        ? "border-orange-500 bg-orange-50"
                        : "border-gray-200 hover:border-orange-200"
                        }`}
                      onClick={handleConfigureClick}
                    >
                      <div className="flex mb-4 items-center space-x-3">
                        <div className=" p-1.5 bg-orange-100 rounded-lg flex items-center justify-center">
                          <Upload className="w-4 h-4  text-orange-500 text-bold" />
                        </div>
                        <h4 className="typography-body-lg font-weight-medium">Auto Configuration</h4>
                      </div>

                      <p className="text-gray-600">
                        {en.ProjectAutoConfig}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </>)}
    </div>
  );
}
