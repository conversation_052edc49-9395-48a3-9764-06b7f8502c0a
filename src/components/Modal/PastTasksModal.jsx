import React, { useState, useEffect, useContext } from 'react';
import TableComponent from "@/components/SimpleTable/ArchitectureTable";
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { FaTimes } from 'react-icons/fa';
import { formatUTCToLocal } from "@/utils/datetime";
import { updateSessionStorageBackHistory } from '@/utils/helpers';
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import EditableCell from '../SimpleTable/EditableCell';
import { updateTask } from '@/utils/batchAPI';
import { AlertContext } from '../NotificationAlertService/AlertList';

const Spinner = () => (
  <div className="flex justify-center items-center h-64">
    <div className="relative">
      <div className="w-12 h-12 rounded-full border-4 border-gray-200"></div>
      <div className="w-12 h-12 rounded-full border-4 border-t-primary animate-spin absolute top-0 left-0"></div>
    </div>
  </div>
);

const PastTasksModal = ({
  isOpen,
  onClose,
  tasks = [],
  totalCount,
  limit,
  skip,
  onPageChange,
  onLimitChange,
  title = 'Past Code Generation Tasks',
  isLoading,
}) => {
  const [pastTasks, setPastTasks] =useState(tasks)
  const [loading, setLoading] = useState(true);
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const {showAlert} = useContext(AlertContext)
  // Fetching tasks when the modal is opened
  useEffect(() => {
    if (isOpen) {
      setLoading(true);
      const timer = setTimeout(() => {
        setLoading(false); // Simulate fetching data
      }, 2000);
      return () => clearTimeout(timer); // Cleanup on unmount
    }
  }, [isOpen]);

  // Set loading to false when tasks are updated
  useEffect(() => {
    if (pastTasks.length > 0) {
      setLoading(false);
    }
  }, [pastTasks]);

  if (!isOpen) return null;

  const handleFieldUpdate = async (rowId, field, value) => {
    try {
      await updateTask(rowId, { [field]: value });
      const updatedData = pastTasks.map(item => 
        item._id === rowId ? {...item, [field]: value} : item
      );
      setPastTasks(updatedData);
      return Promise.resolve();
    } catch (error) {
      return Promise.reject(error);
    }
  };

  const headers = [
    { 
      key: 'session_name',
      label: 'Session Name',
      render: (value, row) => (
        <EditableCell
          value={value || 'Untitled Session'}
          field="session_name"
          rowId={row.id}
          onUpdate={handleFieldUpdate}
          onError={(error) => showAlert("Failed to update session name", "error")}
          onSuccess={(message) => showAlert(message, "success")}
        />
      )
    },
    // { 
    //   key: 'description',
    //   label: 'Description',
    //   render: (value, row) => (
    //     <div className="flex items-center justify-between gap-2">
    //       <div className="flex-1 truncate" title={value || 'No description'}>
    //         <EditableCell
    //           value={value || 'No description'}
    //           field="description"
    //           rowId={row.id}
    //           onUpdate={handleFieldUpdate}
    //           onError={(error) => showAlert("Failed to update description:" + error, "error")}
    //           onSuccess={(message) => showAlert(message, "success")}
    //         />
    //       </div>
    //     </div>
    //   )
    // },
    {
      key: 'start_time',
      label: 'Created at',
      render: (value) => value
    },
    {
      key: 'duration',
      label: 'Duration',
      render: (value) => value || '-'
    },
    {
      key: 'status',
      label: 'Status'
    }
  ];
  const sortableColumns = {
    session_name: true,
    start_time: true
  };
  
  const calculateDuration = (startTime, endTime) => {
    try {
      const start = new Date(startTime);
      const end = new Date(endTime);
  
      // Calculate difference in milliseconds
      const diffMs = end.getTime() - start.getTime();
  
      // Convert to various units
      const seconds = Math.floor(diffMs / 1000);
      const minutes = Math.floor(seconds / 60);
      const hours = Math.floor(minutes / 60);
      const days = Math.floor(hours / 24);
      const weeks = Math.floor(days / 7);
      const years = Math.floor(days / 365.25);
  
      // Calculate remaining values
      const remainingWeeks = Math.floor((days % 365.25) / 7);
      const remainingDays = Math.floor(days % 7);
      const remainingHours = Math.floor(hours % 24);
      const remainingMinutes = Math.floor(minutes % 60);
      const remainingSeconds = Math.floor(seconds % 60);
  
      // Return appropriate unit based on duration with granular details
      if (years >= 1) {
        return `${years}y ${remainingWeeks}w`;
      } else if (weeks >= 1) {
        return `${weeks}w ${remainingDays}d`;
      } else if (days >= 1) {
        return `${days}d ${remainingHours}h`;
      } else if (hours >= 1) {
        return `${hours}h ${remainingMinutes}m`;
      } else if (minutes >= 1) {
        return `${minutes}m ${remainingSeconds}s`;
      } else {
        return `<1m`;
      }
    } catch (error) {
      
      return '0';
    }
  };
  const formattedTasks = pastTasks.map(task => {
    const startTime = task.start_time;
    const endTime = task.messages && task.messages.length > 0 
      ? task.messages[task.messages.length - 1].timestamp 
      : (task.status === 'RUNNING' ? new Date().toISOString() : startTime);
    return{
    id: task._id,
    job_id: task.job_id || task._id,
    status: task.status || 'in progress',
    start_time: formatUTCToLocal(task.start_time) || formatUTCToLocal(new Date()),
    session_name: task.session_name || 'Untitled',
    description: task.description || 'No description',
    messages_length: Array.isArray(task.messages) ? task.messages.length : 0,
    duration: `${calculateDuration(startTime, endTime)}`,
    }
  });

  const handleRowClick = (jobId) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("task_id", jobId);
    updateSessionStorageBackHistory();
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };



  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40">
      <div className="bg-white rounded-lg shadow-lg w-[90%] h-[90%] flex flex-col">
        <div className="p-4 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-white z-10">
          <h4 className="user-panel-sub-head">{title}</h4>
          <button
            onClick={onClose}
            className="w-8 h-8 flex items-center justify-center border border-gray-300 bg-white text-gray-500 hover:text-gray-700 hover:bg-gray-100 hover:border-gray-400 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            <FaTimes size={16} />
          </button>
        </div>
        <div className="flex-grow overflow-auto custom-scrollbar p-5">
          {loading ? (
            <Spinner />
          ) : (
            formattedTasks.length > 0 ? (
              <TableComponent
                // title="Past Tasks"
                totalCount={totalCount}
                data={formattedTasks}
                headers={headers}
                sortableColumns={sortableColumns}
                onRowClick={handleRowClick}
                component = "code-generation"
                onPageChange={onPageChange}
                onPageSizeChange={onLimitChange}
                isLoading={isLoading}
              />
            ) : (
              <div className="text-center flex justify-center  items-center"><EmptyStateView type="discussions" onClick={() => {}} /></div> 
            )
          )}
        </div>
      </div>
    </div>
  );
};

export default PastTasksModal;
