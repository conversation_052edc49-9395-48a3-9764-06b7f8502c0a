import React from 'react';
import { cn } from "@/lib/utils";
import { RefreshCw,TriangleAlert   } from 'lucide-react';

interface ErrorViewProps {
  title?: string;
  message?: string;
  showRetryButton?: boolean;
  onRetry?: () => void;
  className?: string;
  panelType?: 'main' | 'side';
}

const ErrorView: React.FC<ErrorViewProps> = ({
  title = 'Unable to Load Chats',
  message = 'We had trouble loading your chats. Lets try refreshing.',
  showRetryButton = true,
  onRetry,
  className = '',
  panelType = 'side'
}) => {
  const isPanelMain = panelType === 'main';

  return (
    <div className={cn(
      "flex flex-col items-center justify-center gap-2",
      isPanelMain ? "py-4 px-4 mt-12" : "p-3 m-6 h-2/6",
      
      className
    )}>
      <TriangleAlert  className="relative text-red-500 w-12 h-12 transform transition-transform hover:scale-110 duration-300" />

      <div className="text-center">
        <h3 className="typography-body-lg font-weight-semibold text-red-600 mb-0.5">
          {title}
        </h3>

        <p className="typography-body-sm text-red-500">
          {message}
        </p>

        {showRetryButton && onRetry && (
          <button
            onClick={onRetry}
            className="mt-2 inline-flex items-center px-4 py-2 rounded-md
              typography-body font-weight-medium 
              bg-red-50 text-red-600 hover:bg-red-100
              transition-colors duration-200"
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Try Again
          </button>
        )}
      </div>
    </div>
  );
};

export default ErrorView;