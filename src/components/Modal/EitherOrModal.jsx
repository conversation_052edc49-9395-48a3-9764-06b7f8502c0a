import React from "react";

const EitherOrModal = ({ 
  isOpen, 
  onClose, 
  onAction, 
  isProcessing, 
  type, 
  buttons = [
    { name: 'Cancel', className: 'bg-gray-200 text-gray-800 hover:bg-gray-300' },
    { name: 'Confirm', className: 'bg-red-600 text-white hover:bg-red-700' }
  ]
}) => {
  if (!isOpen) return null;

  const getTextContent = () => {
    switch (type) {
      case "project":
        return "Are you sure you want to delete this project from Kavia?";
      case "task":
        return "Are you sure you want to delete this task from Kavia?";
      case "requirements":
        return "Are you sure you want to delete this requirement from Kavia?";
      case "discussion":
        return "Are you sure you want to delete this discussion from Kavia?";
      case "user":
        return "Are you sure you want to delete this user from Kavia?";
      case "notification":
        return "Are you sure you want to delete this notification from Kavia?";
      case "multiple-requirements":
        return "Are you sure you want to delete these requirements from Kavia?";
      case "task-approval":
        return "You already have a task in progress.Please choose the Action";
      default:
        return "Are you sure you want to perform this action?";
    }
  };

  const getTextWarning = () => {
    switch(type){
        
        case "task-approval":
            return "Overwrite task will delete existing task and recreate new one";

        default:
            return `By performing this action, related data may also be affected.`;
    }
    
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={onClose}></div>
      <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6 sm:p-8 z-50">
        <div className="flex justify-between items-center border-b pb-3 mb-4">
          <h2 className="typography-body-lg sm:typography-heading-4 font-weight-semibold text-center" id="modal-title">
            Task Approval
          </h2>
          <button
            onClick={onClose}
            className="text-gray-600 hover:text-gray-800"
            aria-label="Close"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-5 h-5 sm:w-6 sm:h-6"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18 18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div className="mb-4">
          <p className="typography-body-sm sm:typography-body">
            {getTextContent()}
          </p>
        </div>
        <div
          className="flex p-3 sm:p-4 mb-4 typography-body-sm sm:typography-body text-red-800 rounded-lg bg-red-50"
          role="alert"
        >
          <svg
            className="flex-shrink-0 inline w-10 h-12 me-2 sm:me-3 mt-[2px]"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z" />
          </svg>
          <span className="sr-only">Warning</span>
          <div>
            <span className="font-weight-medium">
              {getTextWarning()}
            </span>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row justify-center space-y-2 sm:space-y-0 sm:space-x-4">
          {buttons.map((button, index) => (
            <button
              key={index}
              onClick={index === 0 ? onClose : onAction}
              className={`py-2 px-4 rounded-md flex items-center justify-center ${button.className}`}
              disabled={index === 1 && isProcessing}
              aria-label={isProcessing && index === 1 ? "Processing" : button.name}
            >
              {isProcessing && index === 1 ? (
                <>
                  Processing...
                </>
              ) : (
                <>
                  {button.name}
                </>
              )}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default EitherOrModal;
