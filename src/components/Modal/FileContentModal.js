// FileContentModal.js
import React from 'react';

const FileContentModal = React.memo(({ file, onClose }) => {
  const handleOutsideClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes < 1024) return bytes + ' bytes';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(2) + ' KB';
    else if (bytes < 1073741824) return (bytes / 1048576).toFixed(2) + ' MB';
    else return (bytes / 1073741824).toFixed(2) + ' GB';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" onClick={handleOutsideClick}>
      <div className="bg-white rounded-lg w-full max-w-4xl h-[90vh] flex flex-col shadow-xl">
        <div className="p-6 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-white z-10">
          <h2 className="typography-heading-2 font-weight-bold truncate max-w-[60%]">{file.file_name}</h2>
          <div className="flex items-center space-x-6">
            <span className="typography-body font-weight-semibold text-gray-600">{file.file_kind}</span>
            {file.file_size && <span className="typography-body font-weight-semibold text-gray-600">{formatFileSize(file.file_size)}</span>}
            <button
              onClick={(e) => {
                e.stopPropagation();
                onClose();
              }}
              className="text-gray-600 hover:text-gray-800 transition-colors"
              aria-label="Close"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
        <div className="flex-grow overflow-auto p-6">
          {file.file_kind === "image" ? (
            <div className="flex items-center justify-center h-full">
              <img src={file.preview_url || file.original_url} alt={file.file_name} className="max-w-full max-h-full object-contain" />
            </div>
          ) : (
            <pre className="whitespace-pre-wrap typography-body font-weight-medium text-gray-800 bg-gray-50 p-4 rounded-lg shadow-inner overflow-auto max-h-full">
              {file.extracted_content}
            </pre>
          )}
        </div>
      </div>
    </div>
  );
});
FileContentModal.displayName="FileContentModal"
export default FileContentModal;