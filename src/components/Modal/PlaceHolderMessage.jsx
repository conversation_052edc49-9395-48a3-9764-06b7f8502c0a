import React from "react";
import { FileText, Component } from "lucide-react";

const PlaceholderMessage = ({ type, message, subMessage }) => (
  <div className="group p-6 bg-gradient-to-br from-gray-50 to-slate-50 rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-300">
    <div className="flex items-start space-x-4">
      <div className="p-3 bg-white rounded-lg shadow-sm">
        {type === 'interfaces' ? (
          <FileText className="w-6 h-6 text-gray-600" />
        ) : (
          <Component className="w-6 h-6 text-gray-600" />
        )}
      </div>
      <div className="flex-1">
        <h3 className="typography-body-lg font-weight-semibold text-gray-800 mb-1">{message}</h3>
        <p className="typography-body-sm text-gray-600">{subMessage}</p>
      </div>
    </div>
  </div>
);

export default PlaceholderMessage;