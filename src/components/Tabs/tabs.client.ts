'use client'
import Draggabill<PERSON> from "draggabilly";

const TAB_CONTENT_MARGIN = 0;
const TAB_CONTENT_OVERLAP_DISTANCE = 1;
const TAB_CONTENT_MIN_WIDTH = 24;
const TAB_CONTENT_MAX_WIDTH = 240;
const TAB_SIZE_SMALL = 84;
const TAB_SIZE_SMALLER = 60;
const TAB_SIZE_MINI = 48;
const noop = (_: any) => { };
const closest = (value: number, array: number[]) => {
  let closest = Infinity;
  let closestIndex = -1;
  array.forEach((v, i) => {
    if (Math.abs(value - v) < closest) {
      closest = Math.abs(value - v);
      closestIndex = i;
    }
  });
  return closestIndex;
};

const tabTemplate = `
      <div class="tabs-tab">
        <div class="tabs-tab-background">
          <svg version="1.1" xmlns="http://www.w3.org/2000/svg"><defs><symbol id="tabs-tab-geometry-left" viewBox="0 0 214 36"><path d="M17 0h197v36H0v-2c4.5 0 9-3.5 9-8V8c0-4.5 3.5-8 8-8z"/></symbol><symbol id="tabs-tab-geometry-right" viewBox="0 0 214 36"><use xlink:href="#tabs-tab-geometry-left"/></symbol></defs><svg width="52%" height="100%"><use xlink:href="#tabs-tab-geometry-left" width="214" height="36" class="tabs-tab-geometry"/></svg><g transform="scale(-1, 1)"><svg width="52%" height="100%" x="-100%" y="0"><use xlink:href="#tabs-tab-geometry-right" width="214" height="36" class="tabs-tab-geometry"/></svg></g></svg>
        </div>
        <div class="tabs-tab-content">
          <div class="tabs-tab-title"></div>
          <div class="tabs-tab-close"></div>
        </div>
      </div>
    `;

const defaultTapProperties = {
  title: "New tab",
};

export interface TabProperties {
  id: string;
  title: string;
  active?: boolean;
}

let instanceId = 0;

class Tabs {
  el!: HTMLElement;
  styleEl!: HTMLStyleElement;
  instanceId?: number;
  draggabillies: Draggabilly[];
  isDragging: any;
  draggabillyDragging: any;
  isMouseEnter: boolean = false;
  mouseEnterLayoutResolve: null | (() => void) = null

  constructor() {
    this.draggabillies = [];
  }

  init(el: HTMLElement) {
    this.el = el;
    this.instanceId = instanceId;
    this.el.setAttribute("data-tabs-tabs-instance-id", this.instanceId + "");
    instanceId += 1;
    this.setupCustomProperties();
    this.setupStyleEl();
    this.setupEvents();
    this.layoutTabs();
    this.setupDraggabilly();
  }

  emit(eventName: string, data: any) {
    this.el.dispatchEvent(new CustomEvent(eventName, { detail: data }));
  }

  setupCustomProperties() {
    this.el.style.setProperty(
      "--tab-content-margin",
      `${TAB_CONTENT_MARGIN}px`
    );
  }

  setupStyleEl() {
    this.styleEl = document.createElement("style");
    this.el.appendChild(this.styleEl);
  }

  setupEvents() {
    window.addEventListener("resize", this.onResize);
    this.el.addEventListener('mouseenter', () => {
      this.isMouseEnter = true;
    });
    this.el.addEventListener('mouseleave', this.onMouseLeave);
    document.addEventListener('visibilitychange', this.onMouseLeave);
    this.tabEls.forEach((tabEl) => this.setTabCloseEventListener(tabEl));
  }
  onResize = () => {
    this.cleanUpPreviouslyDraggedTabs();
    this.layoutTabs();
  }
  onMouseLeave = () => {
    this.isMouseEnter = false;
    if (this.mouseEnterLayoutResolve) {
      this.mouseEnterLayoutResolve();
      this.mouseEnterLayoutResolve = null;
    }
  }

  get tabEls() {
    return Array.prototype.slice.call(this.el.querySelectorAll(".tabs-tab"));
  }

  get tabContentEl() {
    return this.el.querySelector(".tabs-tabs-content")!;
  }

  get tabContentWidths() {
    const numberOfTabs = this.tabEls.length;
    if (numberOfTabs === 0) return [];

    const tabsContentWidth = this.tabContentEl!.clientWidth;
    const tabsCumulativeOverlappedWidth =
      (numberOfTabs - 1) * TAB_CONTENT_OVERLAP_DISTANCE;
    const targetWidth =
      (tabsContentWidth -
        2 * TAB_CONTENT_MARGIN +
        tabsCumulativeOverlappedWidth) /
      numberOfTabs;
    const clampedTargetWidth = Math.max(
      TAB_CONTENT_MIN_WIDTH,
      Math.min(TAB_CONTENT_MAX_WIDTH, targetWidth)
    );
    const flooredClampedTargetWidth = Math.floor(clampedTargetWidth);
    const totalTabsWidthUsingTarget =
      flooredClampedTargetWidth * numberOfTabs +
      2 * TAB_CONTENT_MARGIN -
      tabsCumulativeOverlappedWidth;
    const totalExtraWidthDueToFlooring =
      Math.max(0, tabsContentWidth - totalTabsWidthUsingTarget);

    const widths = [];
    let extraWidthRemaining = totalExtraWidthDueToFlooring;
    for (let i = 0; i < numberOfTabs; i += 1) {
      const extraWidth =
        flooredClampedTargetWidth < TAB_CONTENT_MAX_WIDTH &&
        extraWidthRemaining > 0
          ? 1
          : 0;
      widths.push(flooredClampedTargetWidth + extraWidth);
      if (extraWidthRemaining > 0) extraWidthRemaining -= extraWidth;
    }

    return widths;
  }

  get tabContentPositions() {
    const positions: number[] = [];
    const tabContentWidths = this.tabContentWidths;

    let position = TAB_CONTENT_MARGIN;
    tabContentWidths.forEach((width, i) => {
      const offset = i * TAB_CONTENT_OVERLAP_DISTANCE;
      positions.push(position - offset);
      position += width;
    });

    return positions;
  }

  get tabPositions() {
    const positions: number[] = [];

    this.tabContentPositions.forEach((contentPosition) => {
      positions.push(contentPosition - TAB_CONTENT_MARGIN);
    });

    return positions;
  }

  layoutTabs() {
    const tabContentWidths = this.tabContentWidths;
    this.tabEls.forEach((tabEl, i) => {
      const contentWidth = tabContentWidths[i];
      const width = contentWidth + 2 * TAB_CONTENT_MARGIN;

      tabEl.style.width = width + "px";
      tabEl.removeAttribute("is-small");
      tabEl.removeAttribute("is-smaller");
      tabEl.removeAttribute("is-mini");

      if (contentWidth < TAB_SIZE_SMALL) tabEl.setAttribute("is-small", "");
      if (contentWidth < TAB_SIZE_SMALLER) tabEl.setAttribute("is-smaller", "");
      if (contentWidth < TAB_SIZE_MINI) tabEl.setAttribute("is-mini", "");
    });

    const tabPositions = [...this.tabPositions];
    const containerWidth = this.tabContentEl!.clientWidth;
    const lastTabIndex = tabPositions.length - 1;

    if (lastTabIndex >= 0) {
      const lastTabPosition = tabPositions[lastTabIndex];
      const lastTabWidth = (tabContentWidths[lastTabIndex] || 0) + 2 * TAB_CONTENT_MARGIN;

      if (lastTabPosition + lastTabWidth > containerWidth) {
        const adjustment = Math.min(lastTabPosition, lastTabPosition + lastTabWidth - containerWidth + 2);
        for (let i = 0; i <= lastTabIndex; i++) {
          tabPositions[i] = Math.max(0, tabPositions[i] - adjustment);
        }
      }
    }

    let styleHTML = "";
    tabPositions.forEach((position, i) => {
      styleHTML += `
            .tabs-tabs[data-tabs-tabs-instance-id="${this.instanceId
        }"] .tabs-tab:nth-child(${i + 1}) {
              transform: translate3d(${position}px, 0, 0)
            }
          `;
    });
    this.styleEl.innerHTML = styleHTML;
  }

  createNewTabEl() {
    const div = document.createElement("div");
    div.innerHTML = tabTemplate;
    return div.firstElementChild;
  }

  addTab(
    tabProperties?: TabProperties,
    { animate = true, background = false } = {}
  ) {
    const tabEl = this.createNewTabEl() as HTMLElement;
    tabEl.oncontextmenu = (event) => {
      this.emit("contextmenu", { tabEl, event });
    };
    if (animate) {
      tabEl.classList.add("tabs-tab-was-just-added");
      setTimeout(
        () => tabEl.classList.remove("tabs-tab-was-just-added"),
        500
      );
    }

    tabProperties = Object.assign({}, defaultTapProperties, tabProperties);
    this.tabContentEl.appendChild(tabEl);
    this.setTabCloseEventListener(tabEl);
    this.updateTab(tabEl, tabProperties);
    this.emit("tabAdd", { tabEl });
    if (!background) this.setCurrentTab(tabEl);
    this.cleanUpPreviouslyDraggedTabs();
    this.layoutTabs();
    this.setupDraggabilly();
    return tabEl;
  }

  setTabCloseEventListener(tabEl: HTMLElement) {
    tabEl.querySelector(".tabs-tab-close")!.addEventListener("click", (_) => {
      _.stopImmediatePropagation();
      this.emit("tabClose", { tabEl });
    });
  }

  get activeTabEl() {
    return this.el.querySelector(".tabs-tab[active]");
  }

  hasActiveTab() {
    return !!this.activeTabEl;
  }

  setCurrentTab(tabEl: HTMLElement) {
    const activeTabEl = this.activeTabEl;
    if (activeTabEl === tabEl) return;
    if (activeTabEl) activeTabEl.removeAttribute("active");
    tabEl.setAttribute("active", "");
    this.emit("activeTabChange", { tabEl });
  }

  removeTab(tabEl: HTMLElement) {
    if (tabEl === this.activeTabEl) {
      if (tabEl.nextElementSibling) {
        this.setCurrentTab(tabEl.nextElementSibling as HTMLElement);
      } else if (tabEl.previousElementSibling) {
        this.setCurrentTab(tabEl.previousElementSibling as HTMLElement);
      }
    }
    tabEl.parentNode!.removeChild(tabEl);
    this.emit("tabRemove", { tabEl });
    this.cleanUpPreviouslyDraggedTabs();
    if (this.isMouseEnter) {
      if (!this.mouseEnterLayoutResolve) {
        new Promise<void>((resolve) => {
          this.mouseEnterLayoutResolve = resolve;
        }).then(() => this.layoutTabs())
      }
    } else {
      this.layoutTabs();
    }
    this.setupDraggabilly();
  }

  updateTab(tabEl: HTMLElement, tabProperties: TabProperties) {
    tabEl.querySelector(".tabs-tab-title")!.textContent = tabProperties.title;

    if (tabProperties.id) {
      tabEl.setAttribute("data-tab-id", tabProperties.id);
    }
  }

  cleanUpPreviouslyDraggedTabs() {
    this.tabEls.forEach((tabEl) =>
      tabEl.classList.remove("tabs-tab-was-just-dragged")
    );
  }

  setupDraggabilly() {
    const tabEls = this.tabEls;
    const tabPositions = this.tabPositions;

    if (this.isDragging) {
      this.isDragging = false;
      this.el.classList.remove("tabs-tabs-is-sorting");
      this.draggabillyDragging.element.classList.remove(
        "tabs-tab-is-dragging"
      );
      this.draggabillyDragging.element.style.transform = "";
      this.draggabillyDragging.dragEnd();
      this.draggabillyDragging.isDragging = false;
      this.draggabillyDragging.positionDrag = noop;
      this.draggabillyDragging.destroy();
      this.draggabillyDragging = null;
    }

    this.draggabillies.forEach((d) => d.destroy());
    tabEls.forEach((tabEl, originalIndex) => {
      const originalTabPositionX = tabPositions[originalIndex];
      const draggabilly = new Draggabilly(tabEl, {
        axis: "x",
        containment: this.tabContentEl,
      });
      this.draggabillies.push(draggabilly);
      draggabilly.on("pointerDown", (_) => {
        this.emit("tabClick", { tabEl });
      });
      draggabilly.on("dragStart", (_) => {
        this.isDragging = true;
        this.draggabillyDragging = draggabilly;
        tabEl.classList.add("tabs-tab-is-dragging");
        this.el.classList.add("tabs-tabs-is-sorting");
        this.emit("dragStart", {});
      });

      draggabilly.on("dragEnd", (_) => {
        this.isDragging = false;
        const finalTranslateX = parseFloat(tabEl.style.left);
        tabEl.style.transform = `translate3d(0, 0, 0)`;
        this.emit("dragEnd", {});
        requestAnimationFrame((_) => {
          tabEl.style.left = "0";
          tabEl.style.transform = `translate3d(${finalTranslateX}px, 0, 0)`;
          requestAnimationFrame((_) => {
            tabEl.classList.remove("tabs-tab-is-dragging");
            this.el.classList.remove("tabs-tabs-is-sorting");
            tabEl.classList.add("tabs-tab-was-just-dragged");
            requestAnimationFrame((_) => {
              tabEl.style.transform = "";
              this.layoutTabs();
              this.setupDraggabilly();
            });
          });
        });
      });

      draggabilly.on("dragMove", (_, __, moveVector) => {
        const tabEls = this.tabEls;
        const currentIndex = tabEls.indexOf(tabEl);
        const currentTabPositionX = originalTabPositionX + moveVector.x;
        const destinationIndexTarget = closest(
          currentTabPositionX,
          tabPositions
        );
        const destinationIndex = Math.max(
          0,
          Math.min(tabEls.length, destinationIndexTarget)
        );
        if (currentIndex !== destinationIndex) {
          this.animateTabMove(tabEl, currentIndex, destinationIndex);
        }
      });
    });
  }

  animateTabMove(
    tabEl: HTMLElement,
    originIndex: number,
    destinationIndex: number
  ) {
    if (destinationIndex < originIndex) {
      tabEl!.parentNode!.insertBefore(tabEl, this.tabEls[destinationIndex]);
    } else {
      tabEl!.parentNode!.insertBefore(tabEl, this.tabEls[destinationIndex + 1]);
    }
    this.emit("tabReorder", { tabEl, originIndex, destinationIndex });
    this.layoutTabs();
  }

  destroy() {
    window.removeEventListener('resize', this.onResize);
    document.removeEventListener('visibilitychange', this.onMouseLeave)
  }
}

export default Tabs;
