// TabInitializer.js

import { PanelContext } from "../Context/PanelContext";
import { useEffect, useContext } from "react";
import { usePathname } from "next/navigation";
import { TopBarContext } from "../Context/TopBarContext";

const TabInitializer = () => {
  const pathname = usePathname();
  const { addTab, tabExists } = useContext(TopBarContext);
  const { getProject } = useContext(PanelContext);

  useEffect(() => {
    const initializeTab = async () => {
      if (pathname.startsWith("/project/")) {
        const [, , projectId, tab] = pathname.split("/");
        const fullPath = `/project/${projectId}/${tab}`;

        if (!tabExists(projectId)) {
          try {
            const project = await getProject(parseInt(projectId, 10));
            if (project.message === "Node not found") {
              
              return; // Exit the function if the project node is not found
            }
            const tabTitle =
              project.properties?.Title ||
              project.properties?.Name ||
              "Project";
            addTab(tabTitle, fullPath);
          } catch (error) {
            
            addTab("Project", fullPath);
          }
        }
      }
    };

    initializeTab();
  }, [pathname]);

  return null;
};

export default TabInitializer;
