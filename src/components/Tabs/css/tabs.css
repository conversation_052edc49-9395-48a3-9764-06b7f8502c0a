.tabs-tabs {
  box-sizing: border-box;
  position: relative;
  font-size: var(--font-size-sm);
  height: 32px;
  background: hsl(var(--muted));
  font-weight: var(--font-weight-normal);
  overflow: hidden;
}
.tabs-tabs * {
  box-sizing: inherit;
  font: inherit;
}
.tabs-tabs .tabs-tabs-content {
  position: relative;
  width: 100%;
  height: 100%;
}
.tabs-tabs .tabs-tab {
  position: absolute;
  left: 0;
  height: 32px;
  width: 220px;
  border: 0;
  margin: 0;
  z-index: 1;
  pointer-events: none;
}
.tabs-tabs .tabs-tab,
.tabs-tabs .tabs-tab * {
  user-select: none;
  cursor: default;
}
/* Removed tab dividers */
.tabs-tabs .tabs-tab .tabs-tab-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}
.tabs-tabs .tabs-tab .tabs-tab-background > svg {
  width: 100%;
  height: 100%;
}
.tabs-tabs .tabs-tab .tabs-tab-background > svg .tabs-tab-geometry {
  fill: hsl(var(--muted));
}
.tabs-tabs .tabs-tab[active] {
  z-index: 5;
  background: hsl(var(--background));
}
.tabs-tabs .tabs-tab[active] .tabs-tab-background > svg .tabs-tab-geometry {
  fill: hsl(var(--background));
}
.tabs-tabs .tabs-tab[active] .tabs-tab-content {
  border-top: 3px solid hsl(var(--semantic-gray-700));
  padding-top: 3px; /* Reduced from 5px to prevent text cutoff */
  color: hsl(var(--semantic-gray-900));
}
.tabs-tabs .tabs-tab:not([active]) .tabs-tab-close {
  opacity: 0 !important;
}
.tabs-tabs .tabs-tab:not([active]):hover .tabs-tab-close {
  opacity: 1 !important;
  transition: ease-in-out;
}
.tabs-tabs .tabs-tab:not([active]) .tabs-tab-background {
  transition: opacity 0.2s ease;
  opacity: 0;
}
@media (hover: hover) {
  .tabs-tabs .tabs-tab:not([active]):hover {
    z-index: 2;
  }
  .tabs-tabs .tabs-tab:not([active]):hover .tabs-tab-background {
    opacity: 1;
  }
}
.tabs-tabs .tabs-tab.tabs-tab-was-just-added {
  top: 10px;
  animation: tabs-tab-was-just-added 120ms forwards ease-in-out;
  transition: none;
}
.tabs-tabs .tabs-tab .tabs-tab-content {
  position: absolute;
  display: flex;
  align-items: center;
  top: 0;
  bottom: 0;
  left: 0;
  padding: 8px 10px;
  right: var(--tab-content-margin);
  overflow: hidden;
  pointer-events: all;
}
.tabs-tabs .tabs-tab[is-mini] .tabs-tab-content {
  padding-left: 2px;
  padding-right: 2px;
}
/* Removed favicon styles */
.tabs-tabs .tabs-tab .tabs-tab-title {
  flex: 1;
  display: flex;
  align-items: center;
  overflow: hidden;
  white-space: nowrap;
  margin-left: 8px;
  color: hsl(var(--muted-foreground));
  font-weight: var(--font-weight-medium);
  -webkit-mask-image: linear-gradient(90deg, black 0%, black calc(100% - 24px), transparent);
  mask-image: linear-gradient(90deg, black 0%, black calc(100% - 24px), transparent);
}
.tabs-tabs .tabs-tab[is-small] .tabs-tab-title {
  margin-left: 0;
}
/* Removed favicon + title styles */
.tabs-tabs .tabs-tab[is-mini] .tabs-tab-title {
  display: none;
}
.tabs-tabs .tabs-tab[active] .tabs-tab-title {
  color: hsl(var(--semantic-gray-900));
  font-weight: var(--font-weight-semibold);
}
/* Removed drag handle styles */
.tabs-tabs .tabs-tab .tabs-tab-close {
  flex-grow: 0;
  flex-shrink: 0;
  position: relative;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path stroke='currentColor' strokeLinecap='square' strokeWidth='1.5' d='M0 0 L8 8 M8 0 L0 8'></path></svg>");
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 8px 8px;
  cursor: pointer;
  color: hsl(var(--muted-foreground));
}
@media (hover: hover) {
  .tabs-tabs .tabs-tab .tabs-tab-close:hover {
    background-color: hsl(var(--semantic-gray-200));
  }
  .tabs-tabs .tabs-tab .tabs-tab-close:hover:active {
    background-color: hsl(var(--semantic-gray-300));
  }
}
@media not all and (hover: hover) {
  .tabs-tabs .tabs-tab .tabs-tab-close:active {
    background-color: hsl(var(--semantic-gray-200));
  }
}
@media (hover: hover) {
  .tabs-tabs .tabs-tab:not([active]) .tabs-tab-close:not(:hover):not(:active) {
    opacity: 0.8;
  }
}
.tabs-tabs .tabs-tab[is-smaller] .tabs-tab-close {
  margin-left: auto;
}
.tabs-tabs .tabs-tab[is-mini]:not([active]) .tabs-tab-close {
  display: none;
}
.tabs-tabs .tabs-tab[is-mini][active] .tabs-tab-close {
  margin-left: auto;
  margin-right: auto;
}
@-moz-keyframes tabs-tab-was-just-added {
  to {
    top: 0;
  }
}
@-webkit-keyframes tabs-tab-was-just-added {
  to {
    top: 0;
  }
}
@-o-keyframes tabs-tab-was-just-added {
  to {
    top: 0;
  }
}
@keyframes tabs-tab-was-just-added {
  to {
    top: 0;
  }
}
.tabs-tabs.tabs-tabs-is-sorting .tabs-tab:not(.tabs-tab-is-dragging),
.tabs-tabs:not(.tabs-tabs-is-sorting) .tabs-tab.tabs-tab-was-just-dragged {
  transition: transform 120ms ease-in-out;
}
.tabs-tabs .tabs-tabs-bottom-bar {
  position: absolute;
  bottom: 0;
  height: 4px;
  left: 0;
  width: 100%;
  background: hsl(var(--background));
}