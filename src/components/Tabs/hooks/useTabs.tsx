//@ts-nocheck
'use client'
import React, {
  CSSProperties,
  forwardRef,
  useCallback,
  useEffect,
  useRef,
} from "react";
import ChromeTabsClz, { TabProperties } from "../tabs.client";
import { useLatest } from "./useLatest";

export type Listeners = {
  onTabActive?: (tabId: string) => void;
  onTabClose?: (tabId: string) => void;
  onTabReorder?: (tabId: string, fromIdex: number, toIndex: number) => void;
  onDragBegin?: () => void;
  onDragEnd?: () => void;
  onContextMenu?:(tabId: string, event: MouseEvent) => void;
};

const ChromeTabsWrapper = forwardRef<HTMLDivElement, { className?: string, darkMode?: boolean}>((props, ref) => {
  const classList = ['tabs-tabs'];
  if (props.darkMode) {
    classList.push('tabs-tabs-dark-theme');
  }
  if (props.className) {
    classList.push(props.className);
  }
  return (
    <div
      ref={ref}
      className={classList.join(' ')}
      style={{ "--tab-content-margin": "9px" } as CSSProperties}
    >
      <div className="tabs-tabs-content"></div>
      <div className="tabs-tabs-bottom-bar"></div>
    </div>
  );
});

export function useTabs(listeners: Listeners) {
  const ref = useRef<HTMLDivElement>(null);
  const chromeTabsRef = useRef<ChromeTabsClz | null>(null);
  
  const listenersLest = useLatest(listeners);

  useEffect(() => {
    const chromeTabs = new ChromeTabsClz();
    chromeTabsRef.current = chromeTabs;
    chromeTabs.init(ref.current as HTMLDivElement);
    return () => {
      chromeTabs.destroy();
    }
  }, []);

  // activated
  useEffect(() => {
    const listener = ({ detail }: any) => {
      const tabEle = detail.tabEl as HTMLDivElement;
      const tabId = tabEle.getAttribute("data-tab-id") as string;
      listenersLest.current.onTabActive?.(tabId);
    };
    const ele = chromeTabsRef.current?.el;
    ele?.addEventListener("tabClick", listener);
    return () => {
      ele?.removeEventListener("tabClick", listener);
    };
  }, []);

  useEffect(() => {
    const ele = chromeTabsRef.current?.el;
    const listener = ({ detail }: any) => {
      const { tabEl: tabEle, originIndex, destinationIndex } = detail;
      const tabId = tabEle.getAttribute("data-tab-id") as string;
      listenersLest.current.onTabReorder?.(tabId, originIndex, destinationIndex);
    };
    ele?.addEventListener("tabReorder", listener);
    return () => {
      ele?.removeEventListener("tabReorder", listener);
    };
  }, []);

  useEffect(() => {
    const ele = chromeTabsRef.current?.el;
    const listener = ({ detail }: any) => {
      const tabEle = detail.tabEl as HTMLDivElement;
      const tabId = tabEle.getAttribute("data-tab-id") as string;
      listenersLest.current.onTabClose?.(tabId);
    };
    ele?.addEventListener("tabClose", listener);
    return () => {
      ele?.removeEventListener("tabClose", listener);
    };
  }, []);

  useEffect(() => {
    const listener = () => {
      listenersLest.current.onDragBegin?.();
    };
    const ele = chromeTabsRef.current?.el;
    ele?.addEventListener("dragBegin", listener);
    return () => {
      ele?.removeEventListener("dragBegin", listener);
    };
  }, []);

  useEffect(() => {
    const ele = chromeTabsRef.current?.el;
    const listener = ({ detail }: any) => {
      const tabEle = detail.tabEl as HTMLDivElement;
      if (!tabEle) {
        return;
      }
      const tabId = tabEle.getAttribute("data-tab-id") as string;
      listenersLest.current.onContextMenu?.(tabId, detail.event);
    };
    ele?.addEventListener("contextmenu", listener);
    return () => {
      ele?.removeEventListener("contextmenu", listener);
    };
  }, []);

  useEffect(() => {
    const listener = () => {
      listenersLest.current.onDragEnd?.();
    };
    const ele = chromeTabsRef.current?.el;
    ele?.addEventListener("dragEnd", listener);
    return () => {
      ele?.removeEventListener("dragEnd", listener);
    };
  }, []);

  const addTab = useCallback((tab: TabProperties) => {
    chromeTabsRef.current?.addTab(tab);
  }, []);

  const removeTab = useCallback((tabId: string) => {
    const ele = ref.current?.querySelector(
      `[data-tab-id="${tabId}"]`
    ) as HTMLDivElement;
    if (ele) {
      chromeTabsRef.current?.removeTab(ele);
    }
  }, []);

  const activeTab = useCallback((tabId: string) => {
    const ele = ref.current?.querySelector(
      `[data-tab-id="${tabId}"]`
    ) as HTMLDivElement;
    if (ele !== chromeTabsRef.current?.activeTabEl) {
      chromeTabsRef.current?.setCurrentTab(ele);
    }
  }, []);

  const updateTab = useCallback((tabId: string, tab: TabProperties) => {
    const ele = ref.current?.querySelector(
      `[data-tab-id="${tabId}"]`
    ) as HTMLDivElement;
    if (ele) {
      chromeTabsRef.current?.updateTab(ele, { ...tab });
    } else {
      chromeTabsRef.current?.addTab(tab);
    }
  }, []);

  const updateTabByIndex = useCallback((index: number, tab: TabProperties) => {
    const tabs = ref.current?.querySelectorAll('.tabs-tab');
    if (tabs) {
      const ele = tabs.item(index) as HTMLDivElement;
      if (ele) {
        chromeTabsRef.current?.updateTab(ele, { ...tab });
      } else {
        chromeTabsRef.current?.addTab(tab);
      }
    }
  }, [])

  const Tabs = useCallback(function Tabs(props: { className?: string, darkMode?: boolean}) {
    return <ChromeTabsWrapper {...props} ref={ref} />;
  }, []);

  return {
    Tabs,
    addTab,
    updateTab,
    removeTab,
    activeTab,
    updateTabByIndex,
  };
}
ChromeTabsWrapper.displayName = 'ChromeTabsWrapper';
