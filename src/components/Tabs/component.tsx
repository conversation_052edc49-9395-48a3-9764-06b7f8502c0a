'use client'
import React, { useEffect, useRef } from "react";
import { Listeners, useTabs } from "./hooks/useTabs";
import isEqual from "lodash.isequal";
import { TabProperties } from "./tabs.client";
import { useLatest } from "./hooks/useLatest";
import { usePrevious } from "./hooks/usePrevious";
import { usePersistFn } from "./hooks/usePersistFn";
import "./css/tabs.css";

export type TabsProps = Listeners & {
  tabs: TabProperties[];
  className?: string;
  darkMode?: boolean;
};

export function Tabs({
  tabs,
  className,
  darkMode,
  onTabActive,
  onTabClose,
  onTabReorder,
  onContextMenu,
}: TabsProps) {
  const tabsLatest = useLatest(tabs);
  const previousTabs = usePrevious(tabs);

  const moveIndex = useRef({ tabId: "", fromIndex: -1, toIndex: -1 });

  const handleTabReorder = usePersistFn(
    (tabId: string, fromIndex: number, toIndex: number) => {
      const [dest] = tabsLatest.current.splice(fromIndex, 1);
      tabsLatest.current.splice(toIndex, 0, dest);
      const beforeFromIndex = moveIndex.current.fromIndex;
      moveIndex.current = {
        tabId,
        fromIndex: beforeFromIndex > -1 ? beforeFromIndex : fromIndex,
        toIndex,
      };
    }
  );

  const handleDragEnd = usePersistFn(() => {
    const { tabId, fromIndex, toIndex } = moveIndex.current;
    if (fromIndex > -1) {
      onTabReorder?.(tabId, fromIndex, toIndex);
    }
    moveIndex.current = {
      tabId: "",
      fromIndex: -1,
      toIndex: -1,
    };
  });

  const {
    Tabs,
    addTab,
    activeTab,
    removeTab,
    updateTab,
    updateTabByIndex,
  } = useTabs({
    onTabClose: (tabId) => {
      onTabClose?.(tabId); 
    },
    onTabActive: (tabId) => {
      activeTab(tabId);
      onTabActive?.(tabId);
      },
    onContextMenu,
    onDragEnd: handleDragEnd,
    onTabReorder: handleTabReorder,
  });

  useEffect(() => {
    const beforeTabs = previousTabs || [];
    if (!isEqual(beforeTabs, tabs)) {
      const retainTabs = beforeTabs.slice(tabs.length);
      retainTabs.forEach((tab) => {
        removeTab(tab.id);
      }); 

      tabs.forEach((tab, index) => {
        updateTabByIndex(index, tab);
      });

      tabs.forEach((tab) => {
        if (tab.active) {
          activeTab(tab.id);
        }
      });
    }
  }, [tabs]);

  return <Tabs className={className} darkMode={darkMode} />;
}
