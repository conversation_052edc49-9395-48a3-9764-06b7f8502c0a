* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
html,
body,
:root {
  height: 100%;
  line-height: 25px;
}
button {
  cursor: pointer;
}
.sidebar {
  padding: 20px;
  width: 300px;
  border-right: 1px solid hsl(var(--border));
}
.heading-title {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 2rem;
}
/* Arborist component */
.node-container,
.node-content {
  display: flex;
  height: 100%;
  align-items: center;
  width: 100%;
}
.node-content {
  cursor: pointer;
}
.node-content span.arrow {
  width: 20px;
  font-size: var(--font-size-xl);
  display: flex;
}
.node-content span.file-folder-icon {
  margin-right: 7px;
  display: flex;
  align-items: center;
  font-size: var(--font-size-xl);
}
.node-content span.node-text {
  flex: 1;
}
.node-content input[type="text"],
.node-content input[type="text"] {
  width: 97%;
  border: 1px solid hsl(var(--border));
  background: transparent;
  height: 22px;
}
.search-input:focus {
  border: 1px solid hsl(var(--border));
}
.file-actions {
  height: 100%;
  display: flex;
}
.file-actions button {
  cursor: pointer;
}
.folderFileActions {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-right: 10px;
}
.folderFileActions button {
  display: flex;
  align-items: center;
  color: hsl(var(--primary));
  background-color: inherit;
  border: none;
  font-size: var(--font-size-lg);
  height: 100%;
  width: 24px;
}
.node-container .file-actions .folderFileActions {
  visibility: hidden;
}
.node-container:hover .file-actions .folderFileActions {
  visibility: visible;
}
[role="treeitem"]:hover {
  background-color: var(--primary);
  color: white;
}
[role="treeitem"]:focus-within {
  background-color: var(--primary);
  outline: none;
}
/* Selected node */
.node-container.isSelected {
  background: var(--primary);
  color: white;
  border-radius: 0;
}
/* Right side content */
.content {
  flex: 1;
  padding: 32px;
}
.content ul {
  list-style: none;
}
.content ul li:nth-child(2),
.content ul li:nth-child(3) {
  margin-bottom: 1rem;
}
.content ul li:nth-child(3) {
  color: hsl(var(--foreground));
  font-weight: bold;
}
.indentLines {
  --indent-size: 15px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  display: flex;
  align-items: flex-start;
  height: 100%;
}
.indentLines > div {
  height: 100%;
  padding-left: 10px;
  border-right: 1px solid hsl(var(--border));
  margin-right: calc(var(--indent-size) - 10px - 1px);
}