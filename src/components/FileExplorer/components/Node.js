import { AiFillFolder, AiFillFile } from "react-icons/ai";
import { MdArrowRight, MdArrowDropDown, MdEdit } from "react-icons/md";
import { RxCross2 } from "react-icons/rx";
import "../styles/styles.css"

const INDENT_STEP = 15;

const Node = ({ node, style, dragHandle, tree, onFolderClick }) => {
  const CustomIcon = node.data.icon;
  const iconColor = node.data.iconColor;
  const indentSize = Number.parseFloat(`${style.paddingLeft || 0}`);
  const indentCount = Math.max(0, Math.floor(indentSize / INDENT_STEP));

  const handleNodeClick = () => {
    if (node.isInternal) {
      node.toggle();
      onFolderClick(node.data);
    }
  };

  const hasChildren = node.data.children && node.data.children.length > 0;

  return (
    <div
      className={`node-container ${node.state.isSelected ? "isSelected" : ""}`}
      style={style}
      ref={dragHandle}
    >
      <div className="indentLines">
        {new Array(indentCount).fill(0).map((_, index) => (
          <div key={index}></div>
        ))}
      </div>
      <div className="node-content" onClick={handleNodeClick}>
        {node.isInternal ? (
          <>
            <span className="arrow" style={{ visibility: hasChildren ? 'visible' : 'hidden' }}>
              {node.isOpen ? <MdArrowDropDown /> : <MdArrowRight />}
            </span>
            <span className="file-folder-icon">
              {CustomIcon ? (
                <CustomIcon color={iconColor ? iconColor : "#FFBB33"} />
              ) : (
                <AiFillFolder color="#f6cf60" />
              )}
            </span>
          </>
        ) : (
          <>
            <span className="arrow"></span>
            <span className="file-folder-icon">
              {CustomIcon ? (
                <CustomIcon color={iconColor ? iconColor : "#FFBB33"} />
              ) : (
                <AiFillFile color="#6bc7f6" />
              )}
            </span>
          </>
        )}
        <span className="node-text">
          {node.isEditing ? (
            <input
              type="text"
              defaultValue={node.data.name}
              onFocus={(e) => e.currentTarget.select()}
              onBlur={() => node.reset()}
              onKeyDown={(e) => {
                if (e.key === "Escape") node.reset();
                if (e.key === "Enter") node.submit(e.currentTarget.value);
              }}
              autoFocus
            />
          ) : (
            <span>{node.data.name}</span>
          )}
        </span>
      </div>

      <div className="file-actions">
        <div className="folderFileActions">
          <button onClick={() => node.edit()} title="Rename...">
            <MdEdit />
          </button>
          <button onClick={() => tree.delete(node.id)} title="Delete">
            <RxCross2 />
          </button>
        </div>
      </div>
    </div>
  );
};

export default Node;