'use client'
import { useRef, useState } from "react";
import { Tree } from "react-arborist";
import Node from "./Node";
import { TbFolderPlus } from "react-icons/tb";
import { AiOutlineFileAdd } from "react-icons/ai";

const FileExplorer = ({ 
  data, 
  onFolderSelect, 
  enableFolderCreation = true, 
  enableFileCreation = true,
  onCreateFolder,
  onCreateFile
}) => {
  const [term, setTerm] = useState("");
  const treeRef = useRef(null);

  const handleCreateFolder = () => {
    const newFolder = { id: `folder-${Date.now()}`, name: "New Folder", children: [] };
    treeRef.current.createInternal(newFolder);
    if (onCreateFolder) {
      onCreateFolder(newFolder);
    }
  };

  const handleCreateFile = () => {
    const newFile = { id: `file-${Date.now()}`, name: "New File" };
    treeRef.current.createLeaf(newFile);
    if (onCreateFile) {
      onCreateFile(newFile);
    }
  };

  const createFileFolder = (
    <>
      {enableFolderCreation && (
        <button
          onClick={handleCreateFolder}
          title="New Folder..."
        >
          <TbFolderPlus />
        </button>
      )}
      {enableFileCreation && (
        <button 
          onClick={handleCreateFile}
          title="New File..."
        >
          <AiOutlineFileAdd />
        </button>
      )}
    </>
  );

  const handleFolderClick = (folderData) => {
    onFolderSelect(folderData);
  };

  return (
    <div>
      {(enableFolderCreation || enableFileCreation) && (
        <div className="folderFileActions">{createFileFolder}</div>
      )}
      <Tree
        ref={treeRef}
        initialData={data}
        disableEdit
        
        width={260}
        height={1000}
        indent={24}
        rowHeight={32}
        searchTerm={term}
        searchMatch={(node, term) =>
          node.data.name.toLowerCase().includes(term.toLowerCase())
        }
      >
        {(props) => <Node {...props} onFolderClick={handleFolderClick} />}
      </Tree>
    </div>
  );
};

export default FileExplorer;