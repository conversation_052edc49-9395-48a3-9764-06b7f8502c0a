"use client";
import * as React from "react";
import { ChatContext } from "@/components/Context/ChatContext";
import { useContext } from "react";
const { useState, useEffect, useRef } = React;
import { fetchEventSource } from "@microsoft/fetch-event-source";
import { renderHTML } from "@/utils/helpers";
import { getCookie } from "@/utils/auth";
import ConfirmationModal from "@/components/Modal/ConfirmationModal"
import Image from "next/image";
import Cookies from 'js-cookie'
import { getHeadersRaw } from "@/utils/api";


export default function ChatPanel() {
  
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [functionCallData, setFunctionCallData] = useState(null);

  const userIdVal = Cookies.get('userId') //getting userId from the cookie


  const handleConfirmation = (data:any) => {
    addMessage({ text: data, sender: "AI" });
  };
  const { messages, setMessages, addMessage, removeLastMessage , discussionId, setDiscussionId } = useContext(ChatContext);
 
  const [currentMessage, setCurrentMessage] = useState("");
  let userId = userIdVal; 
  const endOfMessagesRef = useRef<HTMLDivElement>(null);
  const [username, setUsername] = useState("");

  useEffect(()=>{
    (async()=>{
      const email = await getCookie("email");
      setUsername(email?email:'User');
    })();
  })


  // Handle input change
  const handleInputChange = (event: any) => {
    setCurrentMessage(event.target.value);
  };

  const handleKeyPress = (event: any) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      handleFormSubmit(event);
    }
  };

  // Handle form submission
  const handleFormSubmit = (event: any) => {
    event.preventDefault();
    if (currentMessage.trim() !== "") {
      setLoading(true); // Start loading
      const newMessage = {
        id: Date.now(),
        text: currentMessage,
        sender: "User",
      };
      addMessage(newMessage);
      addMessage({ text: "Typing...", sender: "AI" });
      setCurrentMessage("");

    }
    handleMessageSubmit(currentMessage)
  };

  const handleMessageSubmit = async (message: any) => {
    if (loading) return; // Prevent multiple submissions

      let url = `${
        process.env.NEXT_PUBLIC_API_URL
      }/conversation/chat?user_id=${userId}&message=${encodeURIComponent(
        message
      )}`;


      if (discussionId) {
        url += `&discussion_id=${discussionId}`;
      }
  
      setLoading(true);

      const controller = new AbortController();
      controller.signal.addEventListener('abort', () => {
        // 
      });

      const eventSource = fetchEventSource(url, {
        headers: getHeadersRaw(),
        signal: controller.signal,
        openWhenHidden: true,
        onopen: (response) => {
          // 
          return Promise.resolve();
        },
        onmessage: async (event) => {
          try {
            
            let json = JSON.parse(event.data);
            const content = await renderHTML(json.content);
            // const content = json.content;

            if (json.discussion_id) {
              setDiscussionId(json.discussion_id);
            } else {
              removeLastMessage()
              if(content){
                addMessage({ text: content, sender: 'AI' });
              }
            }

            if (json.function_call) {
              let streamingResponse = "Asking for confirmation";
              addMessage({ text: streamingResponse, sender: 'AI' });
              if (json.function_call["function"] == "configure_node") {
                // handleModalToggle();
                
              } else {
                setFunctionCallData(json.function_call);
                setShowModal(true);
                
              }
            }
      
      
          } catch (error) {
            
            controller.abort();
            setLoading(false);
          }
        },
        onerror: (error) => {
          
          setLoading(false);
          
    
          setMessages((prevMessages:any) =>
            prevMessages
              .slice(0, -1)
              .concat({ text: "Error receiving data.", fromUser: false })
          );
          controller.abort() // Close the connection
          throw new Error("Error receiving data.");
          return null;
        },
        onclose: () => {
          setLoading(false);
          
          return Promise.resolve();
        },
      });
    }
  
    // Ensure the controller is only aborted once


  useEffect(() => {
    endOfMessagesRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const getImageSrc = (sender: any) => {
    if (sender === "AI") {
      return "https://i.ibb.co/ck64KsG/Frame-44.png"; // Kavia logo
    } else {
      return `https://ui-avatars.com/api/?name=${username}+${"Shetty"}&background=A371F7&color=FFFFFF`;
      // return `https://ui-avatars.com/api/?name=${data?.firstName}+${data?.lastName}&background=0D8ABC&color=A371F7`;
    }
  };



  return (
    <>
      <div className="flex  flex-col  min-h-[74vh] mt-2 max-h-[86vh]  p-2 ">
      {/* <div className="flex flex-col mt-7  min-h-[89vh] max-h-[89vh] "> */}
        {showModal && <ConfirmationModal 
        showModal={showModal}
        functionCallData={functionCallData}
        onCancel={() => setShowModal(false)}
        onConfirm={handleConfirmation}
        />
          }
        <div className="flex-1 rounded-xl p-4 typography-body-sm leading-6 text-slate-900 light:bg-slate-800 light:text-slate-300 sm:typography-body sm:leading-7  overflow-auto custom-scrollbar"
        style={{
          maxHeight: "calc(100vh - 30px)",
        }}>
          {messages.map((message:any) => (
            <div
              key={message.id}
              className={`flex flex-row px-2 py-4 sm:px-4 ${
                message.sender !== "User"
                  ? "mb-3 rounded-md bg-slate-100 px-2 py-3 light:bg-slate-900 sm:px-4"
                  : ""
              }`}
            >
              
               <Image
      className="mr-2 flex h-8 w-8 rounded-full sm:mr-4"
      src={getImageSrc(message.sender)}
      alt={message.sender === "AI" ? "AI Avatar" : "User Avatar"}
      width={32} // Adjust the width as needed
      height={32} // Adjust the height as needed
    />
              <div className="flex max-w-3xl items-center">
                <span dangerouslySetInnerHTML={{ __html: message.text }} />
              </div>
            </div>
          ))}
          <div ref={endOfMessagesRef} />
          {/* {true && <div className="flex justify-center p-4"><div className="spinner"></div></div>} */}
          {loading && (
          <div className="flex space-x-2 animate-pulse pl-5 pt-1 pb-5">
            <div className="w-2 h-2 bg-gray-600 rounded-full"></div>
            <div className="w-2 h-2 bg-gray-600 rounded-full"></div>
            <div className="w-2 h-2 bg-gray-600 rounded-full"></div>
          </div>
          )}
        </div>

        <form onSubmit={handleFormSubmit} className="mb-4">
          <label htmlFor="chat-input" className="sr-only">
            Enter your prompt
          </label>
          <div className="relative px-2">
            <textarea
              id="chat-input"
              className="block w-full resize-none rounded-xl border-none bg-slate-200 p-4 pr-16 typography-body-sm text-slate-900 shadow-md focus:outline-none focus:ring-2 focus:ring-primary-600 sm:typography-body"
              placeholder="Enter your prompt"
              rows={1}
              required
              value={currentMessage}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
            ></textarea>
            <button
              type="submit"
              disabled={loading}
              className="absolute bottom-1.5 right-2.5 rounded-lg bg-primary p-2 mr-1 typography-body-sm font-weight-medium text-slate-200 hover:bg-primary-800 focus:outline-none focus:ring-4 focus:ring-primary-300 sm:typography-body"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                aria-hidden="true"
                viewBox="0 0 24 24"
                strokeWidth="2"
                stroke="currentColor"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                <path d="M10 14l11 -11"></path>
                <path d="M21 3l-6.5 18a.55 .55 0 0 1 -1 0l-3.5 -7l-7 -3.5a.55 .55 0 0 1 0 -1l18 -6.5"></path>
              </svg>
              <span className="sr-only">Send message</span>
            </button>
          </div>
        </form>
      </div>
    </>
  );
}
