import React from "react";
import Tab from "@/components/BrowsePanel/Tab";

const Tabs = ({ tabs, activeTab, handleTabClick }) => {
  return (
    <div className="ml-2 m-1 text-color flex space-x-4 text-md overflow-x-auto">
      {tabs.map((tab) => (
        <Tab
          key={tab.id}
          icon={tab.icon}
          label={tab.label}
          isActive={activeTab === tab.id}
          onClick={() => handleTabClick(tab.id)}
        />
      ))}
    </div>
  );
};

export default Tabs;
