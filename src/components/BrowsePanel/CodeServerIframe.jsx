"use client"
import React, { useContext, useEffect, useRef, useState } from "react";
import { FaExpand, FaCompress,FaPlay, FaCircle } from "react-icons/fa";
import { Loading2 } from "../Loaders/Loading";
import {  getUserProjectURL } from "@/utils/api";
import { usePathname } from "next/navigation";
import { AlertContext } from "../NotificationAlertService/AlertList";

const CodeServerIframe = () => {
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [iFrameUrl, setIFrameUrl] = useState(null);
  const [logger,setLogger] = useState('');
  const iframeRef = useRef(null);
  const pathname = usePathname();
  const projectId = pathname.split("/")[3];
  const { showAlert } = useContext(AlertContext);

  const processStream = async () => {
    try {
      const response = await getUserProjectURL(projectId);
      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      const processStreaming = async () => {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split("}{").map((line, index, array) => {
            if (index > 0) line = "{" + line;
            if (index < array.length - 1) line = line + "}";
            return line;
          });

          for (const line of lines) {
            const data = JSON.parse(line);
            setLogger(data.message);

            if (data.end) {
              if (!data.error) {
                setTimeout(() => {
                  setIFrameUrl(data.url);
                  setLogger("Loading Code View...")
                }, 2000); // Short delay before opening the new tab
              } else {
                showAlert(data.message, 'danger');
                setIsLoading(false);
              }
              return;
            }
          }
        }
      };

      processStreaming();
    } catch (error) {
      setError(error.message);
      setIsLoading(false);
    }
  };


  useEffect(() => {
    return async() => {
    };
  }, []);

  const toggleFullScreen = () => {
    setIsFullScreen(!isFullScreen);
  };

  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  return (
    <div className={`${isFullScreen ? "fixed inset-0 z-50" : "relative"} py-10 bg-white`} style={{ height: isFullScreen ? '100vh' : '80vh' }}>
      <button
        onClick={toggleFullScreen}
        className="absolute right-4 top-4 z-50 bg-gray-800 text-white p-2 rounded-full shadow-lg"
      >
        {isFullScreen ? <FaCompress size={20} /> : <FaExpand size={20} />}
      </button>
      {(!isLoading && !iFrameUrl) && (
        <div className="absolute inset-0 flex items-center justify-center">
          <button
            onClick={()=>{
              setIsLoading(!isLoading);
              setLogger('Starting Workspace');
              processStream();
            }}
            className="flex center-4 top-4 z-50 bg-gray-800 text-white p-4 rounded-full shadow-lg text-center"
          >
            <FaPlay size={20} /> &nbsp;&nbsp;Start Workspace
          </button>
        </div>
      )}
      
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="flex flex-col items-center w-full">
            <Loading2 />
            <Loading2 />
            <button
              onClick={() => { setIsLoading(!isLoading) }}
              className="flex items-center justify-center typography-body-lg rounded bg-gray-200 text-black p-2 text-center m-2"
              style={{width:700}}
            >
              <FaCircle className="mr-2" /> 
              {logger}
            </button>
            <Loading2 />
            <Loading2 />
          </div>
        </div>
      )}
      
      {iFrameUrl && (
        <iframe
          ref={iframeRef}
          src={iFrameUrl}
          style={{ border: "none", height: '100%', width: "100%" }}
          sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
          className={`${isFullScreen ? "fixed inset-0" : "relative"}`}
          title="Code Server"
          onLoadedData={handleIframeLoad}
        ></iframe>
      )}
    </div>
  );
};

export default CodeServerIframe;
