import React, { useEffect } from "react";
import Image from "next/image";
import { useState, useContext } from "react";
import dynamic from "next/dynamic";
import { useParams, useRouter, useSearchParams, usePathname } from "next/navigation";
import BreadCrumbModel from "@/components/Modal/BreadCrumbModel";
import { LoadingSkeleton } from "../../Loaders/Loading";
import { StateContext } from "@/components/Context/StateContext";
import { ArchitectureContext } from "@/components/Context/ArchitectureContext";
import ConfigureModal from "@/components/Modal/ConfigureModel";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import graphicon from "../../../../public/images/graph_icon.svg";
import tableicon from "../../../../public/images/table_icon.svg";
import { FaCogs } from "react-icons/fa";
import en from "../../../en.json";
import ConfigureButtons from "@/components/ConfigureButtons/ConfigureButtons";
import EllipsisDropdown from "@/components/ElipsisDropdown/ElipsisDropDown";
import ComponentTable from "@/components/SimpleTable/ComponentsTable";
import Accordion2 from "./Accordian2";
import WithInterfaceTable from "@/components/SimpleTable/WithInterfaceTable";
import Button from "@/components/Buttons";
import { fetchArchitectureInterfacesWith, fetchArchitectureRelationships } from "@/utils/api";
import EnParser from '@/utils/enParser';
import PropertiesRenderer from "@/components/UiMetadata/PropertiesRenderer";
import { Loading2 } from "../../Loaders/Loading";
import Badge from "@/components/UIComponents/Badge/Badge"
const NoSSR = dynamic(() => import("../../Chart/MermaidChart"), {
  ssr: false,
});

const ToggleButton = ({ isToggled, toggle }) => (
  <button
    onClick={toggle}
    className={`flex items-center rounded-md border border-[#3e83ee] ${isToggled ? "m-1.5" : " "
      }`}
  >
    <div className="flex items-center space-x-2">
      <div className={`p-2 ${!isToggled ? "bg-[#dfeafc] text-[#3e83ee] rounded-sm" : ""}`}>
        <Image
          src={graphicon}
          alt="Left Icon"
          width={20}
          height={20}
          className={`${isToggled ? "opacity-50" : "opacity-100"}`}
          data-twe-toggle="tooltip"
          data-twe-ripple-init
          data-twe-ripple-color="light"
          data-twe-placement="right"
          title="Graph View"
        />
      </div>
      <div className={`p-2 ${isToggled ? "bg-[#dfeafc] text-[#3e83ee] rounded-sm" : ""}`}>
        <Image
          src={tableicon}
          alt="Table Icon"
          width={20}
          height={20}
          className={`${isToggled ? "opacity-100" : "opacity-50"}`}
          data-twe-toggle="tooltip"
          data-twe-ripple-init
          data-twe-ripple-color="light"
          data-twe-placement="right"
          title="Table View"
        />
      </div>
    </div>
  </button>
);


const ArchitectureDetails = ({ data }) => {
  const { dataType } = useContext(ArchitectureContext);
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const params = useParams();
  const { setIsVertCollapse } = useContext(StateContext);
  const { showAlert } = useContext(AlertContext);
  const [, setLoadingAutoConfigure] = useState(false);
  const [configureModel, setConfigureModel] = useState(false);
  const router = useRouter();
  const projectId = parseInt(pathname.split("/")[3], 10);
  const architectureId = params.architectureId;
  const [isToggled, setIsToggled] = useState(true);
  const toggle = () => setIsToggled(!isToggled);
  const [withInterfaceData, setWithInterfaceData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [architectureRelationships, setArchitectureRelationships] = useState(
    []
  );

  useEffect(() => {
    const fetchRelationships = async () => {
      setIsLoading(true);
      try {
        const pluralLowercaseDataType = dataType?.toLowerCase() + "s";
        const relationships = await fetchArchitectureRelationships(
          projectId,
          pluralLowercaseDataType
        );
        setArchitectureRelationships(relationships.implementations);
      } catch (error) {
        
      } finally {
        setIsLoading(false);
      }
    };

    fetchRelationships();
  }, [projectId, dataType]);

  const getCurrentArchitectureRelationships = () => {
    return (
      architectureRelationships.find(
        (impl) => impl.source_node.id.toString() === architectureId
      )?.target_nodes || []
    );
  };

  const ArchitectureRelationshipsTable = ({ relationships }) => {
    const router = useRouter();
    const handleRowClick = (node) => {
      const { buildProjectUrl } = require('@/utils/navigationHelpers');
      const organizationId = pathname.split("/")[1];
      const type = pathname.split("/")[2];
      router.push(
        buildProjectUrl(projectId, `architecture/architecture-requirement/${node.properties.Type}/${node.id}`, type, organizationId)
      );
    };

    return (
      <div className="overflow-x-auto">
        <table className="w-full  table-auto">
          <thead className="uppercase h-10 bg-orange-50 table-header">
            <tr className="mt-1">
              <th className="px-4 py-2">ID</th>
              <th className="px-6 py-1">TITLE</th>
              <th className="px-6 py-1">TYPE</th>
              <th className="px-6 py-1">DESCRIPTION</th>
              <th className="px-6 py-1">RELATION</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y table-val">
            {relationships.map((node) => (
              <tr
                key={node.id}
                className="cursor-pointer hover:bg-orange-50 transition-colors duration-150 p-4"
                onClick={() => handleRowClick(node)}
              >
                <td className="py-2 px-4 border-b text-center">{node.id}</td>
                <td className="py-2 px-4 border-b">
                  {node.properties.Title
                    ? node.properties.Title.length > 20
                      ? `${node.properties.Title.slice(0, 20)}...`
                      : node.properties.Title
                    : "N/A"}
                </td>
                <td className="py-2 px-4 border-b">
                  <span className="bg-orange-100 text-orange-700 rounded-xl px-2 py-0.5 typography-body-sm">
                    {node.properties.Type || "N/A"}
                  </span>
                </td>
                <td className="py-2 px-4 border-b">
                  {node.properties.Description
                    ? node.properties.Description.length > 100
                      ? `${node.properties.Description.slice(0, 30)}...`
                      : node.properties.Description
                    : "N/A"}
                </td>
                <td className="py-2 px-4 border-b">
                  <span className="bg-orange-100 text-orange-700 rounded-xl px-2 py-0.5 typography-body-sm">
                    {node.relationship.type || "N/A"}
                  </span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const data = await fetchArchitectureInterfacesWith(architectureId);
        setWithInterfaceData(data);
      } catch (e) {
        
      }
    };

    fetchData();
  }, [architectureId]);

  const handleUpdateArchitecture = () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", architectureId);
    newSearchParams.set("node_type", "Architecture");
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const isEmptyObject = (obj) => {
    return obj && Object.keys(obj).length === 0 && obj.constructor === Object;
  };

  if (!data || isEmptyObject(data)) {
    return <LoadingSkeleton />;
  }
  const handleCloseModal = () => setConfigureModel(false);

  const updateProps = {
    onUpdateClick: handleUpdateArchitecture,
    buttonText: "Update Architecture",
  };

  const configureProps = {
    isConfigurable: true,
    nodeId: architectureId,
    nodeType: "Architecture",
    setLoadingAutoConfigure: setLoadingAutoConfigure,
    onSubmitSuccess: () => {
      const successMessage = "Node Configured Successfully";
      showAlert(successMessage, "success");
      setIsVertCollapse(false);
    },
    buttonText: "Auto Configure",
  };

  const handleUpdateClick = (_e, component = false) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", params.architectureId);
    newSearchParams.set("node_type", "Architecture");
    if (component) newSearchParams.set("discussion_node_type", "component");
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const handleViewPastDiscussion = async (component = false) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("view_past_discussions", "true");
    newSearchParams.set("node_id", params.architectureId);
    const discussionType = component ? "component" : "architecture";
    newSearchParams.set("discussion_type", discussionType);
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };
  const handleBtn = () => {
    showAlert("Functionality was not implemented", "info");
  };

  return (
    <div className="flex flex-col  overflow-y-auto custom-scrollbar max-h-[74vh]">
      <div className=" border-2 rounded-lg mb-2  ">
        <div className="m-3 top-0 sticky bg-white  ">
          <div className="flex top-0  mb-3 -m-2  h-[80px] justify-between  items-center border-t border-r border-l pl-2 rounded-t ">
            <div className="flex items-center flex-grow  flex-wrap ml-2">
              <BreadCrumbModel projectId={projectId} parents={data.parents} />
              {data?.properties?.Type ? (
                <div className="py-1 ml-2">
                  <Badge type={data.properties.Type} />
                </div>
              ) : (
                ""
              )}
            </div>
            <div className="flex items-center justify-right flex-shrink-0 mr-2">
              <ConfigureButtons
                updateProps={updateProps}
                configureProps={configureProps}
              />
              <EllipsisDropdown
                options={["Edit", "Delete", "view past discussion"]}
                onOptionClick={(option) => {
                  if (option === "view past discussion") {
                    handleViewPastDiscussion();
                  } else {
                    handleBtn();
                  }
                }}
                buttonClassName="architecture-btn"
              />
            </div>
          </div>
        </div>
        <div className="  w-full  ">
          <div className="p-3 pt-0 ">
            {" "}
            {data && (
              <PropertiesRenderer
                properties={data.properties}
                metadata={data.ui_metadata}
                to_skip={["Type"]}
                projectId={projectId}
                architectureId={architectureId}
              />
            )}{" "}
          </div>
          <div >
            {withInterfaceData.length > 0 ? (
              <WithInterfaceTable
                data={withInterfaceData}
                projectId={params.projectId}
              />
            ) : (
              <div className="flex items-center justify-center">
                <EnParser content={en.InterfaceTableNotFound} />
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="border-2 rounded-lg mb-5">
        <div
          className="flex sticky top-0 justify-between  items-center border-t border-r border-l pl-2 rounded-t"
          style={{ height: "80px", zIndex: 1 }}
        >
          <div className="flex items-center flex-grow max-h-auto flex-wrap">
            <h2 className="project-panel-heading whitespace-normal ml-2">
              Architecture Relationships{" "}
              <Badge type={dataType} />
            </h2>
          </div>
        </div>
        {isLoading ? (
          <div className="w-full flex justify-center">
            <Loading2 />
          </div>
        ) : getCurrentArchitectureRelationships().length > 0 ? (
          <div className="overflow-x-auto bg-white">
            <ArchitectureRelationshipsTable
              implementations={[
                { target_nodes: getCurrentArchitectureRelationships() },
              ]}
              projectId={projectId}
            />
          </div>
        ) : (
          <div className="m-3 flex items-center justify-center">
            <EnParser content={en.ArchitectureRelationshipsNotAvailable} />
          </div>
        )}
      </div>

      <div className=" border-2 rounded-lg mb-5">
        <div
          className="flex sticky top-0 justify-between  items-center border-t border-r border-l pl-2 rounded-t "
          style={{ height: "80px", zIndex: 1 }}
        >
          <div className="flex items-center flex-grow max-h-auto flex-wrap">
            <h2 className="project-panel-heading whitespace-normal ml-2">
              Sub Components
            </h2>
          </div>
          <div className="flex mr-3 items-center justify-right flex-shrink-0">
            <Button
              className="p-2 mr-3 flex items-center"
              size="md"
              onClick={(e) => {
                handleUpdateClick(e, true);
              }}
            >
              <FaCogs size={18} className="mr-2" />
              {"Update Sub Components"}
            </Button>
            <EllipsisDropdown
              options={["Edit", "Delete", "view past discussion"]}
              onOptionClick={(option) => {
                if (option === "view past discussion") {
                  handleViewPastDiscussion(true);
                } else {
                  handleBtn();
                }
              }}
              buttonClassName="architecture-btn"
            />
            <div className="ml-4">
              <ToggleButton isToggled={isToggled} toggle={toggle} />
            </div>
          </div>
        </div>
        {isToggled ? (
          <ComponentTable
            projectId={params.projectId}
            nodeId={params.architectureId}
            isChild={true}
            handleItemClick={(id) => {
              const { buildProjectUrl } = require('@/utils/navigationHelpers');
              const organizationId = pathname.split("/")[1];
              const type = pathname.split("/")[2];
              router.push(
                buildProjectUrl(params.projectId, `architecture/high-level/${id}`, type, organizationId)
              );
            }}
          />
        ) : (
          <>
            <Accordion2
              isopen={true}
              title={
                data.properties.Title + " Diagram" || "Architecture Diagram"
              }
            >
              {data.properties.MermaidChart ? (
                <NoSSR chartDefinition={data.properties.MermaidChart} />
              ) : (
                <div className="flex items-center justify-center">
                  <EnParser content={en.MermaidChartNotAvailable} />
                </div>
              )}
            </Accordion2>
          </>
        )}
      </div>

      {configureModel && (
        <ConfigureModal
          id={architectureId}
          type={"architecture"}
          isNodeType="Architecture"
          closeModal={handleCloseModal}
          setLoadingAutoConfigure={setLoadingAutoConfigure}
          onSubmitSuccess={() => {
            const successMessage = "Node Configured Successfully";
            showAlert(successMessage, "success");
            setIsVertCollapse(false);
          }}
        />
      )}
    </div>
  );
};

export default ArchitectureDetails;
