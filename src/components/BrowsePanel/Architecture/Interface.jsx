"use client";
import { getInterfaceData } from "@/utils/api";
import { useParams, useRouter } from "next/navigation";
import React, { useEffect, useState, useMemo } from "react";
import en from "../../../en.json";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import ErrorView from "@/components/Modal/ErrorViewModal";
import GenericCardGrid from "@/components/UIComponents/GenericCards/GenericCardGrid"
import CardGroupSkeletonLoder from "@/components/UIComponents/Loaders/CardGroupSkeletonLoder"
import { buildProjectUrl } from '@/utils/navigationHelpers';

const Interface = ({ pageSize = 20, routeType ="interface" }) => {
  const [interfaceData, setInterfaceData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const params = useParams();
  const router = useRouter();

  const fetchData = async () => {
    try {
      const data = await getInterfaceData(params.projectId);
      // Use a setTimeout to ensure smooth transition from loading state
      setTimeout(() => {
        setInterfaceData(data[0]?.interfaces || []);
        setIsLoading(false);
      }, 100);
    } catch (error) {
      setError(error);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [params.projectId]);

  const headers = [
    { key: "id", label: "ID" },
    { key: "title", label: "TITLE" },
    { key: "type", label: "TYPE" },
    { key: "description", label: "DESCRIPTION" },
  ];

  const groupedData = useMemo(() => {
    const uniqueContainers = new Map();

    interfaceData.forEach((item) => {
      const containerId = item.id;

      if (!uniqueContainers.has(containerId)) {
        uniqueContainers.set(containerId, {
          id: containerId,
          title: item.properties?.Title,
          type: item.type,
          components: [
            {
              id: item.child_node.id,
              title: item.child_node.properties?.Title || "N/A",
              type: item.child_node.type,
              description: item.child_node.properties?.Description || "N/A",
              container: {
                title: item.properties?.Title,
                id: containerId,
                type: item.type,
              },
            },
          ],
        });
      }
    });

    return Array.from(uniqueContainers.values());
  }, [interfaceData]);

  const handleRowClick = (id) => {
    if (routeType=='api_docs'){
      router.push(buildProjectUrl(params.projectId, `architecture/api-docs/${id}`));
      return
    }
    router.push(buildProjectUrl(params.projectId, `architecture/interfaces/${id}`));
  };

  // Create a stable container with consistent height
  return (
    <div className={`interface-content-container architecture-content-transition ${isLoading ? 'loading' : ''} h-full overflow-y-auto custom-scrollbar`}>
      {isLoading ? (
        <CardGroupSkeletonLoder />
      ) : error ? (
        <ErrorView
          title="Unable to Load Interface Details"
          message={en.UnableToLoadInterface}
          onRetry={fetchData}
        />
      ) : !groupedData.length ? (
        routeType === "api_docs" ? (
          <EmptyStateView type="apiDocumentation" />
        ) : (
          <EmptyStateView type="architectureInterfaces" />
        )
      ) : (
        <div className="h-full overflow-y-auto">
          <GenericCardGrid
            data={Object.entries(groupedData).map(([title, components]) => ({
              container_name: title,
              ...components
            }))}
            onCardClick={(comp) => handleRowClick(comp.id)}
            uniquePageIdentifier={`arch-interface-list-${params.projectId}`}
          />
        </div>
      )}
    </div>
  );
};

export default Interface;
