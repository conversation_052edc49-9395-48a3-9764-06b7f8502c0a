"use client";
import React, { useEffect, useState } from "react";
import SoftwareArchitectureDocument from "../SAD";
import { fetchSadDocumentation } from "@/utils/api";
import { useParams } from "next/navigation";
import en from "../../../en.json"
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import ErrorView from "@/components/Modal/ErrorViewModal";
import { Loading2 } from "@/components/Loaders/Loading";


const SoftwareArchitectureDocumentScreen = () => {
  const params = useParams();
  const projectId = params.projectId;
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error,setError] = useState(null)

  
    const fetchData = async () => {
      setLoading(true);
      try {
        const data = await fetchSadDocumentation(projectId);
        setData(data);
      } catch (e) {
        setError(e)
        
      } finally {
        setLoading(false);
      }
    };
  useEffect(() => {
    fetchData();
  }, [projectId])

  

  if (loading) {
    return <Loading2/>
  }

  if(error){
    return (
      <ErrorView
        title="Unable to Load Software Architecture Documents"
        message={en.UnableToLoadSAD}
        onRetry={() => fetchData()}
        panelType='main'
      />
    );
  }

  if (data.length === 0) {
    return (
      <div className="text-center flex justify-center  items-center "> 
      <EmptyStateView type="softwareArchitecture" />
   </div>
    );
  }

  return (
    <div className="p-4 space-y-6">
      {data.map((item, index) => (
        <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <SoftwareArchitectureDocument data={item.sad_docs} />
        </div>
      ))}
    </div>
  );
};

export default SoftwareArchitectureDocumentScreen;
