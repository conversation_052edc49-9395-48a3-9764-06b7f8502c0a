"use client";
import React, { useEffect, useState, useContext } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { getAllComponentsFromProject ,getReconfigNodeStatus } from "@/utils/api";
import en from "@/en.json"
import { ArchitectureContext } from "@/components/Context/ArchitectureContext";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import Badge from "@/components/UIComponents/Badge/Badge"
import ErrorView from "@/components/Modal/ErrorViewModal";
import { IconButton } from '@/components/UIComponents/Buttons/IconButton';
import { ArrowLeft } from "lucide-react";
import GenericCardGrid from "@/components/UIComponents/GenericCards/GenericCardGrid"
import CardGroupSkeletonLoder from "@/components/UIComponents/Loaders/CardGroupSkeletonLoder"
import RequirementsBanner from "@/components/UIComponents/Badge/RequirementBanner";
import "@/styles/tabs/architecture/components.css"
import { buildProjectUrl } from '@/utils/navigationHelpers';

const ComponentList = () => {
  const [containersWithComponents, setContainersWithComponents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null)
  const pathname = usePathname();
  const router = useRouter();
  const projectId = pathname.split("/")[3];
  const { setComponentIdVal } = useContext(ArchitectureContext)
  const searchParams = useSearchParams();
  const [projectName, setProjectName] = useState("");
  const [showBaner,setShowBaner] = useState(false)
  const [reconfigCount,setReconfigCount] = useState(0)

  const fetchData = async () => {
    setError(null);
    try {
      const response = await getAllComponentsFromProject(projectId);
      if (response.containers) {
        setContainersWithComponents(response.containers);
      }
      const reconfig = await getReconfigNodeStatus(projectId)
      let showBanner = false;

      if (reconfig) {
        const hasReconfigNeeded =
        reconfig.Component?.some(item => item.reconfig_needed === true)
        const Count = 
        (reconfig.Component?.filter(item => item.reconfig_needed === true)?.length || 0)

        setReconfigCount(Count) 

        showBanner = hasReconfigNeeded;
      }
      setShowBaner(showBanner)
      
      const name = sessionStorage.getItem("project name");
      setProjectName(name);
    } catch (err) {
      
      setError("Failed to fetch all component data");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData()
  }, [projectId, pathname])


  const headers = [
    { key: 'id', label: 'Id' },
    { key: 'title', label: 'Title' },
    { key: 'type', label: 'Type' },
    { key: 'description', label: 'Description' },
  ];

  const handleBack = () => {
    router.back();
  };

  const handleRowClick = (componentId, containerId) => {
    sessionStorage.setItem("containerId", containerId);
    setComponentIdVal(containerId);
    router.push(buildProjectUrl(projectId, `architecture/component/${componentId}`));
  };

  const HeaderSection = () => (
    <div className="flex flex-col space-y-4 top-1" style={{ zIndex: 5 }}>
      <div className="flex flex-col  border border-gray-200">
        <div className="relative px-2.5 py-1 space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <IconButton
                icon={<ArrowLeft className="w-5 h-5 text-gray-600" />}
                tooltip="Go back"
                onClick={handleBack}
                className="hover:bg-gray-100"
              />
              <div className="flex items-center gap-2">
                <h2 className="typography-body-lg font-weight-semibold text-gray-800">
                  {"Components"}
                </h2>
                <div className="flex items-center gap-1">
                  <Badge type={"Components List"} />
                </div>
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-primary-100/50 via-primary-300/20 to-transparent"></div>
        </div>
      </div>
    </div>

  );

  if (loading) {
    return <CardGroupSkeletonLoder />;
  }

  if (error) {
    return (
      <ErrorView
        title="Unable to Load Component List"
        message={en.UnableToLoadComponent}
        onRetry={() => fetchData()}
        panelType='main'
      />
    );
  }

  if (!containersWithComponents.length)
    return <EmptyStateView type="components" />;

  return (
    <div>
       {showBaner? (
          <RequirementsBanner value={`One or more components `}/> 
        ):null}
    <GenericCardGrid
      data={Object.entries(containersWithComponents).map(([container_id, container_data]) => ({
        container_name: container_data.container_name,
        components: container_data.components.map(component => ({
          container: {
            title: container_data.container_name,
            id: container_data.container_id,
          },
          ...component,
          ...component.properties // Flatten properties into the main object
        }))
      }))}
      onCardClick={(comp) => handleRowClick(comp.id, comp.container.id)}
      uniquePageIdentifier={`arch-component-list-${projectId}`}
    />
    </div>
  )
};

export default ComponentList;
