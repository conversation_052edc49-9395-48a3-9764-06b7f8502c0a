"use client"

import React, { useEffect, useState, useContext } from "react";
import '@/styles/tabs/architecture/designDetailTab/designDetails.css'
import {
    useRouter,
    usePathname,
    useSearchParams
} from "next/navigation";
import PropertiesRenderer from "@/components/UiMetadata/PropertiesRenderer";
import { fetchDesignWithChildren, updateNodeByPriority } from "@/utils/api";
import TableComponent from "@/components/SimpleTable/ArchitectureTable"
import { ArchitectureContext } from "@/components/Context/ArchitectureContext";
import ConfigureButtons from "@/components/ConfigureButtons/ConfigureButtons";
import { Loading2 } from "@/components/Loaders/Loading";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import ErrorView from "@/components/Modal/ErrorViewModal";
import Badge from "@/components/UIComponents/Badge/Badge"
import en from "@/en.json"
import { updateSessionStorageBackHistory } from "@/utils/helpers";
import { ArrowLeft } from "lucide-react";
import { IconButton } from '@/components/UIComponents/Buttons/IconButton';

const DesignDetails = () => {
    const [loading, setLoading] = useState(true);
    const [designDetails, setDesignDetails] = useState([])
    const { showAlert } = useContext(AlertContext);
    const [error, setError] = useState(null)
    const pathname = usePathname();
    const projectId = pathname.split("/")[3];
    const designId = pathname.split("/")[5];
    const router = useRouter();
    const searchParams = useSearchParams()
    const { componentIdVal } = useContext(ArchitectureContext);
    let activeComponentIdval = componentIdVal;



    const fetchData = async () => {
        if (componentIdVal === null) {
            activeComponentIdval = sessionStorage.getItem("componentId")
        }
        setLoading(true);
        try {
            const data = await fetchDesignWithChildren(projectId, activeComponentIdval, designId);
            setDesignDetails(data);
            sessionStorage.setItem("designId", designId)
        } catch (error) {

            setError(error)
        } finally {
            setLoading(false);
        }
    };



    useEffect(() => {
        fetchData();
    }, [projectId, designId, pathname])

    const headers = [
        { key: 'id', label: 'Id' },
        { key: 'title', label: 'Title' },
        { key: 'type', label: 'Type' },

    ];

    const tableData = designDetails?.data?.childNodes.map(data => ({
        id: data.id,
        title: data.properties.Title,
        type: data.type,

    }))

    if (loading) {
        return <Loading2 />;
    }

    if (error) {
        return (
            <ErrorView
                title="Unable to Load Design Details"
                message={en.UnableToLoadDesignDetails}
                onRetry={() => fetchData()}
                panelType='main'
            />
        )
    }

    const handleUpdateDesign = () => {
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.set("discussion", "new");
        newSearchParams.set("node_id", designId);
        newSearchParams.set("node_type", "Design");
        updateSessionStorageBackHistory();
        router.push(`${pathname}?${newSearchParams.toString()}`);
    };
    const updateProps = {
        onUpdateClick: handleUpdateDesign,
        buttonText: "Update Design Details",
    };

    const handleRowClick = (id, type) => {
        sessionStorage.setItem("child design id", id)
        router.push(buildProjectUrl(projectId, `architecture/design-details/${designId}/${type}`))
    };

    const handleBack = () => {
        if(sessionStorage.getItem("querySet")){
            const backTabs = Number(sessionStorage.getItem("querySet")); //number of tabs we need to go back to avoid the modal being repopped
            sessionStorage.removeItem("querySet");
            window.history.go(backTabs); //will go back required number of steps to avoid popping the discussion channel
        }
        else{
            router.back();
        }
    };

    const handlePropertyUpdate = async (key, value) => {
        try {
            const response = await updateNodeByPriority(designId, key, value);

            if (response === "success") {
                // Update local state
                setDesignDetails(prev => ({
                    ...prev,
                    data: {
                        ...prev.data,
                        design: {
                            ...prev.data.design,
                            properties: {
                                ...prev.data.design.properties,
                                [key]: value
                            }
                        }
                    }
                }));
                showAlert("Content updated successfully", "success");
            } else {
                throw new Error('Update failed');
            }
        } catch (error) {

            showAlert("Failed to update content", "error");
        }
    };


    return (
        <div className="custom-scrollbar design-details-content-wrapper">
            <div className="design-details-content-sub-wrapper">
                <div className="design-details-container pr-4">
                    <div
                        className="design-details-header-wrapper"
                    >
                        <div className="design-details-header-sub-wrapper">
                            <IconButton
                                icon={<ArrowLeft className="w-5 h-5 text-gray-600" />}
                                tooltip="Go back"
                                onClick={handleBack}
                                className="hover:bg-gray-100"
                            />
                            <h2 className="design-details-heading-title">  {designDetails?.data?.design?.properties?.Title || "Design Details"}</h2>
                            {designDetails?.data?.design?.properties?.Type ? (
                                <div className="design-details-heading-badge">
                                    <Badge type={designDetails?.data?.design.properties.Type} />
                                </div>
                            ) : (
                                ""
                            )}
                        </div>
                        <div className="design-details-header-button-wrapper">
                            <ConfigureButtons
                                updateProps={updateProps}

                            />
                        </div>

                    </div>
                    {designDetails && (
                        <PropertiesRenderer
                            properties={designDetails?.data?.design?.properties}
                            metadata={designDetails?.model?.Design?.ui_metadata}
                            to_skip={["configuration_state", "Type", "Title"]}
                            onUpdate={handlePropertyUpdate}
                        />
                    )}
                    <div className="design-details-related-child-nodes-wrapper">
                        {designDetails?.data?.childNodes?.length > 0 ? (
                            <TableComponent
                                data={tableData}
                                onRowClick={handleRowClick}
                                headers={headers}
                                sortableColumns={{ "id": true, "title": true, "type": true }}
                                itemsPerPage={20}
                                component="Design Details"
                                title="Related Design Child Nodes in the Architecture"
                            />
                        ) : (
                            <div className="design-details-no-items-found">
                                <EmptyStateView type="relatedDesignChildNodes" />
                            </div>
                        )}

                    </div>
                </div>
            </div>
        </div>
    )


}

export default DesignDetails;