"use client";

import React, { useEffect, useState, useContext } from "react";
import ApiAccordion from "@/components/APIDoc";
import { Loading2 } from "@/components/Loaders/Loading";
import { fetchApiDocs } from "@/utils/api";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { useParams } from "next/navigation";
import en from "../../../en.json"
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import ErrorView from "@/components/Modal/ErrorViewModal";

const APIDocs = () => {
  const params = useParams()
  const projectId = params.projectId;
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const {showAlert} = useContext(AlertContext);


    const fetchData = async () => {
      setLoading(true);
      showAlert(null);
      try {
        const fetchedData = await fetchApiDocs(projectId);
        setData(fetchedData);
      } catch (e) {

        setError(e)
      } finally {
        setLoading(false);
      }
    };

    useEffect(()=>{
      fetchData()
    },[projectId])

  if (loading) {
    return <Loading2 />;
  }

  if (error) {
    return (
      <ErrorView
        title="Unable to Load API Documentations"
        message={en.UnableToLoadAPIDocs}
        onRetry={() => fetchData()}
        panelType='main'
      />
    );
  }

  if (data.length === 0) {
    return (
      <div className="text-center flex justify-center  items-center ">
         <EmptyStateView type="apiDocumentation" />
      </div>
    );
  }

  return (
    <div className="p-4 pr-4 space-y-6">
      {data.map((item, index) => (
        <ApiAccordion key={item.id || index} data={item} />
      ))}
    </div>
  );
};

export default APIDocs;
