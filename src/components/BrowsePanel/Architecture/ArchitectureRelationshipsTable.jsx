import React from 'react';
import { useRouter } from 'next/navigation';

const ArchitectureRelationshipsTable = ({ implementations, projectId }) => {
  const router = useRouter();

  const handleRowClick = (node) => {
    const { buildProjectUrl } = require('@/utils/navigationHelpers');
    const currentPath = window.location.pathname;
    const pathParts = currentPath.split('/');
    const organizationId = pathParts[1];
    const type = pathParts[2];
    router.push(buildProjectUrl(projectId, `architecture/architecture-requirement/${node.properties?.Type || 'unknown'}/${node.id}`, type, organizationId));
  };

  const truncate = (str, n) => {
    return str && str.length > n ? str.slice(0, n-1) + '...' : str;
  };

  return (
    <div className="overflow-x-auto">
      <table className="w-full project-panel-content text-center table-auto">
        <thead className="uppercase h-10 bg-gray-100">
          <tr className="mt-1">
            <th className="px-4 py-2">ID</th>
            <th className="px-6 py-1">TITLE</th>
            <th className="px-6 py-1">TYPE</th>
            <th className="px-6 py-1">DESCRIPTION</th>
            <th className="px-6 py-1">RELATION</th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y">
          {implementations[0]?.target_nodes.map((node) => (
            <tr
              key={node.id}
              className="cursor-pointer hover:bg-gray-100 px-4 transition-colors duration-150"
              onClick={() => handleRowClick(node)}
            >
              <td className="py-2 px-4 border-b text-center">{node.id}</td>
              <td className="py-2 px-4 border-b">
                {truncate(node.properties?.Title, 20) || "N/A"}
              </td>
              <td className="py-2 px-4 border-b">
                <span className="bg-gray-200 rounded-xl px-2 py-0.5 typography-body-sm">
                  {node.properties?.Type || "N/A"}
                </span>
              </td>
              <td className="py-2 px-4 border-b">
                {truncate(node.properties?.Description, 30) || "N/A"}
              </td>
              <td className="py-2 px-4 border-b">
                <span className="bg-gray-200 rounded-xl px-2 py-0.5 typography-body-sm">
                  {node.relationship?.type || "N/A"}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ArchitectureRelationshipsTable;