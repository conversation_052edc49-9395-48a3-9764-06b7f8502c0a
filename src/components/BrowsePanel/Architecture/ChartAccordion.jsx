// components/ChartAccordion.js
"use client"
import React, { useState } from "react";
import { ChevronDown, ChevronUp } from 'lucide-react';

const ChartAccordion = ({ title,isopen = true, children }) => {
  const [isOpen, setIsOpen] = useState(isopen);
  const toggleAccordion = () => setIsOpen(!isOpen);

  return (
    <div className="border border-gray-200 rounded-lg mb-4">
      <div
        className={`w-full flex items-center justify-between cursor-pointer px-4 py-2.5 text-left  rounded-t-lg transition-colors duration-200 ${
          isOpen ? 'bg-gray-50' : 'hover:bg-gray-100'
        }`}
        onClick={toggleAccordion}
      >
        <div className="flex items-center gap-2 font-weight-semibold typography-body text-[#2A3439]">{title}</div>
        <div className="text-gray-600">
          {isOpen ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
        </div>
      </div>
      {isOpen && (
        <div className="p-4 bg-white border-t border-gray-200 rounded-b-lg typography-body-sm text-[#464F60] font-weight-medium">
          {children}
        </div>
      )}
    </div>
  );
};

export default ChartAccordion;
