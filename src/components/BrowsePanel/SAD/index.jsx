import React from 'react';
import * as AccordionPrimitive from '@radix-ui/react-accordion';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';

const Accordion = AccordionPrimitive.Root;

const AccordionItem = React.forwardRef(({ className, ...props }, ref) => (
  <AccordionPrimitive.Item
    ref={ref}
    className={cn("mb-2", className)}
    {...props}
  />
));
AccordionItem.displayName = "AccordionItem";
const AccordionTrigger = React.forwardRef(
    ({ className, children, arrow, ...props }, ref) => (
      <AccordionPrimitive.Header className="flex">
        <AccordionPrimitive.Trigger
          ref={ref}
          className={cn(
            "flex w-full p-4  items-center justify-start typography-body  ffont-weight-semibold  transition-all  text-gray-900 dark:text-gray-100 [&[data-state=open]>svg]:rotate-180",
            className
          )}
          {...props}
        >
                 {!arrow && (
            <ChevronDown className="h-5 w-5 shrink-0 transition-transform duration-300 ease-in-out mr-2" />
          )}
          {children}
     
        </AccordionPrimitive.Trigger>
      </AccordionPrimitive.Header>
    )
  );
AccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName;

const AccordionContent = React.forwardRef(
  ({ className, children, ...props }, ref) => (
    <AccordionPrimitive.Content
      ref={ref}
      className={cn(
        "overflow-hidden typography-body-sm transition-all text-gray-700 dark:text-gray-300 max-h-0 data-[state=open]:max-h-screen",
        className
      )}
      {...props}
    >
      <div className="pl-4  rounded-md">{children}</div>
    </AccordionPrimitive.Content>
  )
);
AccordionContent.displayName = AccordionPrimitive.Content.displayName;

const Badge = ({ children }) => (
  <span className="inline-flex items-center rounded-full bg-gray-200 px-3 py-0.5 typography-body-sm font-weight-medium text-gray-800">
    {children}
  </span>
);

const parseContent = (content) => {
    if (!content){
      return null
    }
    const lines = content.split('\n');
    const parsedContent = [];
    let listItems = [];
    let isParagraph = false;
  
    lines.forEach((line, index) => {
      line = line.trim();
      if (line) {
        // Check if it's a key-value pair or a paragraph
        const [key, ...rest] = line.split(':');
        const value = rest.join(':').trim();
  
        if (value && (value.startsWith('{') && value.endsWith('}')) || (value.startsWith('[') && value.endsWith(']'))) {
          // Handle JSON objects/arrays
          listItems.push(
            <li key={index} className="my-1">
              <div className="flex flex-col">
                <strong className="w-full">{key}:</strong>
                <pre className="w-full bg-gray-100 dark:bg-gray-900 p-2 rounded-md overflow-auto">
                  {JSON.stringify(JSON.parse(value), null, 2)}
                </pre>
              </div>
            </li>
          );
        } else if (line.includes(':')) {
          // Handle key-value pairs
          listItems.push(
            <li key={index} className="my-1">
              <div className="flex">
                <strong className="w-1/3">{key}:</strong>
                <span className="w-2/3">{value}</span>
              </div>
            </li>
          );
        } else {
          // Handle paragraphs
          if (listItems.length > 0) {
            parsedContent.push(
              <ol key={`ordered-list-${parsedContent.length}`} className="list-decimal pl-6">
                {listItems}
              </ol>
            );
            listItems = [];
          }
          parsedContent.push(
            <p key={index} className="my-2">
              {line}
            </p>
          );
        }
      }
    });
  
    // Handle remaining list items if any
    if (listItems.length > 0) {
      parsedContent.push(
        <ol key={`ordered-list-${parsedContent.length}`} className="list-decimal pl-6">
          {listItems}
        </ol>
      );
    }
  
    return parsedContent;
  };
  

  const SoftwareArchitectureDocument = ({ data }) => {
    return (
      <div className="space-y-6">
        <Accordion type="multiple" className="space-y-4">
          {data.map((item, index) => {
            return (
              <AccordionItem key={index} value={`item-${index}`} className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
                <AccordionTrigger className="bg-gray-50 dark:bg-gray-900 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors">
                  <div className="flex items-center space-x-3">
                    
                    <div className="font-weight-medium">{item.title.replace(/_/g, ' ')}</div>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="p-4 bg-white dark:bg-gray-800">
                  <div className="prose dark:prose-invert max-w-none">
                    {parseContent(item.content)}
                  </div>
                </AccordionContent>
              </AccordionItem>
            );
          })}
        </Accordion>
      </div>
    );
  };
  
  export default SoftwareArchitectureDocument;