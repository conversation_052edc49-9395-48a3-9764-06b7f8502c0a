import React from 'react';

const Pagination = ({ currentPage, totalPages, onPageChange }) => {
  const handlePrevious = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  const handlePageChange = (page) => {
    if (page !== currentPage) {
      onPageChange(page);
    }
  };

  const renderPageNumbers = () => {
    const pages = [];
    const totalNumbersToShow = 5;
    const halfNumbersToShow = Math.floor(totalNumbersToShow / 2);

    let startPage = Math.max(1, currentPage - halfNumbersToShow);
    let endPage = Math.min(totalPages, currentPage + halfNumbersToShow);

    if (currentPage <= halfNumbersToShow) {
      endPage = Math.min(totalNumbersToShow, totalPages);
    }

    if (currentPage + halfNumbersToShow >= totalPages) {
      startPage = Math.max(1, totalPages - totalNumbersToShow + 1);
    }

    if (startPage > 1) {
      pages.push(
        <button
          key={1}
          onClick={() => handlePageChange(1)}
          className={`px-3 py-1 border ${1 === currentPage ? 'bg-primary text-white' : 'bg-white text-primary'}`}
        >
          1
        </button>
      );
      if (startPage > 2) {
        pages.push(<span key="start-dots" className="px-3 py-1">...</span>);
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <button
          key={i}
          onClick={() => handlePageChange(i)}
          className={`px-3 py-1 border ${i === currentPage ? 'bg-primary text-white' : 'bg-white text-primary'}`}
        >
          {i}
        </button>
      );
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push(<span key="end-dots" className="px-3 py-1">...</span>);
      }
      pages.push(
        <button
          key={totalPages}
          onClick={() => handlePageChange(totalPages)}
          className={`px-3 py-1 border ${totalPages === currentPage ? 'bg-primary text-white' : 'bg-white text-primary'}`}
        >
          {totalPages}
        </button>
      );
    }

    return pages;
  };

  return (
    <div className="flex justify-center items-center space-x-2 ">
      <button
        onClick={handlePrevious}
        disabled={currentPage === 1}
        className="px-3 py-1 border bg-white text-primary disabled:opacity-50"
      >
        Previous
      </button>
      {renderPageNumbers()}
      <button
        onClick={handleNext}
        disabled={currentPage === totalPages}
        className="px-3 py-1 border bg-white text-primary disabled:opacity-50"
      >
        Next
      </button>
    </div>
  );
};

export default Pagination;
