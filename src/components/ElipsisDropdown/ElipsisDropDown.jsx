import React, { useState, useRef, useEffect } from "react";
import { FaEllipsisH } from "react-icons/fa";
import Button from "../Buttons";
const EllipsisDropdown = ({ options, onOptionClick, buttonClassName = "" }) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);

  const handleDropdownToggle = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const handleClickOutside = (event) => {
    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
      setIsDropdownOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <Button
        onClick={handleDropdownToggle}
        className={`p-2   bg-[#f5f6f7]  ${buttonClassName}`}
        size="md"
        variant="outline"
        color="secondary"
      >
        <FaEllipsisH size={18} />
      </Button>
      {isDropdownOpen && (
        <div className="absolute right-0 mt-2 w-48 bg-white border rounded-md shadow-lg z-40">
          {options.map((option, index) => (
            <button
              key={index}
              onClick={() => {
                onOptionClick(option);
                setIsDropdownOpen(false);
              }}
              className="block px-4 py-2 text-left text-gray-800 hover:bg-gray-100 w-full"
            >
              {option}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default EllipsisDropdown;
