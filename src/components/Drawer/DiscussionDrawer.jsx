import React, { useState, useEffect, useRef } from 'react';;
import DiscussionListModal from '../Modal/DiscussionListModal';
import Drawer from "@/components/Drawer";
import { MessageSquare } from "lucide-react";

const DiscussionDrawer = ({ discussions, isOpen, onClose, showSearch = true }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const modalRef = useRef(null);

  if (discussions.length === 0) {
    showSearch = false
  }

  const handleClose = () => {
    if (onClose) {
      onClose();
    }
  };

  const handleClickOutside = (e) => {
    if (modalRef.current && !modalRef.current.contains(e.target)) {
      handleClose();
    }
  };

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);
  const filteredDiscussions = showSearch
    ? discussions.filter((discussion) =>
      discussion.project_title.toLowerCase().includes(searchQuery.toLowerCase())
    )
    : discussions;

  const handleSearchQuery = (value) => {
    setSearchQuery(value);
  };

  const clearSearchQuery = () => {
    setSearchQuery('');
  };

  return (
    <Drawer
      isOpen={isOpen}
      onClose={onClose}
      placement="right"
      showBackdrop={false}
      title={
        <span className="drawer-title">
          <div className="drawer-title-container">
            <MessageSquare className="drawer-title-icon" />
            <span className="drawer-title-name">Discussions ({discussions.length})</span>
          </div>
        </span>
      }
      width={400}
    >
      <DiscussionListModal
        discussions={filteredDiscussions}
        showSearch={showSearch}
        searchQuery={searchQuery}
        clearQueryfn ={clearSearchQuery}
        searchQueryfn={handleSearchQuery}
      />
    </Drawer>
  );
};

export default DiscussionDrawer;
