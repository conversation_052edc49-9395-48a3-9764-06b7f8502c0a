import * as React from "react";
import * as AccordionPrimitive from "@radix-ui/react-accordion";
import { ChevronDown } from "lucide-react";
import { cn } from "@/lib/utils";
import Badge from "../Badge";

const Accordion = AccordionPrimitive.Root;

const AccordionItem = React.forwardRef(({ className, ...props }, ref) => (
  <AccordionPrimitive.Item
    ref={ref}
    className={cn(
      "border border-gray-300 dark:border-gray-600 rounded-md mb-2",
      className
    )}
    {...props}
  />
));
AccordionItem.displayName = "AccordionItem";

const AccordionTrigger = React.forwardRef(
  ({ className, children, ...props }, ref) => (
    <AccordionPrimitive.Header className="flex w-full">
      <AccordionPrimitive.Trigger
        ref={ref}
        className={cn(
          "flex w-full items-center justify-between p-2 typography-body font-weight-semibold text-gray-900 dark:text-gray-100 bg-gray-200 dark:bg-gray-700 border-b border-gray-300 dark:border-gray-600 rounded-t-md transition-all",
          className
        )}
        {...props}
      >
        <div className="flex items-center space-x-2">
          {children}
        </div>
        <span className="ml-2">
          <ChevronDown className="h-5 w-5 transition-transform duration-300 ease-in-out data-[state=open]:rotate-180" />
        </span>
      </AccordionPrimitive.Trigger>
    </AccordionPrimitive.Header>
  )
);
AccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName;

const AccordionContent = React.forwardRef(
  ({ className, children, ...props }, ref) => (
    <AccordionPrimitive.Content
      ref={ref}
      className={cn(
        "overflow-hidden typography-body-sm transition-all text-gray-700 dark:text-gray-300 max-h-20 data-[state=open]:max-h-screen",
        className
      )}
      {...props}
    >
      <div className="p-2">{children}</div>
    </AccordionPrimitive.Content>
  )
);
AccordionContent.displayName = AccordionPrimitive.Content.displayName;



const ApiAccordion = ({ data }) => {
  if (!data) {
    return null;
  }
  const apiDocs = data.api_docs || [];
  const apiTitle = data.design_name || "API Documentation";

  const parseContent = (content) => {
    if (!content) return {};
    const lines = content.split('\n');
    const keyValuePairs = {};

    lines.forEach(line => {
      const [key, ...valueParts] = line.split(':');
      if (key && valueParts.length) {
        const value = valueParts.join(':').trim();
        keyValuePairs[key.trim()] = isCodeBlock(value) ? formatCode(value) : value;
      }
    });

    return keyValuePairs;
  };

  const isCodeBlock = (value) => {
    try {
      JSON.parse(value);
      return true;
    } catch {
      return false;
    }
  };

  const formatCode = (code) => (
    <pre className="bg-gray-100 p-2 rounded-md overflow-x-auto">
      <code>{code}</code>
    </pre>
  );

  const getMethodBadge = (method) => {
    switch (method?.toUpperCase()) {
      case "POST":
        return <Badge color="success" variant="soft" className="font-weight-bold mr-2">POST</Badge>;
      case "GET":
        return <Badge color="warning" className="font-weight-bold mr-2">GET</Badge>;
      case "PUT":
        return <Badge color="info" variant="soft" className="font-weight-bold mr-2">PUT</Badge>;
      case "DELETE":
        return <Badge color="danger" variant="soft" className="font-weight-bold mr-2">DELETE</Badge>;
      default:
        return null;
    }
  };

  return (
    <>
      <h2 className="typography-heading-4 font-weight-bold mb-4">{apiTitle}</h2>
      <Accordion type="multiple" className="w-full space-y-4">
        {apiDocs.map((doc, index) => {
          const contentData = parseContent(doc.content);
          const method = contentData.Method || "Unknown";

          return (
            <AccordionItem key={index} value={`item-${index}`}>
              <AccordionTrigger>
                {getMethodBadge(method)}
                <div className="flex items-center">
                  <div className="typography-body-lg font-weight-semibold text-default-900">{doc?.title || "Untitled"}</div>
                  <div className="typography-caption text-default-900 mt-0.5 ml-2 align-middle">{doc?.description || ""}</div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="p-4">
                  <div className="space-y-2">
                    {Object.entries(contentData).map(([key, value], idx) => (
                      <div key={idx} className="flex flex-col sm:flex-row">
                        <strong className="w-full sm:w-1/3 text-gray-600">{key}:</strong>
                        <span className="w-full sm:w-2/3 mt-1 sm:mt-0">
                          {value || "N/A"}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          );
        })}
      </Accordion>
    </>
  );
};

export default ApiAccordion;