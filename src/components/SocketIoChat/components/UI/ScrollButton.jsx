import React from 'react';

// Add a scroll-to-bottom button component that appears when auto-scroll is disabled
const ScrollToBottomButton = ({ 
  autoScroll, 
  hasNewMessages, 
  isAtBottom, 
  inputFocused, 
  messagesContainerRef, 
  directScrollToBottom, 
  setAutoScroll, 
  setHasNewMessages, 
  userManuallyScrolledRef 
}) => {
  // Reference to check if the messages container actually has overflow
  const hasScrollableContent = messagesContainerRef.current &&
    messagesContainerRef.current.scrollHeight > messagesContainerRef.current.clientHeight;

  // Don't show button if:
  // 1. Auto-scroll is enabled
  // 2. No new messages to show
  // 3. Already at the bottom
  // 4. Input is currently focused (user is typing)
  // 5. There's not enough content to scroll
  if (autoScroll || !hasNewMessages || isAtBottom() || inputFocused || !hasScrollableContent) {
    return null;
  }

  return (
    <button
      className="fixed bottom-20 right-6 bg-orange-500 text-white rounded-full w-12 h-12 flex items-center justify-center shadow-lg transition-all hover:bg-orange-600 z-50"
      onClick={() => {
        // Scroll to bottom and reset flags
        directScrollToBottom();
        setAutoScroll(true);
        setHasNewMessages(false);
        userManuallyScrolledRef.current = false;
      }}
      aria-label="Scroll to bottom"
    >
      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M10 16L3 9L4.4 7.55L9 12.15V4H11V12.15L15.6 7.55L17 9L10 16Z" fill="currentColor"/>
      </svg>
    </button>
  );
};

export default ScrollToBottomButton; 