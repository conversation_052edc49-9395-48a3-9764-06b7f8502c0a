import { useState, useCallback } from 'react';

export const useRollback = (wsConnection, searchParams) => {
  const [showRollbackModal, setShowRollbackModal] = useState(false);
  const [rollbackMessageId, setRollbackMessageId] = useState(null);
  const [isRollbackInProgress, setIsRollbackInProgress] = useState(false);

  const handleRollbackClick = useCallback((messageId) => {
    setRollbackMessageId(messageId);
    setShowRollbackModal(true);
  }, []);

  const handleRollbackCancel = useCallback(() => {
    setShowRollbackModal(false);
    setRollbackMessageId(null);
    setIsRollbackInProgress(false);
  }, []);

  const handleRollbackConfirm = useCallback((result) => {
    
    
    
    // Send switch_to_checkpoints command following the merge_to_kavia_main pattern
    if (wsConnection?.readyState === WebSocket.OPEN && rollbackMessageId) {
      // Set rollback in progress
      setIsRollbackInProgress(true);
      
      wsConnection.send(JSON.stringify({
        type: "switch_to_checkpoints",
        task_id: searchParams.get("task_id"),
        input_data: {
          message_id: rollbackMessageId
        }
      }));
      
      
    } else {
      console.error('Unable to execute rollback command - WebSocket not ready or no message ID');
    }
    
    // Close the modal
    setShowRollbackModal(false);
    setRollbackMessageId(null);
  }, [rollbackMessageId, wsConnection, searchParams]);

  const handleRollbackStatusUpdate = useCallback((data) => {
    // Handle rollback status response
    if (data.data?.status === 'completed' || data.data?.status === 'error') {
      setIsRollbackInProgress(false);
      
      if (data.data.status === 'completed') {
        
      } else {
        console.error('Rollback failed:', data.data);
      }
    }
  }, []);

  return {
    showRollbackModal,
    rollbackMessageId,
    isRollbackInProgress,
    handleRollbackClick,
    handleRollbackCancel,
    handleRollbackConfirm,
    handleRollbackStatusUpdate
  };
}; 