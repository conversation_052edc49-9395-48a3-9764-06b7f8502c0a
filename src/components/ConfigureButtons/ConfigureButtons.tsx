// ConfigureButtons.tsx
// @ts-nocheck

import React, { useState } from "react";
import ConfigureModal from "@/components/Modal/ConfigureModel";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { Settings, Plus } from "lucide-react";

// Model for update-related props
interface UpdateProps {
  onUpdateClick: () => void;
  buttonText?: string;
  classNames?: Array<string>;
  tooltip?: string;
}

// Model for configure-related props
interface ConfigureProps {
  isConfigurable: boolean;
  nodeId: string | number;
  nodeType: string;
  setLoadingAutoConfigure: (loading: boolean) => void;
  onSubmitSuccess: () => void;
  buttonText?: string;
}

// Main component props
interface ConfigureButtonsProps {
  updateProps: UpdateProps;
  configureProps: ConfigureProps;
}

const ConfigureButtons: React.FC<ConfigureButtonsProps> = ({
  updateProps,
  configureProps,
}) => {
  const [configureModel, setConfigureModel] = useState<boolean>(false);

  const handleConfigureClick = () => {
    setConfigureModel(true);
  };

  const handleCloseModal = () => {
    setConfigureModel(false);
  };

  return (
    <div className="space-x-2">
      <DynamicButton
        type="submit"
        variant="primary"
        icon={Settings}
        onClick={updateProps.onUpdateClick}
        text={updateProps.buttonText || "Update Requirements"}
        tooltip={updateProps.tooltip}
      />

      {configureProps?.isConfigurable && (
        <DynamicButton
          type="submit"
          variant="primary"
          icon={Plus}
          onClick={handleConfigureClick}
          text={configureProps.buttonText || "Auto Configure"}
        />
      )}
      {configureModel && (
        <ConfigureModal
          id={configureProps.nodeId}
          requirementId={configureProps.nodeId}
          isNodeType={configureProps.nodeType}
          type={configureProps.nodeType}
          setLoadingAutoConfigure={configureProps.setLoadingAutoConfigure}
          onSubmitSuccess={configureProps.onSubmitSuccess}
          closeModal={handleCloseModal}
        />
      )}
    </div>
  );
};

export default ConfigureButtons;
