"use client";
import { useEffect, useState } from 'react';

const ThemeTransition = ({ children, delay = 50, duration = 500, applyToBody = true }) => {
  const [isTransitioning, setIsTransitioning] = useState(true);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Add the theme-transition class to the body when the component mounts
    if (applyToBody) {
      document.body.classList.add('theme-transition');
    }
    
    // First phase: wait for DOM to be ready
    const initialTimer = setTimeout(() => {
      setIsTransitioning(false);
      
      // Second phase: start fade-in animation
      setTimeout(() => {
        setIsLoaded(true);
      }, 25);
    }, delay);

    // Clean up function to remove the class when the component unmounts
    return () => {
      if (applyToBody) {
        document.body.classList.remove('theme-transition');
      }
      clearTimeout(initialTimer);
    };
  }, [delay, applyToBody]);

  // Apply fade-in effect to children with a container that also has theme transition
  return (
    <div 
      className={`theme-transition transition-all duration-${duration} ease-in-out ${isTransitioning ? 'opacity-0' : ''}`}
      style={{ transitionProperty: 'all' }}
    >
      <div className={`transition-opacity duration-${duration} ease-in-out ${isLoaded ? 'opacity-100' : 'opacity-0'}`}>
        {children}
      </div>
    </div>
  );
};

export default ThemeTransition;
