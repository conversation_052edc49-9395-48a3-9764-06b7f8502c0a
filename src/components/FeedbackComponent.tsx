

'use client';

import React, { useState } from 'react';
import { MessageSquare, AlertTriangle, X, Send, ExternalLink } from 'lucide-react';

const FeedbackComponent: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

 
  const feedbackUrl = "https://forms.gle/jyjby6wAsiYsnAs17"; 
  const issuesUrl = "https://forms.gle/T14KragEV8yB1d4L8";

  const openInNewTab = (url: string) => {
    window.open(url, '_blank', 'noopener,noreferrer');
    setIsOpen(false); // Close the menu after opening the form
  };

  return (
    <div className="fixed bottom-6 right-6 z-50 flex flex-col items-end">
      {/* Main floating button */}
      <button
        onClick={handleToggle}
        className="flex items-center justify-center rounded-full p-3 shadow-lg transition-all duration-300"
        style={{ backgroundColor: isOpen ? '#333' : '#FF6B35', color: 'white' }}
        aria-label="Feedback options"
      >
        {isOpen ? (
          <X size={24} /> // Proper X icon with no rotation
        ) : (
          <MessageSquare size={24} />
        )}
      </button>

      {/* Popup options */}
      {isOpen && (
        <div className="mb-4 flex flex-col items-end space-y-3 animate-fadeIn">
          <div className="flex items-center space-x-2">
            <button
              onClick={() => openInNewTab(issuesUrl)}
              className="bg-gray-800 text-white px-3 py-2 rounded-lg shadow-md text-sm flex items-center hover:bg-gray-700 transition-colors"
            >
              Report an issue
              <ExternalLink size={12} className="ml-1" />
            </button>
            <div
              className="text-white p-3 rounded-full shadow-md"
              style={{ backgroundColor: '#FF6B35' }}
              aria-label="Report issue"
            >
              <AlertTriangle size={20} />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => openInNewTab(feedbackUrl)}
              className="bg-gray-800 text-white px-3 py-2 rounded-lg shadow-md text-sm flex items-center hover:bg-gray-700 transition-colors"
            >
              Give feedback
              <ExternalLink size={12} className="ml-1" />
            </button>
            <div
              className="text-white p-3 rounded-full shadow-md"
              style={{ backgroundColor: '#FF6B35' }}
              aria-label="Give feedback"
            >
              <Send size={20} />
            </div>
          </div>
        </div>
      )}
      
      <style jsx>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out forwards;
        }
      `}</style>
    </div>
  );
};

export default FeedbackComponent;