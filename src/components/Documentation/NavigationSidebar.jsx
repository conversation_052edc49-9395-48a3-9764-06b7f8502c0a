// NavigationSidebar.jsx
import React, { useEffect, useContext, useState } from 'react';
import { Tree } from 'react-arborist';
import { Plus, GripVertical, Check, X, Trash2 } from 'lucide-react';
import ActionButtons from './ActionButtons';
import { AlertContext } from '../NotificationAlertService/AlertList';
import { createSection, reorderSections } from '@/utils/documentationAPI';

const NavigationSidebar = ({
  documentation,
  activeSection,
  scrollToSection,
  onGeneratePDF,
  onSyncToS3,
  onNavigateToDocuments,
  generatingPdf,
  syncingToS3,
  projectId,
  refreshDocumentation,
  documentType = "SAD",
  interfaceId = null,
  onUpdateDoc,
  onDeleteSection,
}) => {
  const [isCreating, setIsCreating] = useState(false);
  const [newSectionName, setNewSectionName] = useState('');
  const [sections, setSections] = useState(documentation?.sections || []);
  const [isCreatingSection, setIsCreatingSection] = useState(false);
  const [isReordering, setIsReordering] = useState(false);
  const { showAlert } = useContext(AlertContext);

  useEffect(() => {
    if (documentation?.sections) {
      setSections(documentation.sections);
    }
  }, [documentation]);

  const handleCreateSection = async () => {

    if (!newSectionName.trim()) {
      showAlert('Section name cannot be empty', 'warning');
      return;
    }

    const sectionExists = documentation.sections.some(
      (section) => section.Title.toLowerCase() === newSectionName.trim().toLowerCase()
    );
  
    if (sectionExists) {
      showAlert('Section name already exists', 'warning');
      return;
    }
  
    
      try {
        setIsCreatingSection(true);
        await createSection(projectId, newSectionName, documentType, interfaceId);
        showAlert('Section created successfully', 'success');
        await refreshDocumentation();
        setNewSectionName('');
        setIsCreating(false);
      } catch (error) {
        showAlert(error.message || 'Failed to create section', 'error');
      } finally {
        setIsCreatingSection(false);
      }
    
  };

  const handleMove = async (args) => {
    try {
      setIsReordering(true);
      const { dragIds, dragNodes, index } = args;

      if (!dragIds?.length || !dragNodes?.length) {
        throw new Error('No section selected for reordering');
      }

      const draggedNode = dragNodes[0];
      if (!draggedNode || !draggedNode.data) {
        throw new Error('Invalid drag operation');
      }

      const updatedSections = [...documentation.sections];
      const oldIndex = updatedSections.findIndex(s => s.id === draggedNode.id);

      if (oldIndex === -1) {
        throw new Error('Section not found');
      }

      const [movedSection] = updatedSections.splice(oldIndex, 1);
      updatedSections.splice(index, 0, movedSection);

      const sectionOrders = {};
      updatedSections.forEach((section, idx) => {
        sectionOrders[section.id] = { order: idx };
      });

      await reorderSections(projectId, sectionOrders, documentType, interfaceId);
      showAlert('Sections reordered successfully', 'success');
      await refreshDocumentation();

    } catch (error) {
      
      showAlert(error.message || 'Failed to reorder sections', 'error');
    } finally {
      setIsReordering(false);
    }
  };

  const handleScrollToSection = (sectionId) => {
    // Add a small delay to let the scroll complete before updating active section
    setTimeout(() => {
      scrollToSection(sectionId);
    }, 100);
  };

  return (
    <aside className="h-full flex flex-col bg-white border-r border-gray-200">
      {/* Header remains the same */}

      {/* Header */}
      {/* <div className="shrink-0 p-3 sm:p-4 border-b border-gray-200">
        <h1 className="typography-body-lg sm:typography-heading-4 font-weight-bold text-gray-800 line-clamp-2">
          {documentation.doc?.Title}
        </h1>
        <div className="mt-1 sm:mt-2 typography-caption sm:typography-body-sm text-gray-500">
          <p>Version: {documentation.doc?.Version}</p>
          <p>Last Updated: {documentation.doc?.LastUpdated ? new Date(documentation.doc.LastUpdated).toLocaleDateString() : 'N/A'}</p>
        </div>
      </div> */}

      {/* Action Buttons */}
      <div className="shrink-0 p-3 sm:p-4 border-b border-gray-200">
        <ActionButtons
          onGeneratePDF={onGeneratePDF}
          onSyncToS3={onSyncToS3}
          onNavigateToDocuments={onNavigateToDocuments}
          generatingPdf={generatingPdf}
          syncingToS3={syncingToS3}
          onUpdateDoc={onUpdateDoc}  // Pass the handler
          docId={documentation?.doc?.id}

        />
      </div>

      {/* Table of Contents Header */}
      <div className="p-3 sm:p-4 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-white z-20">
        <h2 className="font-weight-semibold text-gray-700">Contents</h2>
        <button
          onClick={() => setIsCreating(true)}
          className="p-1.5 sm:p-2 hover:bg-gray-100 rounded-full transition-colors"
        >
          <Plus className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />
        </button>
      </div>

      {/* Sections List */}
      <div className="flex-1 min-h-0 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent ">
        {isCreating && (
          <div className="flex items-center px-3 sm:px-4 py-2 gap-2 bg-gray-50 border-b border-gray-200 sticky top-0 z-10">
            <input
              type="text"
              value={newSectionName}
              onChange={(e) => setNewSectionName(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleCreateSection();
                if (e.key === 'Escape') {
                  setIsCreating(false);
                  setNewSectionName('');
                }
              }}
              placeholder="New section name"
              autoFocus
              disabled={isCreatingSection}
              className="flex-1 px-2 py-1 typography-body-sm border rounded focus:outline-none focus:ring-2 focus:ring-primary"
            />
            <button
              onClick={handleCreateSection}
              disabled={isCreatingSection}
              className="p-1 hover:bg-gray-200 rounded transition-colors relative"
              title="Create section"
            >
              {isCreatingSection ? (
                <div className="animate-spin w-4 h-4 border-2 border-primary border-t-transparent rounded-full" />
              ) : (
                <Check className="w-4 h-4 text-green-600" />
              )}
            </button>
            <button
              onClick={() => {
                setIsCreating(false);
                setNewSectionName('');
              }}
              disabled={isCreatingSection}
              className="p-1 hover:bg-gray-200 rounded transition-colors"
              title="Cancel"
            >
              <X className="w-4 h-4 text-gray-600" />
            </button>
          </div>
        )}

        {sections && sections.length > 0 && (
          <div className="relative">
            {isReordering && (
              <div className="absolute inset-0 bg-white/50 flex items-center justify-center z-50">
                <div className="animate-spin w-6 h-6 border-3 border-primary border-t-transparent rounded-full" />
              </div>
            )}
            <Tree
              data={documentation.sections}
              openByDefault={true}
              width="100%"
              height={360}
              rowHeight={50}
              indent={5}
              onMove={handleMove}
              onClick={() => { }} // Prevent default click handling
              onSelect={() => { }} // Prevent default selection handling
              selection={false}
              dndOptions={{
                dragTriggerType: 'handle',
                canDragNodes: true,
                canDropOnNonTerminals: true,
                canReorder: true
              }}
            >
              {({ node, style, dragHandle }) => (
                <div
                  style={style}
                  className={`
                flex items-center px-3 sm:px-4 py-2 gap-2 border-b border-gray-100
                transition-colors duration-200
                ${activeSection === `section-${node.data.id}`?.toLowerCase()
                      ? 'bg-primary-50 text-primary font-weight-medium border-l-2 border-primary-600'
                      : 'text-gray-600 hover:bg-gray-50'}
              `}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleScrollToSection(`section-${node.data.id}`);
                  }}
                >
                  <div
                    ref={dragHandle}
                    className="cursor-move"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <GripVertical className="w-4 h-4 text-gray-400" />
                  </div>
                  <span className="flex-1 text-left line-clamp-2 typography-caption sm:typography-body-sm cursor-pointer">
                    {node.data.Title}
                  </span>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onDeleteSection(node.data.id);
                    }}
                    className="p-1.5 hover:bg-gray-100 rounded-full transition-colors"
                    title="Delete section"
                  >
                    <Trash2 className="w-4 h-4 text-gray-400 hover:text-red-500" />
                  </button>
                </div>
              )}
            </Tree>
          </div>
        )}
      </div>
    </aside>
  );
};

export default NavigationSidebar;