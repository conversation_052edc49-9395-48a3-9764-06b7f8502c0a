"use client";
import React, { useState, useEffect } from "react";
import dynamic from "next/dynamic";
import { Edit2 } from "lucide-react";
import { formatUTCToLocal } from "@/utils/datetime";
import { Accordion } from "@/components/UIComponents/Accordions/Accordion";
import ConfigureButtons from "@/components/ConfigureButtons/ConfigureButtons";
import { useSearchParams, useRouter, usePathname } from "next/navigation";
import { TOOLTIP_CONTENT } from "@/utils/constant/tooltip";
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";
import { BookOpen } from 'lucide-react';

// Dynamic imports
const MarkdownEditor = dynamic(() => import("@/components/Editor/MarkdownEditor"), {
  ssr: false,
});

const NoSSR = dynamic(() => import("@/components/Chart/MermaidChart"), { ssr: false });

const DocumentContent = ({
  documentation,
  renderHTML,
  onUpdateSection,
  projectId,
  onUpdateDoc
}) => {
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [currentSection, setCurrentSection] = useState(null);
  const [editingField, setEditingField] = useState(null);


  const router = useRouter()
  const searchParams = useSearchParams()
  const pathname = usePathname()

  const handleUpdateSubSection = (nodeId, nodeType) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("discussion", "new");
    newSearchParams.set("node_id", nodeId);
    newSearchParams.set("node_type", nodeType);
    router.push(`${pathname}?${newSearchParams.toString()}`);
  }


  useEffect(() => {

  }, [documentation]);

  const handleEditorClose = () => {
    setIsEditorOpen(false);
    setCurrentSection(null);
    setEditingField(null); // Reset editing field
  };

  const handleEdit = (section, fieldKey) => {
    setCurrentSection(section);
    setEditingField(fieldKey); // Store which field we're editing
    setIsEditorOpen(true);
  };

  const handleViewPastDiscussion = async (nodeId, nodeType) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set("view_past_discussions", "true");
    newSearchParams.set("node_id", nodeId);
    newSearchParams.set("discussion_type", nodeType);
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };





  const handleSave = async (newContent) => {
    if (onUpdateSection && currentSection?.id && editingField) {
      try {
        await onUpdateSection(
          currentSection.id,
          editingField, // Use the dynamic field key instead of hardcoded 'Content'
          newContent
        );
        handleEditorClose();
      } catch (error) {
        
      }
    }
  };

  const transformKey = (key) => {
    return key
      .replace(/_/g, " ")
      .replace(/([a-z])([A-Z])/g, "$1 $2")
      .replace(/\b\w/g, (char) => char.toUpperCase());
  };

  const shouldUseAccordion = (content) => {
    if (!content) return false;
    const lineCount = content.split("\n").length;
    const wordCount = content.split(/\s+/).length;
    return lineCount > 10 || wordCount > 200;
  };

  // Removed formatTechStack - let renderHTML handle all markdown formatting consistently

  const renderContent = (content, isTechStack, renderToHtml) => {
    if (!content) return <></>;
    // Removed isTechStack special handling - let renderHTML handle all markdown formatting
    if (renderToHtml) {
      return <div dangerouslySetInnerHTML={{ __html: renderHTML(content) }} />;
    }
    return <>{content}</>;
  };

  const renderField = (key, value, metadata) => {
    if (!key || !metadata?.[key]) return null;
    const label = transformKey(key);

    if (metadata[key]?.hidden === true) return null;
    const displayType = metadata[key]?.display_type || "text";
    const isTechStack = label === "Tech Stack Choices" || label === "Recommended Tech Stack";

    switch (label) {
      case "Title":
        return (
          <div key={key} className="bg-white rounded-lg p-4 mb-4">
            <span className="typography-body-lg font-weight-semibold text-[#2A3439]">{value}</span>
          </div>
        );


      case "Description":
      case "Content":
        return (
          <div className="bg-white rounded-lg mb-2" key={key}>
            <Accordion
              title={label}
              defaultOpen={true}
              preview={`View ${key.toLowerCase()}`}
              type={displayType}
              onEdit={() => handleEdit(documentation?.doc, key)} // Pass the field key
            >
              <div className="text-[#464F60]">
                {renderContent(value, isTechStack, true)}
              </div>
            </Accordion>
          </div>
        );

      case "Last Updated":
        return (
          <div className="bg-white rounded-lg p-4 mb-4" key={key}>
            <strong className="font-weight-semibold text-[#2A3439] block mb-2">
              {label}
            </strong>
            <p className="text-[#464F60]">{formatUTCToLocal(value) || "N/A"}</p>
          </div>
        );

      // Add other cases as needed...

      default:
        if (shouldUseAccordion(value)) {
          return (
            <div className="bg-white rounded-lg mb-2" key={key}>
              <Accordion
                title={label}
                defaultOpen={true}
                preview={`View ${key.toLowerCase()}`}
                type={displayType}
                onEdit={() => handleEdit({ id: documentation?.doc?.id, Title: label, Content: value })}
              >
                <div className="text-[#464F60]">
                  {renderContent(value, isTechStack, true)}
                </div>
              </Accordion>
            </div>
          );
        }
        return (
          <div className="bg-white rounded-lg p-4 mb-4" key={key}>
            <div className="flex items-center justify-between mb-2">
              <span className="font-weight-semibold text-[#2A3439]">{label}</span>
              <button
                onClick={() => handleEdit({ id: documentation?.doc?.id, Title: label, Content: value })}
                className="flex items-center gap-2 px-3 py-1.5 typography-body-sm text-gray-600 hover:text-red-600 bg-white hover:bg-red-50 border border-red-300 rounded-md transition-colors duration-200"
              >
                <Edit2 size={14} className="text-red-600" />
                <span>Edit</span>
              </button>
            </div>
            <div className="text-[#464F60]">
              {renderContent(value, isTechStack, true)}
            </div>
          </div>
        );
    }
  };
  const renderSections = () => {
    return documentation?.sections?.map((section) => {
      // Create section-specific update props
      const sectionUpdateProps = {
        onUpdateClick: () => handleUpdateSubSection(section.id, 'Sub_Section'), // Pass section-specific ID
        buttonText: "Update SubSection",
        tooltip: TOOLTIP_CONTENT.Architecture.sad.update
      };

      return (
        <div
          key={section.id || section.Order}
          id={`section-${section.id}`}
          className="bg-white rounded-lg border border-gray-200 p-6"
        >
          <div className="flex justify-between items-center mb-4">
            <h2 className="typography-body-lg font-weight-semibold text-gray-800">
              {section.Title}
            </h2>

            <div className="flex gap-2">

              <ConfigureButtons
                updateProps={sectionUpdateProps}
              />
              <button
                onClick={() => handleEdit(section, 'Content')}
                className="flex items-center gap-2 px-3 py-1.5 typography-body-sm text-gray-600 hover:text-red-600 bg-white hover:bg-red-50 border border-gray-200 hover:border-red-300 rounded-md transition-colors duration-200"
              >
                <Edit2 size={14} className="text-red-600" />
                <span>Edit</span>
              </button>

              <button
                onClick={handleViewPastDiscussion}
                className="min-w-[100px] flex items-center gap-1 px-3 py-2 bg-white border border-gray-300 
                                        rounded-md text-black hover:bg-gray-100 focus:ring-2 focus:ring-gray-500 
                                        focus:ring-offset-2 typography-body-sm"
              >
                <BookOpen size={14} />
                History
              </button>

            </div>

          </div>
          <div className="typography-body-sm text-gray-600 prose-sm max-w-none">
            {section.Content && section.Content.trim() !== "" ? (
              section.Content.includes('<mermaid>') ? (
                (() => {
                  const chartDefinition = section.Content.match(/<mermaid>([\s\S]*?)<\/mermaid>/)?.[1];
                  const beforeMermaid = section.Content.split('<mermaid>')[0].trim();  // Content before <mermaid>
                  const afterMermaid = section.Content.split('</mermaid>')[1].trim(); // Content after </mermaid>

                  return (
                    <>
                      {beforeMermaid && <div dangerouslySetInnerHTML={{ __html: renderHTML(beforeMermaid) }} />}
                      {chartDefinition && <NoSSR chartDefinition={chartDefinition.trim()} />}
                      {afterMermaid && <div dangerouslySetInnerHTML={{ __html: renderHTML(afterMermaid) }} />}
                    </>
                  );
                })()
              ) : (
                <div dangerouslySetInnerHTML={{ __html: renderHTML(section.Content) }} />
              )
            ) : (
              <div className="typography-body-sm text-gray-600 mb-4">No Content Found</div>
            )}

          </div>

        </div>
      );
    });
  };
  return (
<div className="w-full p-2">
{/* Documentation Header */}
      <header className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
        <div className="flex items-center justify-between gap-3">
          <div className="typography-body-lg font-weight-semibold text-gray-800 mb-2">
            {documentation?.doc?.Title}
          </div>
          <span className="inline-flex items-center px-3 py-1 typography-caption font-weight-medium text-primary-700 bg-primary-50 border border-primary-200 rounded-full whitespace-nowrap ">
            Version: {documentation?.doc?.Version}
          </span>
        </div>
        <p className="typography-body-sm text-gray-600 mb-4">
          {documentation?.doc?.Description}
        </p>
        <div className="flex flex-wrap justify-between gap-3 typography-body-sm text-gray-500">
          <span className="bg-gray-100 px-4 py-2 rounded">
            Updated: {formatUTCToLocal(documentation?.doc?.LastUpdated)}
          </span>
          <BootstrapTooltip title={TOOLTIP_CONTENT.Architecture.sad.updateDoc} placement="bottom">
            <button
              onClick={() => onUpdateDoc(documentation?.doc?.id, 'DocumentationRoot')}
              className="flex items-center gap-2 px-4 py-2 typography-body-sm font-weight-medium text-primary bg-white hover:bg-primary-50 border border-primary-300 rounded-md transition-colors duration-200"
            >
              <Edit2 size={14} className="text-primary" />
              Update Documentation
            </button>
          </BootstrapTooltip>
        </div>
      </header>

      {/* Documentation Sections */}
      <div className="space-y-6">
        {documentation?.doc && Object.entries(documentation.doc).map(([key, value]) =>
          renderField(key, value, documentation.metadata)
        )}
        {renderSections()}
      </div>

      {/* Markdown Editor Modal */}
      {isEditorOpen && currentSection && (
        <MarkdownEditor
          isOpen={isEditorOpen}
          onClose={handleEditorClose}
          content={currentSection.Content}
          onSave={handleSave}
          title={`Edit ${currentSection.Title}`}
        />
      )}
    </div>
  );
};

export default DocumentContent;