"use client";

import React, { useEffect, useState, useContext } from "react";
import { useParams, usePathname, useRouter, useSearchParams } from "next/navigation";
import {
  getDocumentation,
  downloadDocumentationPDF,
  syncDocumentationToS3,
  deleteSection,
  createDocumentationRoot
} from "@/utils/documentationAPI";
import { updateNodeByPriority } from "@/utils/api";
import { renderHTML } from "@/utils/helpers";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import NavigationSidebar from "@/components/Documentation/NavigationSidebar";
import DocumentContent from "@/components/Documentation/DocumentContent";
import MobileNavToggle from "@/components/Documentation/MobileNavToggle";
import DeleteProjectModal from "@/components/Modal/DeleteProjectModal";
import Breadcrumb from "@/components/Modal/Breadcrumb"
import {TwoColumnSkeletonLoader} from "@/components/UIComponents/Loaders/LoaderGroup"
import { buildProjectUrl } from "@/utils/navigationHelpers";

const DocumentationPage = ({ documentType = "SAD" }) => {
    // Context and hooks
    const { showAlert } = useContext(AlertContext);
    const router = useRouter();
    const params = useParams();
    const searchParams = useSearchParams();
    const pathname = usePathname();

    // State management
    const [isNavOpen, setIsNavOpen] = useState(false);
    const [syncingToS3, setSyncingToS3] = useState(false);
    const [documentation, setDocumentation] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [activeSection, setActiveSection] = useState("");
    const [generatingPdf, setGeneratingPdf] = useState(false);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [sectionToDelete, setSectionToDelete] = useState(null);
    const [isDeleting, setIsDeleting] = useState(false);
    const [isDocumentationRootCreating, setIsDocumentationRootCreating] = useState(false)
    // Get interfaceId from params if documentType is API
    const interfaceId = documentType === "API" ? params?.interfaceId : null;

    // Validation for API documentation
    useEffect(() => {
      if (documentType === "API" && !interfaceId) {
        setError("Interface ID is required for API documentation");
        setLoading(false);
      }
    }, [documentType, interfaceId]);

    const cleanContentFromPTags = (content) => {
      return content.replace(/<p>/g, '').replace(/<\/p>/g, '');
    };

    const handleCreateDocumentationRoot = async () => {
      if (!params?.projectId) return;

      try {
        setIsDocumentationRootCreating(true);
        const response = await createDocumentationRoot(params?.projectId, documentType, interfaceId);

        if (response) {
          showAlert("Documentation Node created successfully", "success")
          await refreshDocumentation()
        }
      } catch (error) {
        showAlert("Failed to create Documentation", "error");
      } finally {
        setIsDocumentationRootCreating(false);
      }
    };

    const handleDeleteSection = async (sectionId) => {
      if (!sectionId) return;

      try {
        setIsDeleting(true);
        await deleteSection(params?.projectId, sectionId, documentType, interfaceId);
        showAlert('Section deleted successfully', 'success');
        await refreshDocumentation();
      } catch (error) {
        showAlert(error.message || 'Failed to delete section', 'error');
      } finally {
        setIsDeleting(false);
        setIsDeleteModalOpen(false);
        setSectionToDelete(null);
      }
    };

    const handleUpdateDoc = (nodeId, nodeType) => {
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set("discussion", "new");
      newSearchParams.set("node_id", nodeId);
      newSearchParams.set("node_type", nodeType);
      router.push(`${pathname}?${newSearchParams.toString()}`);
    };

    const refreshDocumentation = async () => {
      const data = await getDocumentation(params?.projectId, documentType, interfaceId);
      if (data?.length > 0) {
        setDocumentation({
          doc: data[0].doc,
          sections: data[0].sections || [],
        });
      }
    };

 // Fetch documentation data
 useEffect(() => {
    const fetchDocumentation = async () => {
      try {
        // Skip fetching if API doc without interfaceId
        if (documentType === "API" && !interfaceId) return;

        const data = await getDocumentation(params?.projectId, documentType, interfaceId);
        if (data?.length > 0) {
          const processedData = {
            doc: data[0].doc,
            sections: data[0].sections || [],
          };
          setDocumentation(processedData);
        } else {
          setDocumentation(null);
        }
        setLoading(false);
      } catch (err) {
        setError(err?.message || "Error fetching documentation");
        setLoading(false);
      }
    };

    if (params?.projectId) {
      fetchDocumentation();
    }
  }, [params?.projectId, documentType, interfaceId]);

  // Handle scroll spy for active section
  useEffect(() => {
    const handleScroll = () => {
      if (!documentation?.sections) return;

      const mainContent = document.getElementById("main-content");
      if (!mainContent) return;

      const sectionIds = documentation.sections.map((section) =>
        `section-${section.id}`
      );
      let currentSection = "";
      for (const sectionId of [...sectionIds].reverse()) {
        const element = document.getElementById(sectionId);
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= 300) {
            currentSection = sectionId;
            break;
          }
        }
      }

      if (currentSection && currentSection !== activeSection) {
        setActiveSection(currentSection);
      }
    };

    const mainContent = document.getElementById("main-content");
    if (mainContent) {
      let timeout;
      const debouncedHandler = () => {
        if (timeout) {
          clearTimeout(timeout);
        }
        timeout = setTimeout(handleScroll, 100);
      };

      mainContent.addEventListener("scroll", debouncedHandler);
      handleScroll();
      return () => {
        mainContent.removeEventListener("scroll", debouncedHandler);
        if (timeout) {
          clearTimeout(timeout);
        }
      };
    }
  }, [documentation, activeSection]);

  // Handle section navigation
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    const mainContent = document.getElementById("main-content");
    if (element && mainContent) {
      const topOffset = element.offsetTop;
      mainContent.scrollTo({
        top: topOffset - 50,
        behavior: "smooth",
      });
      setIsNavOpen(false);
    }
  };

  // Handle S3 sync
  const handleSyncToS3 = async () => {
    try {
      setSyncingToS3(true);
      await syncDocumentationToS3(params?.projectId, documentType, interfaceId);
      showAlert("Documentation saved successfully", "success");
    } catch (error) {
      
      showAlert(error.message || "Failed to save documentation", "error");
    } finally {
      setSyncingToS3(false);
    }
  };

  // Handle navigation to documents
  const navigateToDocuments = () => {
    router.push(`/projects/${params?.projectId}/documents?type=${documentType}`);
  };

  // Handle PDF generation
  const generatePDF = async () => {
    try {
      setGeneratingPdf(true);
      await downloadDocumentationPDF(params?.projectId, documentType, interfaceId);
      showAlert("PDF Download started", "success");
    } catch (error) {
      showAlert("Error generating PDF: " + error, "error");
    } finally {
      setGeneratingPdf(false);
    }
  };

  const handleSectionUpdate = async (sectionId, propertyKey, newContent) => {
    try {
      if (!sectionId) {
        throw new Error('Section ID is missing');
      }
      const cleanedContent = cleanContentFromPTags(newContent);

      await updateNodeByPriority(
        sectionId,
        propertyKey,
        cleanedContent
      );

      await refreshDocumentation();
      showAlert("Section updated successfully", "success");
    } catch (error) {
      
      showAlert(`Failed to update section: ${error.message}`, "error");
    }
  };

  // Loading state
  if (loading) {
    return <TwoColumnSkeletonLoader />;
  }

  // Error state
  if (error) {
    return (
      <div className="flex justify-center items-center h-screen p-4">
        <div className="text-red-500 text-center bg-red-50 p-6 rounded-lg shadow max-w-md">
          <h2 className="typography-body-lg font-weight-semibold mb-2">
            Error Loading Documentation
          </h2>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  // No documentation state
  if (!documentation?.doc) {
    return (
      <div>
        <EmptyStateView
          type={`${documentType.toLowerCase()}Documentation`}
          onClick={handleCreateDocumentationRoot}
          isLoading={isDocumentationRootCreating}
        />
      </div>
    );
  }

  const breadcrumbItems = [
    { label: 'Home', href: buildProjectUrl(params?.projectId, 'architecture/api-docs') },
    { label: documentation?.doc?.Title || 'Document', href: '#', active: true } // Assuming documentation contains doc.Title
  ]

  // Main render
  return (
    <>
     <Breadcrumb items={breadcrumbItems}/>
     <div className="relative flex h-[calc(100vh-150px)] overflow-hidden bg-gray-50">
     {isNavOpen && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-30 lg:hidden"
          onClick={() => setIsNavOpen(false)}
        />
      )}

      {/* Navigation Sidebar */}
      <div
        className={`
          fixed lg:relative
          inset-y-0 left-0
          w-[85vw] sm:w-[60vw] md:w-[40vw] lg:w-[25vw] xl:w-[20vw]
          transform
          ${isNavOpen ? "translate-x-0" : "-translate-x-full"}
          lg:translate-x-0
          transition-transform duration-200 ease-in-out
          z-40 lg:z-0
          overflow-hidden
        `}
      >
        <NavigationSidebar
          documentation={documentation}
          activeSection={activeSection}
          scrollToSection={scrollToSection}
          onGeneratePDF={generatePDF}
          onSyncToS3={handleSyncToS3}
          onNavigateToDocuments={navigateToDocuments}
          generatingPdf={generatingPdf}
          syncingToS3={syncingToS3}
          projectId={params?.projectId}
          refreshDocumentation={refreshDocumentation}
          interfaceId={interfaceId}
          documentType={documentType}
          onDeleteSection={(sectionId) => {
            setSectionToDelete(sectionId);
            setIsDeleteModalOpen(true);
          }}
          onUpdateDoc={handleUpdateDoc}
        />
      </div>

      {/* Main Content */}
      <main
        id="main-content"
  className="flex-1 relative w-full lg:w-[calc(100%-25vw)] xl:w-[calc(100%-20vw)] 2xl:w-[calc(100%-300px)]
                  overflow-y-auto overflow-x-hidden
                  transition-all duration-200 ease-in-out"
      >
        <DocumentContent
          documentation={documentation}
          renderHTML={renderHTML}
          onUpdateSection={handleSectionUpdate}
          projectId={params?.projectId}
          onUpdateDoc={handleUpdateDoc}
          style={{ width: '100%', maxWidth: 'none' }}
          />
      </main>

      {/* Mobile Navigation Toggle */}
      <MobileNavToggle
        isOpen={isNavOpen}
        onToggle={() => setIsNavOpen(!isNavOpen)}
      />

      <DeleteProjectModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setSectionToDelete(null);
        }}
        onDelete={() => handleDeleteSection(sectionToDelete)}
        isDeleting={isDeleting}
        type="section"
      />
    </div>
    </>
  );
};

export default DocumentationPage;