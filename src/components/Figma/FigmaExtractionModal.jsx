// src/components/Figma/FigmaExtractionModal.jsx
'use client'
import { useState, useEffect, useRef } from 'react';
import FigmaExtractionTabs from './FigmaExtractionTabs';
import FigmaDiscussionChatPanel from './FigmaDiscussionChatPanel';
import { useFigmaExtraction } from '../Context/FigmaExtractionContext';
import { Maximize, Minimize, X } from 'lucide-react';
import { useWebSocket } from "../Context/WebsocketContext";
import MergeButtonSection from './MergeButton';

const FigmaExtractionModal = ({ isOpen, onClose, projectId, extractionStatus, extractionProgress, logMessages, selectedDesign }) => {
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [activeTab, setActiveTab] = useState('design');
  const { selectedFrame, setSelectedFrame } = useFigmaExtraction()
  const modalRef = useRef(null);
  const {files} = useWebSocket();

  // When selectedDesign changes, set the first frame as the default selected frame
  useEffect(() => {
    if (selectedDesign && selectedDesign.frames && selectedDesign.frames.length > 0) {
      setSelectedFrame(selectedDesign.frames[0]);
    }
  }, [selectedDesign]);

  const toggleFullScreen = () => {
    setIsFullScreen(!isFullScreen);
  };

  const handleBackgroundClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <>
      <div
        className={`fixed inset-0 backdrop-blur-modal flex justify-center items-center z-50 ${isFullScreen ? "w-full h-full" : ""
          }`}
        onClick={handleBackgroundClick}
      >
        <div
          ref={modalRef}
          className={`bg-white ${isFullScreen ? "w-full h-full" : "w-[95%] h-[95%]"
            } rounded-lg flex flex-col relative overflow-hidden`}
        >
          {/* Modal Header */}
          <div className="flex flex-col border-b border-gray-200">
            <div className="flex items-center justify-between p-1.5">
              <div className="flex items-center">
                <h2 className="typography-body-lg font-weight-semibold text-gray-900 pl-2.5">Figma Extraction</h2>
                {/* Frame selection dropdown moved here */}
                {selectedDesign && selectedDesign.frames && selectedDesign.frames.length > 0 && (
                  <div className="ml-6 relative">
                    {/* The select element */}
                    <select
                      className="h-8 w-40 bg-white px-3 py-1 typography-body-sm  text-gray-800 pr-8 border border-gray-200 rounded-md outline-none focus:ring-0 focus:outline-none appearance-none" // Added pr-8 for padding-right
                      value={selectedFrame?.id || ''}
                      onChange={(e) => {
                        const frameId = e.target.value;
                        const frame = selectedDesign.frames.find(f => f.id === frameId);
                        if (frame) {
                          setSelectedFrame(frame);
                        }
                      }}
                    >
                      {selectedDesign.frames.map(frame => (
                        <option key={frame.id} value={frame.id}>
                          {frame.name}
                        </option>
                      ))}
                    </select>

                    {/* Custom chevron positioned absolutely */}
                    {/* <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none text-gray-500">
                      <ChevronDown size={16} />
                    </div> */}
                  </div>
                )}
              </div>

              <div className="flex items-center space-x-2">
                {activeTab === 'Code' && (
                <MergeButtonSection isDisabled={!files || files.length === 0} />)}
                <button
                  className="p-2 rounded hover:bg-gray-100"
                  onClick={toggleFullScreen}
                  title={isFullScreen ? "Exit fullscreen" : "Fullscreen"}
                >
                  {isFullScreen ? <Minimize className="w-4 h-4" /> : <Maximize className="w-4 h-4" />}
                </button>
                <button
                  className="p-2 rounded hover:bg-gray-100"
                  onClick={onClose}
                  title="Close"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Content area */}
          <div className="flex flex-1 h-full overflow-hidden">
            {/* Left side: Chat Panel */}
            <div className="w-[30%] border-r border-gray-200 overflow-hidden flex flex-col">
              {/* Discussion label */}
              {/* <div className="border-b border-gray-200 flex items-center" style={{ height: '46px' }}>
                <div className="px-4 font-weight-medium text-gray-900">Discussion</div>
              </div> */}

              {/* Chat Panel */}
              <div className="flex-1 overflow-hidden">
                <FigmaDiscussionChatPanel attachmentEnabled={true} maxFiles={5} />
              </div>
            </div>

            {/* Right side: Tabs Content */}
            <div className="flex-1 overflow-hidden">
              <FigmaExtractionTabs
                projectId={projectId}
                extractionStatus={extractionStatus}
                extractionProgress={extractionProgress}
                logMessages={logMessages}
                selectedDesign={selectedDesign}
                selectedFrame={selectedFrame}
                onTabChange={(tab) => setActiveTab(tab)}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default FigmaExtractionModal;