// src/components/Figma/FigmaExtractButton.jsx
'use client'
import { useState, useContext } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import FigmaExtractionModal from './FigmaExtractionModal';
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { Download } from "lucide-react";
import { TOOLTIP_CONTENT } from '@/utils/constant/tooltip';
import { FigmaExtractionContext } from '@/components/Context/FigmaExtractionContext';

const FigmaExtractButton = ({ projectId , selectedDesignId = null }) => {
  const { showFigmaExtactionModal, figmaDiscussionId, setShowFigmaExtactionModal, setFigmaDiscussionId, messagesHistory, setMessagesHistory, selectedDesignData } = useContext(FigmaExtractionContext);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [extractionStatus, setExtractionStatus] = useState(null);
  const [extractionProgress, setExtractionProgress] = useState(null);
  const [logMessages, setLogMessages] = useState(null);
  
  const router = useRouter();
  const searchParams = useSearchParams();

  // Function to fetch extraction status from API
  const fetchExtractionStatus = async () => {
    try {
      
      
      // For now, we'll use mock data
      const data = {
        status: 'Running',
        taskId: `figma-extraction-${projectId}`,
        description: 'Starting task: Figma Screen Extraction'
      };
      
      setExtractionStatus(data);
      
      // Sample extraction progress data
      // In a real implementation, this would come from the API as well
      const progressData = {
        percentage: 45,
        totalElements: 120,
        processedElements: 54,
        succeededElements: 48,
        failedElements: 6
      };
    //   setExtractionProgress(progressData);
      


      
    } catch (error) {
      
    }
  };

  const handleOpenModal = async () => {
    // Create new URLSearchParams object with current params
    const params = new URLSearchParams(searchParams);
    // Add or update the figmaDiscussion parameter
    params.set('figmaDiscussion', 'new');
    
    // Update the URL with the new search params
    router.push(`?${params.toString()}`, { scroll: false });
    
    // Fetch the latest status when opening the modal
    await fetchExtractionStatus();
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    // Remove the figmaDiscussion parameter when closing
    const params = new URLSearchParams(searchParams);
    params.delete('figmaDiscussion');
    params.delete('figmaDiscussionId')
    setFigmaDiscussionId(null)
    setShowFigmaExtactionModal(false);
    setMessagesHistory(false)
    // Update the URL without the parameter
    router.push(`?${params.toString()}`, { scroll: false });
    
  };
 return (
    <>
      <DynamicButton
        type="button"
        size="default"
        variant="primary"
        icon={Download}
        onClick={handleOpenModal}
        text="Extract"
        tooltip={TOOLTIP_CONTENT.Figma.Create_New || "Extract design elements"}
      />
      
      {showFigmaExtactionModal && (
        <FigmaExtractionModal 
          isOpen={showFigmaExtactionModal} 
          onClose={handleCloseModal} 
          projectId={projectId}
          extractionStatus={extractionStatus}
          extractionProgress={extractionProgress}
          logMessages={logMessages}
          selectedDesign={selectedDesignData}
        />
      )}
    </>
  );
};

export default FigmaExtractButton;