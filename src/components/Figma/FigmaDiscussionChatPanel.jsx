'use client'
import * as React from "react";
import { useState, useEffect, useRef, useCallback, memo } from "react";
import Image from "next/image";
import Cookies from 'js-cookie';
import { useParams, useSearchParams } from "next/navigation";
import chaticon from "../../../public/images/chat_icon.svg";
import { extractTextV1, uploadFile } from "@/utils/fileAPI";
import FileContentModal from "../Modal/FileContentModal";
import AttachmentButton from "../Buttons/AttachmentButton";
import AttachmentWindow from "../File/AttachmentWindow";
import FileAttachment from "../File/FileAttachment";
import { sendFigmaExtraction } from "@/utils/FigmaAPI";
import { useFigmaExtraction } from '../Context/FigmaExtractionContext';
import dynamic from 'next/dynamic';
import { v4 as uuid } from 'uuid';
import { FixedSizeList as List } from 'react-window';
import { cleanupWebSocketConnections } from "@/utils/FigmaAPI";
import { useWebSocket } from "../Context/WebsocketContext";

// Create a lazy-loaded version of CodeBlock with no SSR
const CodeBlock = dynamic(() => import('../CodeBlock'), {
  ssr: false,
  loading: () => <div className="p-2">Loading code formatter...</div>
});

// Reuse the MessageItem component from DiscussionChatPanel
const MessageItem = memo(({ message, onFileView, name }) => {
  // First normalize the message format
  const role = message.role || (message.sender === "User" ? "user" : "assistant");
  const content = message.content || message.text || "";
  const timestamp = message.timestamp || message.created_at || "Unknown";
  const attachments = message.file_attachments || [];

  // Handle tool_calls messages from the assistant
  if (message.tool_calls) {
    return (
      <div className="px-3 w-full">
        <div className="flex w-full px-4 py-4 bg-gray-100 sm:px-4 rounded-lg">
          <div className="w-full">
            <div className="typography-body-sm text-gray-500">Function Call:</div>
            {message.tool_calls.map((toolCall, index) => (
              <div key={index} className="mt-1 bg-gray-200 p-2 rounded">
                <div className=" typography-body-sm">{toolCall.function.name}({toolCall.function.arguments})</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Handle tool response messages
  if (role === 'tool') {
    return (
      <div className="px-3 w-full">
        <div className="flex w-full px-4 py-4 bg-primary-50 sm:px-4 rounded-lg">
          <div className="w-full">
            <div className="typography-body-sm text-gray-500">Tool Response: {message.name}</div>
            <div className="mt-1  typography-body-sm whitespace-pre-wrap">{content}</div>
          </div>
        </div>
      </div>
    );
  }

  // User message handling
  if (role === "user") {
    return (
      <div className="flex flex-row px-4 py-2 sm:px-4">
        <Image
          className="mr-2 flex h-8 w-8 rounded-full"
          src={`https://ui-avatars.com/api/?name=${message.userDetails?.name || name}&background=00000&color=FFFFFF`}
          alt="User Avatar"
          width={32}
          height={32}
        />
        <div className="flex flex-col max-w-3xl items-center">
          <React.Suspense fallback={<div>Loading...</div>}>
            <CodeBlock
              markdownString={content}
              message_end={message.message_end || message.finished}
              timestamp={timestamp}
              user="user"
            />
          </React.Suspense>
          {attachments.length > 0 && (
            <div className="mt-2 space-y-2">
              {attachments.map((attachment, index) => (
                <FileAttachment
                  key={index}
                  attachment={attachment}
                  onView={() => onFileView(attachment)}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    );
  }

  // Assistant message handling
  return (
    <div className="px-3 w-full">
      <div className="flex w-full px-4 py-4 bg-gray-100 sm:px-4 rounded-lg">
        <React.Suspense fallback={<div>Loading...</div>}>
          <CodeBlock
            markdownString={content}
            message_end={message.message_end || message.finished}
            timestamp={timestamp}
          />
        </React.Suspense>
      </div>
    </div>
  );
});

MessageItem.displayName = 'MessageItem';

const MessagesSection = memo(({ messages, name, endOfMessagesRef, onFileView }) => {
  // Check if we have messages to display
  const hasMessages = messages && messages.length > 0;

  if (messages.length > 50) {
    const itemSize = 150;

    const Row = ({ index, style }) => (
      <div style={style} className={`mb-2 ${messages[index].streaming ? 'animate-fade-in' : ''}`}>
        <MessageItem
          message={messages[index]}
          onFileView={onFileView}
          name={name}
        />
      </div>
    );

    return (
      <div className="flex-1 overflow-hidden mt-1 typography-body-sm chat-custom-scrollbar leading-6 text-slate-900 sm:typography-body sm:leading-7">
        <List
          height={600}
          itemCount={messages.length}
          itemSize={itemSize}
          width="100%"
        >
          {Row}
        </List>
        <div ref={endOfMessagesRef} />
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto mt-1 typography-body-sm custom-scrollbar leading-6 text-slate-900 sm:typography-body sm:leading-7">
      {hasMessages ? (
        messages.map((message) => (
          <div
            key={message.messageUuid || message.id}
            className={`mb-2 ${message.streaming ? 'animate-fade-in' : ''}`}
          >
            <MessageItem
              message={message}
              onFileView={onFileView}
              name={name}
            />
          </div>
        ))
      ) : (
        // Add a placeholder or welcome message when no messages exist
        <div className="flex items-center justify-center h-full p-4 text-gray-500">
          <div className="text-center">
            <p>Start a new conversation about your Figma design.</p>
            <p className="typography-body-sm mt-2">Type your message below to begin.</p>
          </div>
        </div>
      )}
      <div ref={endOfMessagesRef} />
    </div>
  );
});

MessagesSection.displayName = 'MessagesSection';


export default function FigmaDiscussionChatPanel({ maxFiles = 5, attachmentEnabled = true }) {
  const { figmaDiscussionId, selectedFrame, messagesHistory, designType } = useFigmaExtraction();
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentMessage, setCurrentMessage] = useState("");
  const [attachedFiles, setAttachedFiles] = useState([]);
  const [showAttachmentWindow, setShowAttachmentWindow] = useState(false);
  const [fileStatuses, setFileStatuses] = useState({});
  const [selectedFile, setSelectedFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const { connectToSession, getConnection, disconnectFromSession, setIsCompleted, updateFileContent,files, setFiles } = useWebSocket();
  const searchParams = useSearchParams();
  const params = useParams();
  const selectedDesignId = searchParams.get("selectedDesignId")
  const name = Cookies.get('username');
  const textareaRef = useRef(null);
  const fileInputRef = useRef(null);
  const attachmentButtonRef = useRef(null);
  const endOfMessagesRef = useRef(null);
  const dropZoneRef = useRef(null);
  const controllerRef = useRef(new AbortController());
  const [wsConnection, setWsConnection] = useState(null);
  const [wsStatus, setWsStatus] = useState('disconnected'); // STATE: 'disconnected', 'connecting', 'connected'
  const [wsConnected, setWsConnected] = useState(false);
  const [assetFiles, setAssetFiles] = useState({}); // Track asset files by task_id
  const [hasRequiredIds, setHasRequiredIds] = useState(false);

  // Add a ref to track stream state
  const streamActiveRef = useRef(false);

  // Add a new ref to track error state
  const hasErrorRef = useRef(false);

  // Add a messageMap to track messages by ID
  const [messageMap, setMessageMap] = useState({});

  // Add this to your component's state
  const [messagesMap, setMessagesMap] = useState({});

  // Add this at the component level where other state variables are defined
  const [shouldScroll, setShouldScroll] = useState(false);

  const updateOrCreateMessage = useCallback((parsedData) => {
    // Handle stop signal
    if (parsedData.stop) {
      setLoading(false);
      streamActiveRef.current = false;
      return;
    }

    setMessages(prev => {
      const messageIndex = prev.findIndex(msg => msg.message_id === parsedData.message_id);

      if (messageIndex === -1) {
        // Create new message
        const newMessage = {
          id: Date.now(),
          message_id: parsedData.message_id,
          text: '',
          tool_calls: [],
          tool_responses: [],
          role: parsedData.role || 'assistant'
        };

        // Handle different message types
        if (parsedData.tool_call_id) {
          if (parsedData.func_name) {
            // This is a tool call
            newMessage.tool_calls.push({
              id: parsedData.tool_call_id,
              function: {
                name: parsedData.func_name,
                arguments: parsedData.func_args
              }
            });
          } else if (parsedData.name && parsedData.content) {
            // This is a tool response
            newMessage.tool_responses.push({
              tool_call_id: parsedData.tool_call_id,
              name: parsedData.name,
              content: parsedData.content
            });
          }
        } else if (parsedData.content) {
          newMessage.text = parsedData.content;
        }

        return [...prev, newMessage];
      } else {
        // Update existing message
        return prev.map((msg, idx) => {
          if (idx !== messageIndex) return msg;

          const updatedMessage = { ...msg };

          if (parsedData.tool_call_id) {
            if (parsedData.func_name) {
              // Update or add tool call
              const existingCallIndex = updatedMessage.tool_calls?.findIndex(
                call => call.id === parsedData.tool_call_id
              );

              if (existingCallIndex === -1) {
                updatedMessage.tool_calls = [
                  ...(updatedMessage.tool_calls || []),
                  {
                    id: parsedData.tool_call_id,
                    function: {
                      name: parsedData.func_name,
                      arguments: parsedData.func_args
                    }
                  }
                ];
              } else {
                updatedMessage.tool_calls[existingCallIndex].function.arguments = parsedData.func_args;
              }
            } else if (parsedData.name && parsedData.content) {
              // Update or add tool response
              const existingResponseIndex = updatedMessage.tool_responses?.findIndex(
                response => response.tool_call_id === parsedData.tool_call_id
              );

              if (existingResponseIndex === -1) {
                updatedMessage.tool_responses = [
                  ...(updatedMessage.tool_responses || []),
                  {
                    tool_call_id: parsedData.tool_call_id,
                    name: parsedData.name,
                    content: parsedData.content
                  }
                ];
              } else {
                updatedMessage.tool_responses[existingResponseIndex].content = parsedData.content;
              }
            }
          } else if (parsedData.content) {
            updatedMessage.text = parsedData.content;
          }

          return updatedMessage;
        });
      }
    });
  }, []);

  const processStructuredMessage = (message) => {
    // Check if message has a content array (structured format)
    if (message.content && Array.isArray(message.content)) {
      let messageText = "";
      const file_attachments = [];

      // Process each content item
      message.content.forEach(item => {
        if (item.type === "text") {
          messageText += item.text;
        }
        else if (item.type === "image_url" && item.image_url && item.image_url.url) {
          // Extract the image data
          const imageUrlData = item.image_url.url;

          // For base64 images
          if (imageUrlData.startsWith('data:image/')) {
            // Extract image type and data
            const matches = imageUrlData.match(/^data:([A-Za-z-+\/]+);base64,(.+)$/);

            if (matches && matches.length === 3) {
              const type = matches[1];
              const data = matches[2];

              // Create a file attachment for the image
              file_attachments.push({
                file_name: `image_${new Date().getTime()}.${type.split('/')[1] || 'png'}`,
                file_type: type,
                file_size: Math.ceil(data.length * 0.75), // Approximate size calculation
                file_kind: 'image',
                extracted_content: 'Image content',
                data_url: imageUrlData,
                original_url: imageUrlData,
                preview_url: imageUrlData
              });
            }
          }
          else {
            // For regular URLs
            file_attachments.push({
              file_name: `image_${new Date().getTime()}.png`,
              file_type: 'image/png',
              file_kind: 'image',
              file_url: imageUrlData,
              extracted_content: 'Image content'
            });
          }
        }
      });

      // Return the processed message with text and attachments
      return {
        ...message,
        text: messageText || message.text,
        content: messageText || message.text,
        file_attachments: file_attachments
      };
    }

    return message;
  };

  useEffect(() => {
    if ((!searchParams.get("figmaDiscussionId")) && (!searchParams.get("selectedDesignId"))){
      setHasRequiredIds(false);

    }
    else{
      setHasRequiredIds(true)
    }

    if (!searchParams.get("figmaDiscussionId")) return;


    // Create connection
    connectToSession(searchParams.get("figmaDiscussionId"));
    const connection = getConnection(searchParams.get("figmaDiscussionId"));
    setWsConnection(connection);

    const setupEventHandlers = (ws) => {
      // Set up message handler
      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);


          // Handle stop signal
          if (data.stop === true) {

            setLoading(false);
            streamActiveRef.current = false;
            setShouldScroll(true);
            return;
          }

          // Handle different message types
          if (data.type) {
            switch (data.type) {
              case 'data':
                // Process payload data
                handleStreamingMessage({ data: `data: ${JSON.stringify(data.payload || data.data)}` });
                break;
              case 'tool_stream':
                // Process tool stream data
                handleToolStreamData(data);
                break;
              case 'error':

                handleStreamError((data.payload && data.payload.message) ||
                                  (data.data && data.data.message) ||
                                  'An error occurred');
                break;

              case 'end':

                setLoading(false);
                streamActiveRef.current = false;
                setShouldScroll(true);
                break;

              case 'connected':

                setWsStatus('connected');
                setWsConnected(true);
                break;

              case 'disconnected':

                setWsStatus('disconnected');
                setWsConnected(false);
                break;

              default:
                // For any other message types
                if (data.payload) {
                  handleStreamingMessage({ data: `data: ${JSON.stringify(data.payload)}` });
                } else if (data.data && (data.data.message || data.data.content)) {
                  handleStreamingMessage({
                    data: `data: ${JSON.stringify({
                      message_id: data.data.message_uuid || data.message_id,
                      role: "assistant",
                      content: data.data.message || data.data.content,
                      message_end: data.data.message_end || false,
                      created_at: new Date().toISOString()
                    })}`
                  });
                } else {
                  // Pass the data directly
                  handleStreamingMessage({ data: `data: ${JSON.stringify(data)}` });
                }
            }
          } else {
            // Handle direct message format
            handleStreamingMessage({ data: `data: ${JSON.stringify(data)}` });
          }
        } catch (err) {

          handleStreamError('Failed to parse message data');
        }
      };

      // Set up close handler
      ws.onclose = () => {

        setWsStatus('disconnected');
        setWsConnected(false);
      };

      // Set up error handler
      ws.onerror = (error) => {

        setWsStatus('disconnected');
        setWsConnected(false);
        handleStreamError("WebSocket connection error. Please try again.");
      };
    };

    // Set up event handlers if connection is available and open
    if (connection && connection.readyState === WebSocket.OPEN) {

      setWsStatus('connected');
      setWsConnected(true);
      setupEventHandlers(connection);
    } else if (connection) {
      // For connections that aren't open yet, add open handler
      connection.addEventListener('open', () => {

        setWsStatus('connected');
        setWsConnected(true);
        setupEventHandlers(connection);
      });
    }

    // Cleanup function
    return () => {
      if (connection) {
        connection.close();
        setWsConnected(false);
        setWsStatus('disconnected');
      }
    };
  }, [searchParams, updateOrCreateMessage]);

  useEffect(() => {
    if ((messagesHistory.length > 0) && (Object.keys(messagesMap).length === 0)) {
      const newMessagesMap = {};
      messagesHistory.forEach(msg => {
        // Process the message if it has structured content
        const processedMsg = processStructuredMessage(msg);

        const messageId = processedMsg.id || processedMsg.messageUuid || processedMsg.message_id || Date.now();
        newMessagesMap[messageId] = {
          id: messageId,
          role: processedMsg.role || (processedMsg.sender === "User" ? "user" : "assistant"),
          content: processedMsg.text || processedMsg.content,
          tool_calls: processedMsg.tool_calls,
          name: processedMsg.name,
          sender: processedMsg.sender,
          file_attachments: processedMsg.file_attachments,
          timestamp: processedMsg.timestamp || processedMsg.created_at,
          finished: true
        };
      });
      setMessagesMap(newMessagesMap);
    }
  }, [messagesHistory, messagesMap]);

  const handleFormSubmit = async (event) => {
    event.preventDefault();
    const message = textareaRef.current.value;

    // Don't proceed if already loading or streaming, or if there's no message
    if (isUploading || loading || !message?.trim() || !figmaDiscussionId || streamActiveRef.current) {
      return;
    }

    try {
      // Disable the chat panel and set streaming state
      setLoading(true);
      streamActiveRef.current = true;
      hasErrorRef.current = false;

      // Create new controller for this request
      controllerRef.current = new AbortController();
      textareaRef.current.value = '';

      // Create a unique ID for the user message
      const userMessageId = uuid();

      // Add user message to messagesMap
      setMessagesMap(prev => ({
        ...prev,
        [userMessageId]: {
          id: userMessageId,
          role: "user",
          sender: "User",
          content: message,
          file_attachments: attachedFiles,
          timestamp: new Date().toISOString(),
          finished: true
        }
      }));

      // Trigger scroll after adding user message
      setShouldScroll(true);

      // Prepare message to send
      let message_to_send = {
        user_message: message,
        file_attachments: attachedFiles
      };

      // Ensure WebSocket connection is active
      if (!wsConnection || wsConnection.readyState !== WebSocket.OPEN) {

        const connection = connectToSession(figmaDiscussionId);
        setWsConnection(connection);

        // Wait for connection to open before sending the request
        if (connection && connection.readyState !== WebSocket.OPEN) {
          await new Promise(resolve => {
            connection.addEventListener('open', resolve, { once: true });
            // Add timeout to prevent hanging indefinitely
            setTimeout(resolve, 5000);
          });
        }
      }

      // Send API request to initiate the figma extraction process
      await sendFigmaExtraction(
        figmaDiscussionId,
        selectedDesignId,
        selectedFrame,
        message_to_send,
        designType
      );

      setCurrentMessage("");
      setAttachedFiles([]);
      setFileStatuses({});
    } catch (error) {


      const errorId = uuid();
      setMessagesMap(prev => ({
        ...prev,
        [errorId]: {
          id: errorId,
          role: 'assistant',
          content: `Failed to send message: ${error.message || 'Please try again.'}`,
          timestamp: new Date().toISOString(),
          finished: true
        }
      }));

      setLoading(false);
      streamActiveRef.current = false;
      setShouldScroll(true);
    }
  };
  // Enhanced handleStreamError to properly enable the chat panel on errors
  const handleStreamError = (errorMessage) => {
    // Abort the current controller
    controllerRef.current.abort();

      // Log the error


      // Add error message to the chat
      const errorId = uuid();
      setMessagesMap(prev => ({
        ...prev,
        [errorId]: {
          id: errorId,
          role: 'assistant',
          content: errorMessage,
          timestamp: new Date().toISOString(),
          finished: true
        }
      }));

      // Enable the chat panel on error
      setLoading(false);
      streamActiveRef.current = false;
      hasErrorRef.current = true;
      setShouldScroll(true);


    };

  const handleKeyPress = (event) => {
    if (loading) return;
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      handleFormSubmit(event);
    }
  };

  const handleTextareaResize = (e) => {
    const textarea = e.target;
    textarea.style.height = 'auto';
    const newHeight = textarea.scrollHeight;
    textarea.style.height = `${newHeight}px`;
    const maxHeight = 150;
    if (newHeight > maxHeight) {
      textarea.style.height = `${maxHeight}px`;
      textarea.style.overflowY = 'scroll';
    } else {
      textarea.style.overflowY = 'hidden';
    }
  };

  const handleAttachmentClick = (e) => {
    e.stopPropagation();
    if (attachedFiles.length === 0) {
      fileInputRef.current?.click();
    } else {
      setShowAttachmentWindow(prev => !prev);
    }
  };

  const handleAddAttachment = (e) => {
    e.preventDefault();
    fileInputRef.current?.click();
  };

  const handleFileUpload = useCallback(async (files) => {
    setAttachedFiles(prevAttachedFiles => {
      const updatedFiles = [...prevAttachedFiles];
      const newFileStatuses = { ...fileStatuses };

      if (updatedFiles.length + files.length <= maxFiles) {
        files.forEach(file => {
          const existingFileIndex = updatedFiles.findIndex(f => f.file_name === file.name);
          if (existingFileIndex !== -1) return;

          const newFile = {
            file_name: file.name,
            file_type: file.type,
            file_size: file.size,
            file_kind: file.type.startsWith('image/') ? 'image' : 'document',
            extracted_content: 'Loading...'
          };
          updatedFiles.push(newFile);
          newFileStatuses[file.name] = 'loading';
        });

        setFileStatuses(newFileStatuses);

        files.forEach(async (file) => {
          try {
            setIsUploading(true);
            const formData = new FormData();
            formData.append('file', file);

            const response = file.type.startsWith('image/')
              ? await uploadFile(file, figmaDiscussionId)
              : await extractTextV1(formData);

            setAttachedFiles(prevFiles => {
              const updatedFiles = [...prevFiles];
              const index = updatedFiles.findIndex(f => f.file_name === file.name);
              if (index !== -1) {
                if (file.type.startsWith('image/')) {
                  updatedFiles[index] = { ...updatedFiles[index], ...response };
                } else {
                  updatedFiles[index].extracted_content = response.text || response.extracted_content;
                }
              }
              return updatedFiles;
            });

            setFileStatuses(prevStatuses => ({
              ...prevStatuses,
              [file.name]: 'loaded'
            }));
          } catch (error) {

            setAttachedFiles(prevFiles => prevFiles.filter(f => f.file_name !== file.name));
            setFileStatuses(prevStatuses => {
              const newStatuses = { ...prevStatuses };
              delete newStatuses[file.name];
              return newStatuses;
            });
          } finally {
            setIsUploading(false);
          }
        });
      } else {
        alert(`You can only attach up to ${maxFiles} files.`);
      }
      return updatedFiles;
    });
  }, [maxFiles, figmaDiscussionId, fileStatuses]);

  const handleFileChange = async (e) => {
    const files = e.target.files;
    if (files) {
      await handleFileUpload(Array.from(files));
    }
  };

  const handleFileView = useCallback((file) => {
    setSelectedFile(file);
  }, []);

  const handleClearAttachments = () => {
    setAttachedFiles([]);
    setShowAttachmentWindow(false);
  };

  useEffect(() => {
    const scrollTimeout = setTimeout(() => {
      endOfMessagesRef.current?.scrollIntoView({ behavior: "smooth" });
    }, 100);
    return () => clearTimeout(scrollTimeout);
  }, [messagesMap]);

  // File drag and drop handlers
  useEffect(() => {
    const handlePaste = async (e) => {
      const items = e.clipboardData.items;
      for (let i = 0; i < items.length; i++) {
        if (items[i].type.indexOf('image') !== -1) {
          e.preventDefault();
          const file = items[i].getAsFile();
          await handleFileUpload([file]);
        }
      }
    };

    const handleDragOver = (e) => {
      e.preventDefault();
      e.stopPropagation();
      dropZoneRef.current.classList.add('bg-primary-100');
    };

    const handleDragLeave = (e) => {
      e.preventDefault();
      e.stopPropagation();
      dropZoneRef.current.classList.remove('bg-primary-100');
    };

    const handleDrop = async (e) => {
      e.preventDefault();
      e.stopPropagation();
      dropZoneRef.current.classList.remove('bg-primary-100');
      const files = Array.from(e.dataTransfer.files);
      await handleFileUpload(files);
    };

    document.addEventListener('paste', handlePaste);
    dropZoneRef.current.addEventListener('dragover', handleDragOver);
    dropZoneRef.current.addEventListener('dragleave', handleDragLeave);
    dropZoneRef.current.addEventListener('drop', handleDrop);

    return () => {
      document.removeEventListener('paste', handlePaste);
      dropZoneRef.current?.removeEventListener('dragover', handleDragOver);
      dropZoneRef.current?.removeEventListener('dragleave', handleDragLeave);
      dropZoneRef.current?.removeEventListener('drop', handleDrop);
    };
  }, [handleFileUpload]);

  // Handle tool stream data
  const handleToolStreamData = (data) => {
    // Handle different possible data structures
    let toolData;

    if (data.data) {
      toolData = data.data;
    } else if (data.payload) {
      toolData = data.payload;
    } else {
      toolData = data; // Try using the data object directly
    }

    // Ensure we have valid data
    if (!toolData) {

      return;
    }

    // Extract task ID from the data


    // Extract file path from different possible locations
    const getFilePath = () => {
      if (toolData.file_path) return toolData.file_path;
      if (toolData.content && toolData.content.file_path) return toolData.content.file_path;
      return null;
    };

    // Extract filename from path, handling both forward and backslashes
    const getFileName = (path) => {
      if (!path) return null;
      // Handle both Unix-style and Windows-style paths
      const parts = path.split(/[\/\\]/);
      return parts[parts.length - 1];
    };

    // Check if this is a file creation operation
    if (toolData.operation === 'create_file') {
      const filePath = getFilePath();
      if (!filePath) {

        return;
      }

      const fileName = getFileName(filePath);
      const fileContent = toolData.content;

      if (!fileName) {

        return;
      }
      // Update our asset files state, avoiding duplicates
      setAssetFiles(prevFiles => {


        // Check if file content has changed
        if (prevFiles[fileName] === fileContent) {

            return prevFiles;
        }

        // Create new state object
        const newState = {
            ...prevFiles,
            [fileName]: fileContent
        };
        setFiles(newState); // Update the files state in WebSocketContext

        return newState;
      });

      // Add console log to track updates

      //

      //   // If we already have this task ID, check if the file is already in the list
      //   const existingFiles = prevFiles[taskId];
      //   if (!existingFiles.includes(fileName)) {
      //     // Add the new file to the list
      //     return {
      //       ...prevFiles,
      //       [taskId]: [...existingFiles, fileName]
      //     };
      //   }

      //   // File already exists, no need to update
      //   return prevFiles;
      // });

      // // Update the file content in the WebSocketContext
      // if (toolData.content) {
      //   // For file creation, we're starting the stream
      //   updateFileContent(fileName, toolData.content, false);
      //
      // }

      //
    }
    // Handle file update operations
    else if (toolData.operation === 'update_file') {
      const filePath = getFilePath();
      if (!filePath) {

        return;
      }

      const fileName = getFileName(filePath);
      if (!fileName) {

        return;
      }

      // Update the file content in the WebSocketContext
      if (toolData.content) {
        // For file updates, we're continuing the stream
        updateFileContent(fileName, toolData.content, false);

      }

      //
    }
    // Handle completion of a tool operation
    else if (toolData.is_complete === true) {
      //

      // If there's a final file content in the completion message, update it
      if (toolData.content && toolData.file_path) {
        const fileName = getFileName(toolData.file_path);
        if (fileName) {
          // Mark this as the final update (isComplete = true)
          updateFileContent(fileName, toolData.content, true);

        }
      }

      // Signal that the tool stream is complete to trigger file refresh in Sandbox
      setIsCompleted(false); // First set to false
      setTimeout(() => {
        setIsCompleted(true); // Then set to true to trigger the effect
      }, 100);
    }
  };

  // // Get all unique file names across all tasks
  // const getAllUniqueFileNames = useCallback(() => {
  //   const allFileNames = new Set();

  //   // Iterate through all tasks and their files
  //   Object.values(assetFiles).forEach(files => {
  //     files.forEach(fileName => {
  //       allFileNames.add(fileName);
  //     });
  //   });

  //   return Array.from(allFileNames);
  // }, [assetFiles]);

  // Log asset files when they change
  // useEffect(() => {
  //   if (Object.keys(assetFiles).length > 0) {
  //
  //     );
  //   }
  // }, [assetFiles, getAllUniqueFileNames]);

  // Update cleanup effect to reset error state
  useEffect(() => {
    return () => {
      if (controllerRef.current) {
        controllerRef.current.abort();
      }
      streamActiveRef.current = false;
      hasErrorRef.current = false; // Reset error state on unmount
      // setWsConnected(false);
      cleanupWebSocketConnections();
    };
  }, []);


// Refined handleStreamingMessage to only enable chat at proper completion points
// Optimized handleStreamingMessage to properly handle WebSocket events
const handleStreamingMessage = (event) => {
  try {
    const text = event.data;
    if (typeof text === 'object') {
      // Handle direct object messages (not string data events)
      if (text.stop === true) {

        setLoading(false);
        streamActiveRef.current = false;
        setShouldScroll(true);
        return;
      }
    }

    // Handle standard SSE format messages
    if (text.startsWith('data: ')) {
      const data = JSON.parse(text.substring(6)); // Remove "data: " prefix

      // Handle explicit stream termination (stop signal)
      if (data.stop === true) {

        setLoading(false);
        streamActiveRef.current = false;
        setShouldScroll(true);
        return;
      }

        // Regular text message from assistant
        if (data.role === "assistant" && data.content !== undefined) {
          setMessagesMap(prevMap => {
            // Create a new map to ensure React detects the change
            const newMap = { ...prevMap };

            // If we already have this message, update its content
            if (newMap[data.message_id]) {
              newMap[data.message_id] = {
                ...newMap[data.message_id],
                content: data.content,
                finished: !!data.message_end,
                timestamp: data.created_at
              };
            } else {
              // Create a new message if it doesn't exist
              newMap[data.message_id] = {
                id: data.message_id,
                role: data.role,
                content: data.content,
                timestamp: data.created_at,
                finished: !!data.message_end
              };
              // Trigger scroll when a new message is added
              setShouldScroll(true);
            }

            return newMap;
          });

        // If the message is finished, reset streaming state
        if (data.message_end) {

          setLoading(false);
          streamActiveRef.current = false;
          setShouldScroll(true);
        }
      }

        // Tool call message (streaming the function arguments)
        else if (data.tool_call_id && data.func_name) {
          setMessagesMap(prevMap => {
            const newMap = { ...prevMap };

            // Either create a new message or update existing tool call
            if (!newMap[data.message_id]) {
              newMap[data.message_id] = {
                id: data.message_id,
                role: "assistant",
                tool_calls: [{
                  id: data.tool_call_id,
                  function: {
                    name: data.func_name,
                    arguments: data.func_args || ""
                  }
                }]
              };
            } else if (data.func_args) {
              // Update arguments of existing tool call
              const existingMessage = newMap[data.message_id];

              if (existingMessage.tool_calls) {
                const toolCallIndex = existingMessage.tool_calls.findIndex(
                  tc => tc.id === data.tool_call_id
                );

                if (toolCallIndex >= 0) {
                  const updatedToolCalls = [...existingMessage.tool_calls];
                  updatedToolCalls[toolCallIndex] = {
                    ...updatedToolCalls[toolCallIndex],
                    function: {
                      ...updatedToolCalls[toolCallIndex].function,
                      arguments: data.func_args
                    }
                  };

                  newMap[data.message_id] = {
                    ...existingMessage,
                    tool_calls: updatedToolCalls
                  };
                }
              }
            }

            return newMap;
          });
        }

        // Tool response
        else if (data.role === "tool" && data.tool_call_id) {
          setMessagesMap(prevMap => {
            const newMap = { ...prevMap };

            newMap[data.message_id] = {
              id: data.message_id,
              role: "tool",
              name: data.name,
              content: data.content,
              tool_call_id: data.tool_call_id
            };

            return newMap;
          });
        }

        // Final message with complete tool_calls array
        else if (data.role === "assistant" && data.tool_calls) {
          setMessagesMap(prevMap => {
            const newMap = { ...prevMap };

            newMap[data.message_id] = {
              id: data.message_id,
              role: "assistant",
              tool_calls: data.tool_calls
            };

          return newMap;
        });

        // Tool calls completion should also enable the chat

        setLoading(false);
        streamActiveRef.current = false;
      }
    }
  } catch (error) {

    // Enable the chat panel on error processing a message
    setLoading(false);
    streamActiveRef.current = false;
  }
};

  // Create a useEffect that handles scrolling when shouldScroll is true
  useEffect(() => {
    if (shouldScroll) {
      // Small delay to ensure DOM has updated
      const scrollTimeout = setTimeout(() => {
        endOfMessagesRef.current?.scrollIntoView({ behavior: "smooth" });
        setShouldScroll(false); // Reset after scrolling
      }, 100);
      return () => clearTimeout(scrollTimeout);
    }
  }, [shouldScroll]);

  // Also add another useEffect to scroll when messagesHistory is loaded
  useEffect(() => {
    if (messagesHistory.length > 0) {
      const scrollTimeout = setTimeout(() => {
        endOfMessagesRef.current?.scrollIntoView({ behavior: "smooth" });
      }, 300); // Slightly longer delay for initial load
      return () => clearTimeout(scrollTimeout);
    }
  }, [messagesHistory]);

  // In your render method, convert the map to an array for rendering
  // This replaces your existing messages array
  const messagesArray = Object.values(messagesMap);

  return (
    <div className="chat-panel flex flex-col h-full overflow-hidden bg-white " ref={dropZoneRef}>
      <div className="flex items-center justify-between px-4 py-1 border-b border-gray-200">
        <div className="flex items-center space-x-2 h-8">
          <h2 className="project-panel-heading">Figma Chat</h2>
          <div className="flex items-center">
            <div
              className={`h-2.5 w-2.5 rounded-full mr-1.5 ${(wsStatus == "connected") ? 'bg-green-500' : 'bg-gray-300'}`}
              title={(wsStatus == "connected") ? "Connected" : "Disconnected"}
            ></div>
            {figmaDiscussionId && (
              <p className="typography-body-sm text-gray-500">ID: {figmaDiscussionId}</p>
            )}
          </div>
        </div>
      </div>

      <div className="flex-1 flex flex-col min-h-0">
        <MessagesSection
          messages={messagesArray}
          endOfMessagesRef={endOfMessagesRef}
          name={name}
          onFileView={handleFileView}
        />
      </div>

      <div className="border-t border-gray-200 p-2 bg-gray-100">
        <form onSubmit={handleFormSubmit} className="flex items-end">
          <textarea
            id="chat-input"
            ref={textareaRef}
            className={`flex-grow px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary resize-none overflow-auto ${(loading || isUploading) ? 'bg-gray-100 cursor-not-allowed' : ''
              }`}
            placeholder={loading || isUploading ? 'Please wait...' : 'Enter your prompt'}
            onKeyDown={handleKeyPress}
            onInput={handleTextareaResize}
            disabled={loading || isUploading}
            rows={1}
          />
          {attachmentEnabled && (
            <div className="relative">
              <AttachmentButton
                ref={attachmentButtonRef}
                onClick={handleAttachmentClick}
                fileCount={attachedFiles.length}
              />
              {showAttachmentWindow && attachedFiles.length > 0 && (
                <AttachmentWindow
                  files={attachedFiles}
                  onAdd={handleAddAttachment}
                  onClear={handleClearAttachments}
                  onClose={() => setShowAttachmentWindow(false)}
                  maxFiles={maxFiles}
                  fileStatuses={fileStatuses}
                  onFileClick={handleFileView}
                />
              )}
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                multiple={maxFiles > 1}
                className="hidden"
              />
            </div>
          )}
          <button
            type="submit"
            disabled={loading || isUploading}
            className={`ml-2 p-2 ${(loading || isUploading) ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <Image src={chaticon} alt="Chat icon" />
          </button>
        </form>
      </div>

      {selectedFile && (
        <FileContentModal
          file={selectedFile}
          onClose={() => setSelectedFile(null)}
        />
      )}
    </div>
  );
}