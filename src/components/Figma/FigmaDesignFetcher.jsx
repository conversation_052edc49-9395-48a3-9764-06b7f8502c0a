// FigmaDesignFetcher.jsx
"use client";
import { useState, useEffect, useContext, } from "react";
import Loader from "./Loader";
import TableComponent from "@/components/SimpleTable/table";
import ImageLightbox from "@/components/Figma/ImageLightbox";
import CardGrid from "./FrameCard";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { getFigmaExtImage, getFigmaFileData } from "@/api/figma";
import en from '@/en.json';
import EnParser from "@/utils/enParser";
import ErrorView from "../Modal/ErrorViewModal";
import {
  Group
} from 'lucide-react';
import { useParams } from "next/navigation";
import { FigmaExtractionContext } from "../Context/FigmaExtractionContext";

export default function FigmaDesignFetcher({
  figmaId,
  figmaLink,
  viewMode,
  setViewMode,
  isUpdating,
  isDownloading,
  handleUpdateFigmaDesign,
  handleDownloadAllFrames,
  selectedDesign,
  designType,
  onRename,  // Add this prop
  onDelete,  // Add this prop
}) {
  const { selectedDesignData, setSelectedDesignData } = useContext(FigmaExtractionContext);
  const [figmaData, setFigmaData] = useState(null);
  const [frames, setFrames] = useState([]);
  const [error, setError] = useState(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [isFetchingMainData, setIsFetchingMainData] = useState(false);
  const [fetchingScreenId, setFetchingScreenId] = useState(null);
  const { showAlert } = useContext(AlertContext)
  const params = useParams();
  const projectId = params.projectId;

  const fetchFigmaData = async () => {
    setIsFetchingMainData(true);
    try {
      let data = null;
      
      if (designType === 'figma') {
        data = await getFigmaFileData(figmaId);
        // Ensure frames property exists for Figma data
        if (!data || !data.frames) {
          throw new Error('Invalid Figma data structure received');
        }
      } else if (designType === 'image') {
        const response = await getFigmaExtImage(projectId, figmaId);
        // Check if response and image property exist
        if (!response || !response.image || !response.image.images) {
          throw new Error('Invalid image data structure received');
        }
        // Transform image data to match expected frames structure
        data = {
          frames: response.image.images.map(img => ({
            id: img.file_id,
            name: img.filename,
            imageUrl: img.base64url,
            absoluteBoundingBox: {
              width: 800,
              height: 600
            },
            dimensions: {
              height: 800,
              width: 600
            }


          })) || []
        };
      }
      

      setFigmaData(data || { frames: [] });
      setFrames(Array.isArray(data?.frames) ? data.frames : []);
      setSelectedDesignData(data || { frames: [] });
    } catch (err) {
      
      setError(err.message);
      showAlert(err.message, 'error');
      // Set empty arrays to prevent undefined errors
      setFigmaData({ frames: [] });
      setFrames([]);
    } finally {
      setIsFetchingMainData(false);
    }
  };

  useEffect(() => {
    if (figmaId) {
      fetchFigmaData();
    }
  }, [figmaId, selectedDesign]);

  const onRowClick = async (rowOrId) => {
    

    // Handle both cases: when receiving full row object or just ID
    const row = typeof rowOrId === 'string'
      ? frames.find(frame => frame.id === rowOrId)
      : rowOrId;

    if (!row || !row.id) {
      
      return;
    }

    setFetchingScreenId(row.id);
    try {
      if (row.imageUrl) {
        setSelectedImage(row.imageUrl);
        return;
      }
    } catch (error) {
      
    } finally {
      setFetchingScreenId(null);
    }
  };

  const handleRename = (row) => {
    if (row && row.id) {
      onRename(row);
    }
  };

  const handleDelete = async (row) => {
    
    // e.stopPropagation(); // Prevent row click event
    if (row && row.originalData) {
      // Call the onDelete prop with the frame data
      onDelete({
        id: row.originalData.id,
        name: row.originalData.name
      });
    } else if (row && row.id) {
      // For card view which has a different data structure
      onDelete({
        id: row.id,
        name: row.title
      });
    }
  }

  const handleDownload = async (row) => {
    try {
      
      // Create blob from image URL
      const response = await fetch(row.imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);

      // Create download link
      const link = document.createElement('a');
      link.href = url;
      link.download = `${row.name|| row.title || 'image'}.png`;
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      
    }
  }

  const formatForTable = (frames) => {
    if (!Array.isArray(frames)) {
      
      return [];
    }
    return frames.map((frame) => ({
      id: frame.id || `frame-${Math.random()}`,
      name: frame.name || 'Untitled',
      dimensions: frame.absoluteBoundingBox
        ? `${Math.round(frame.absoluteBoundingBox.width)} x ${Math.round(frame.absoluteBoundingBox.height)}`
        : 'N/A',
      type: designType === 'image' ? 'Image' : 'Frame',
      imageUrl: frame.imageUrl || '',
      // Add original frame data for actions
      originalData: frame
    }));
  };

  const formatForCards = (frames) => {
    if (!Array.isArray(frames)) {
      return [];
    }
    return frames.map((frame) => ({
      id: frame.id || `frame-${Math.random()}`,
      image: frame.imageUrl ||
        (frame.absoluteBoundingBox
          ? `https://via.placeholder.com/${Math.round(frame.absoluteBoundingBox.width)}x${Math.round(frame.absoluteBoundingBox.height)}?text=${encodeURIComponent(frame.name || 'Untitled')}`
          : `https://via.placeholder.com/800x600?text=${encodeURIComponent(frame.name || 'Untitled')}`),
      title: frame.name || 'Untitled',
      dimensions: frame.absoluteBoundingBox ? {
        width: Math.round(frame.absoluteBoundingBox.width),
        height: Math.round(frame.absoluteBoundingBox.height),
      } : { width: 800, height: 600 },
      imageUrl: frame.imageUrl || ''
    }));
  };

  const tableHeaders = designType === 'image' ? [
    { key: "name", label: "Screen" },
    { key: "dimensions", label: "Dimensions" },
    { key: "type", label: "Type" },
    { key: "action", label: "Actions" }
  ] : [
    { key: "name", label: "Screen" },
    { key: "dimensions", label: "Dimensions" },
    { key: "type", label: "Type" }
  ];


  return (
    <div className="h-full max-h-[80vh] overflow-auto">
      {isFetchingMainData ? (
        <Loader loadingText={"Fetching Frames"} />
      ) : error ? (
        <ErrorView
          title="Unable to Load Figma"
          message={error}
          onRetry={() => fetchFigmaData()}
          panelType='main'
        />
      ) : !frames || frames.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-full text-center">
          <div className="bg-gray-100 p-4 rounded-full mb-4">
            <Group size={32} className="text-gray-400" />
          </div>
          <h3 className="typography-body-lg font-weight-medium text-gray-900 mb-2">No Frames Found</h3>
          <p className="text-gray-500 max-w-md">
            <EnParser content={en.NoFrames} />
          </p>
          <div className="mt-4 typography-body-sm text-gray-400">
            Design ID: {figmaId || 'Not provided'}
          </div>
        </div>
      ) : (
        <div className="h-full overflow-auto p-4">
          <div className={`${viewMode === 'card' ? 'bg-white rounded-xl shadow-sm border border-gray-100 p-6' : ''}`}>
            {viewMode === "table" ? (
              <div className="max-h-[80%] overflow-hidden mb-12">
                <TableComponent
                  data={formatForTable(frames)}
                  onRowClick={onRowClick}
                  headers={tableHeaders}
                  sortableColumns={{ id: true, name: true, dimensions: true }}
                  itemsPerPage={20}
                  isLoading={fetchingScreenId !== null}
                  loadingRowId={fetchingScreenId}
                  type={designType}
                  onEdit={(row) => handleRename(row.originalData)}
                  onDelete={(row) => handleDelete(row.originalData)}
                  onDownload={(row) => handleDownload(row)}
                />
              </div>
            ) : (
              <CardGrid
                cards={formatForCards(frames)}
                onCardClick={onRowClick}
                loadingCardId={fetchingScreenId}
                type={designType}
                onRename={(row) => handleRename(row)}
                onDelete={(row)=>handleDelete(row)}  
                onDownload={(row) => handleDownload(row)}
              />
            )}
          </div>
        </div>
      )}

      {selectedImage && (
        <ImageLightbox
          imageUrl={selectedImage}
          onClose={() => setSelectedImage(null)}
        />
      )}
    </div>
  );
}