import React, { useState, useEffect } from 'react';
import { GitBranch, GitCommit, Plus } from 'lucide-react';
import { getScmConfiguration } from '@/utils/api';
import RepositoryAddition from './RepositoryAddition';

function Repository() {
  const [repositories, setRepositories] = useState([]);
  const [activeTab, setActiveTab] = useState('personal');
  const [showSCMSelection, setShowSCMSelection] = useState(false);
  const [step, setStep] = useState('initial');
  const [selectedSCMType, setSelectedSCMType] = useState(null);
  const [scmConfigurations, setSCMConfigurations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchSCMConfigurations();
  }, []);

  const fetchSCMConfigurations = async () => {
    try {
      setLoading(true);
      const response = await getScmConfiguration();
      if (response.status === "success") {
        setSCMConfigurations(response.data?.configurations || []);
      }
    } catch (err) {
      
      setError('Failed to fetch SCM configurations');
    } finally {
      setLoading(false);
    }
  };

  const handleSCMTypeSelect = (scmType) => {
    setSelectedSCMType(scmType);
    setStep('select-scm');
  };

  const RepositoryList = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="typography-heading-4 font-weight-semibold text-gray-900">Project Repositories</h2>
        <button
          onClick={() => setShowSCMSelection(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-colors"
        >
          <Plus className="w-4 h-4" />
          <span>Add Repository</span>
        </button>
      </div>
      <div className="grid gap-4">
        {repositories.map((repo) => (
          <div key={repo.id} className="p-4 border border-gray-200 rounded-lg hover:border-gray-300">
            <div className="flex items-start justify-between">
              <div>
                <h3 className="font-weight-medium text-gray-900">{repo.name}</h3>
                <p className="typography-body-sm text-gray-500">{repo.description}</p>
              </div>
              <div className="flex items-center space-x-4 typography-body-sm text-gray-500">
                <div className="flex items-center space-x-1">
                  <GitBranch className="w-4 h-4" />
                  <span>{repo.branch}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <GitCommit className="w-4 h-4" />
                  <span>{repo.lastCommit}</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col overflow-hidden">
      <div className="flex-1 min-h-0 overflow-auto">
        <div className="w-full">
          {repositories.length > 0 && !showSCMSelection ? (
            <RepositoryList />
          ) : (
            <RepositoryAddition 
              scmConfigurations={scmConfigurations}
              onSCMTypeSelect={handleSCMTypeSelect}
            />
          )}
        </div>
      </div>
    </div>
  );
}

export default Repository;
