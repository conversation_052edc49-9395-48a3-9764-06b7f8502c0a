declare module '@/components/Repository/RepositoryConfigurationPage' {
  interface Repository {
    container_id: number;
    scm_id: string;
    service: string;
    repositoryName: string;
    repositoryId: string;
    cloneUrlHttp: string;
    cloneUrlSsh: string;
    organization: string;
    encrypted_scm_id: string;
    repositoryStatus: string;
  }

  interface RepositoryConfigurationPageProps {
    projectId: string | number;
    containerId: number;
    onSuccess?: (repo: Repository) => void;
    handleRepoChange?: (repo: Repository) => void;
    initialSCMType?: string | null;
  }

  const RepositoryConfigurationPage: React.FC<RepositoryConfigurationPageProps>;
  
  export default RepositoryConfigurationPage;
}
