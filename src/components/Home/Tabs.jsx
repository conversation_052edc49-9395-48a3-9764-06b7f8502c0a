import React from 'react';
import { FaProjectDiagram, FaTasks, FaComments } from 'react-icons/fa';
import { TAB_ITEMS } from "@/constants/home/<USER>";

const Tab = ({ label, icon: Icon, isActive, onClick }) => (
  <div
    role="tab"
    aria-selected={isActive}
    aria-label={label}
    className={`home-tab ${
      isActive ? 'active-tab' : 'inactive-tab'
    }`}
    onClick={onClick}
  >
    {Icon && <Icon className="home-tab-icon" />}
    {label}
  </div>
);

const TABS = [
  { key: "recentprojects", label: "Recent Projects", icon: FaProjectDiagram },
  { key: "assignedtasks", label: "Assigned Tasks", icon: FaTasks },
  { key: "mydiscussion", label: "My Discussions", icon: FaComments },
];

const Tabs = ({ activeTab, setActiveTab }) => (
  <div className="home-tab-container">
    {TAB_ITEMS.map(({ key, label, icon }) => (
      <Tab
        key={key}
        label={label}
        icon={icon}
        isActive={activeTab === key}
        onClick={() => setActiveTab(key)}
      />
    ))}
  </div>
);

export default Tabs;
