import React, { useState, useEffect, useMemo } from 'react';
import ProjectsList from './RecentProject/ProjectsList';
import RecentActivityList from './RecentProject/RecentActivityList';
import TaskTable from './AssignedTasks/TaskTable';
import DiscussionsList from './DiscussionList/DiscussionsList';
import { getRecentProjectActivity } from "@/utils/api";
import { CircularLoader } from "@/components/Loaders/Loading";
import ErrorView from "@/components/Modal/ErrorViewModal";
import EmptyStateView from "@/components/Modal/EmptyStateModal";

const Content = ({ activeTab }) => {
  const [activities, setActivities] = useState([]);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);

  const fetchActivities = async () => {
    try {
      const response = await getRecentProjectActivity();
      setActivities(response);
    } catch (err) {
      setError(err.message || 'An error occurred while fetching data.');
    } finally {
      setLoading(false); // Set loading to false regardless of success or error
    }
  };

  useEffect(() => {
    if (activeTab === 'Recent Projects') {
      fetchActivities();
    }
  }, [activeTab]);

  // Memoize activities to avoid unnecessary re-renders
  const memoizedActivities = useMemo(() => activities, [activities]);

  if (activeTab === 'Recent Projects') {
    if (loading) {
      return (
        <div className="content-loading-overlay">
          <CircularLoader />
        </div>
      );
    }

    if (error) {
      return (
        <ErrorView
          title="Unable to Load Projects"
          message={error}
          onRetry={() => fetchActivities()}
          panelType='main'
        />
      );
    }

    if (memoizedActivities.length === 0) {
      <EmptyStateView type="projects" onClick={() => {}} />
    }

  }

  return (
    <div>
      {activeTab === 'Recent Projects' && (
        <>
          <ProjectsList activities={memoizedActivities} />
          <RecentActivityList activities={memoizedActivities} />
        </>
      )}
      {activeTab === 'Assigned Tasks' && <TaskTable />}
      {activeTab === 'My Discussions' && <DiscussionsList />}
    </div>
  );
};

export default Content;
