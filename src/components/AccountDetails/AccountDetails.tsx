import React from "react";
import AccountDetailCard from "./AccountDetailCard";
import TabbedInterface from "./AccountTabs";

interface AccountDetailsProps {
    title?: string;
    cardDetails?: any;
    connectedSettingsArray?: any[];
    onIntegrationClick?(): void;
}

const AccountDetails: React.FC<AccountDetailsProps> = ({
    title,
    cardDetails,
    connectedSettingsArray,
    onIntegrationClick,
}) => {
    return (
        <div className="container mx-auto p-6 relative overflow-y-auto h-screen">
            <div className="flex justify-between items-center mb-6">
                <nav className="flex text-black typography-heading-4 font-weight-bold">
                    <span
                        className="text-gray-500 hover:text-black cursor-pointer"
                        onClick={onIntegrationClick}
                    >
                        Integrations
                    </span>
                    <span className="mx-2">/</span>
                    <span className="text-black">{title}</span>
                </nav>
            </div>

            <div className="space-y-4 relative">
                <AccountDetailCard
                    title={cardDetails?.title}
                    id={cardDetails?.id}
                    lastSynced={cardDetails?.lastSynced}
                    isConnected={cardDetails?.isConnected}
                    connectedSettingsArray={connectedSettingsArray}
                />
            </div>
            <TabbedInterface
                togglebuttonsList={["Manage", "View"]}
                rows={[
                    {
                        name: "stellar-observer",
                        path: "https://github.com/username/stellar-observer.git",
                        lastUpdated: "24 May, 2020",
                        status: "Active",
                    },
                    {
                        name: "quantum-horizon",
                        path: "https://github.com/username/quantum-horizon.git",
                        lastUpdated: "24 May, 2020",
                        status: "Inactive",
                    }, {
                        name: "stellar-observer",
                        path: "https://github.com/username/stellar-observer.git",
                        lastUpdated: "24 May, 2020",
                        status: "Active",
                    },
                    {
                        name: "quantum-horizon",
                        path: "https://github.com/username/quantum-horizon.git",
                        lastUpdated: "24 May, 2020",
                        status: "Inactive",
                    }, {
                        name: "stellar-observer",
                        path: "https://github.com/username/stellar-observer.git",
                        lastUpdated: "24 May, 2020",
                        status: "Active",
                    },
                    {
                        name: "quantum-horizon",
                        path: "https://github.com/username/quantum-horizon.git",
                        lastUpdated: "24 May, 2020",
                        status: "Inactive",
                    }, {
                        name: "stellar-observer",
                        path: "https://github.com/username/stellar-observer.git",
                        lastUpdated: "24 May, 2020",
                        status: "Active",
                    },
                    {
                        name: "quantum-horizon",
                        path: "https://github.com/username/quantum-horizon.git",
                        lastUpdated: "24 May, 2020",
                        status: "Inactive",
                    },
                ]}
                headers={["REPOSITORIES NAME", "REPOSITORIES PATH", "LAST UPDATED", "STATUS"]} />
        </div>
    );
};

export default AccountDetails;
