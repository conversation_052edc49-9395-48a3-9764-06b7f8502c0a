import { Menu } from "@headlessui/react";
import React, { useState } from "react";
import { createPortal } from "react-dom";
import { FaCircle, FaEllipsisV } from "react-icons/fa";

interface TabbedInterfaceProps {
    headers: string[];
    rows: {
        name: string;
        path: string;
        lastUpdated: string;
        status: string;
    }[];
    togglebuttonsList?: any[];
    handleMenuClick?(item?: any, data?: any): any;
}

const TabbedInterface: React.FC<TabbedInterfaceProps> = ({
    headers,
    rows,
    togglebuttonsList,
    handleMenuClick,
}) => {
    const [activeTab, setActiveTab] = useState("Repositories");

    const handleTabClick = (tab: string) => {
        setActiveTab(tab);
    };

    const _handleMenuClick = (item?: any, data?: any) => {
        handleMenuClick && handleMenuClick(item, data);
    };

    const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });

    const handleMenuOpen = (event: React.MouseEvent) => {
        const rect = event.currentTarget.getBoundingClientRect();
        setMenuPosition({ top: rect.top, left: rect.left });
    };

    return (
        <div className="container mx-auto p-6">
            <div className="flex border-b-2 mb-4">
                <button
                    onClick={() => handleTabClick("Repositories")}
                    className={`px-4 py-2 transition font-weight-semibold rounded-t-md ${activeTab === "Repositories"
                        ? "bg-white shadow border border-b-0 text-primary"
                        : "text-gray-600"
                        }`}
                >
                    Repositories
                </button>
                <button
                    onClick={() => handleTabClick("Logs")}
                    className={`px-4 py-2 transition font-weight-semibold rounded-t-md ${activeTab === "Logs"
                        ? "bg-white shadow border border-b-0 text-primary"
                        : "text-gray-600"
                        }`}
                >
                    Logs
                </button>
                <button
                    onClick={() => handleTabClick("Settings")}
                    className={`px-4 py-2 transition font-weight-semibold rounded-t-md ${activeTab === "Settings"
                        ? "bg-white shadow border border-b-0 text-primary"
                        : "text-gray-600"
                        }`}
                >
                    Settings
                </button>
            </div>

            <div className="bg-white p-4 shadow rounded-lg mb-12">
                {activeTab === "Repositories" && (
                    <div>
                        <h2 className="typography-body-lg font-weight-semibold mb-4">Repositories</h2>
                        <table className="table-auto w-full text-left">
                            <thead className="text-gray-700 font-weight-bold">
                                <tr>
                                    {headers.map((header, index) => (
                                        <th key={index} className="border px-4 py-2 bg-gray-100">{header}</th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody className="text-gray-700">
                                {rows.map((row, index) => (
                                    <tr key={index}>
                                        <td className="border px-4 py-2">{row.name}</td>
                                        <td className="border px-4 py-2">
                                            <a
                                                href={row.path}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="text-black hover:text-primary hover:underline"
                                            >
                                                {row.path}
                                            </a>
                                        </td>

                                        <td className="border px-4 py-2">{row.lastUpdated}</td>
                                        <td className="border px-4 py-2">
                                            <div className="flex justify-between items-center">
                                                <span
                                                    className={`inline-flex items-center space-x-2 py-1 px-2 rounded
                                                        ${row.status === 'Active' ? 'bg-green-100 text-green-600'
                                                            : row.status === 'Inactive' ? 'bg-red-100 text-red-600'
                                                                : 'bg-gray-200 text-gray-600'}`}
                                                >
                                                    <FaCircle
                                                        size={8}
                                                        className={`${row.status === 'Active'
                                                            ? 'text-green-600'
                                                            : row.status === 'Inactive'
                                                                ? 'text-red-600'
                                                                : 'text-black'}`}
                                                    />
                                                    <span>{row.status}</span>
                                                </span>

                                                <Menu as="div" className="relative">
                                                    <Menu.Button
                                                        className="p-2 rounded-lg border border-gray-300 shadow-sm hover:shadow-md bg-white transition duration-200"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            handleMenuOpen(e);
                                                        }}
                                                    >
                                                        <FaEllipsisV size={12} />
                                                    </Menu.Button>
                                                    {createPortal(
                                                        <Menu.Items
                                                            className="absolute bg-white border rounded-lg shadow-lg z-50"
                                                            style={{ top: menuPosition.top + 40, left: menuPosition.left }}
                                                        >
                                                            {togglebuttonsList && togglebuttonsList.map((item, index) => (
                                                                <Menu.Item key={index}>
                                                                    {({ active }) => (
                                                                        <button
                                                                            className={`block max-w-[100px] w-full text-left px-4 py-2 break-words ${active ? "bg-gray-100" : ""}`}
                                                                            onClick={() => _handleMenuClick(item, row)}
                                                                        >
                                                                            {item}
                                                                        </button>
                                                                    )}
                                                                </Menu.Item>
                                                            ))}
                                                        </Menu.Items>,
                                                        document.body
                                                    )}
                                                </Menu>
                                            </div>
                                        </td>

                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}

                {activeTab === "Logs" && (
                    <div>
                        <h2 className="typography-body-lg font-weight-semibold mb-4">Logs</h2>
                        <p>This is where the logs will be displayed.</p>
                    </div>
                )}

                {activeTab === "Settings" && (
                    <div>
                        <h2 className="typography-body-lg font-weight-semibold mb-4">Settings</h2>
                        <p>This is where the settings will be displayed.</p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default TabbedInterface;
