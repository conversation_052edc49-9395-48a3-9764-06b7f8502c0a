// components/dashboard/TenantsList.jsx
import React, { useState, useMemo, useEffect } from "react";
import { formatCurrency, formatDate } from "../../lib/utils";
import Button from "../ui/Button";

const TenantsList = ({
  tenants,
  onTenantSelect,
  loading = false,
  error = null,
  onRetry = null,
}) => {

  useEffect(() => {
    console.error('TenantsList received tenants:', tenants);
    console.error('TenantsList loading state:', loading);
  }, [tenants, loading]);
  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5); // Changed default to 5 to show pagination
  const [sortField, setSortField] = useState("last_activity");
  const [sortDirection, setSortDirection] = useState("desc");
  const [searchTerm, setSearchTerm] = useState("");

  // Safe tenants array handling
  const tenantsList = Array.isArray(tenants) ? tenants : [];
  // In your TenantsList.jsx, update the filteredTenants useMemo:

  // Filter tenants based on search term and exclude T0000
  const filteredTenants = useMemo(() => {
    // First filter out T0000
    const tenantsWithoutT0000 = tenantsList.filter(
      (tenant) => tenant.id !== "T0000"
    );

    // Then apply search filter if search term exists
    if (!searchTerm.trim()) return tenantsWithoutT0000;

    const searchLower = searchTerm.toLowerCase().trim();
    return tenantsWithoutT0000.filter(
      (tenant) =>
        tenant.id?.toLowerCase().includes(searchLower) ||
        tenant.name?.toLowerCase().includes(searchLower)
    );
  }, [tenantsList, searchTerm]);

  // Sort tenants
  const sortedTenants = useMemo(() => {
    return [...filteredTenants].sort((a, b) => {
      let aVal = a[sortField];
      let bVal = b[sortField];

      // Handle different data types
      if (sortField === "last_activity") {
        aVal = aVal ? new Date(aVal).getTime() : 0;
        bVal = bVal ? new Date(bVal).getTime() : 0;
      } else if (typeof aVal === "string") {
        aVal = aVal.toLowerCase();
        bVal = bVal?.toLowerCase() || "";
      } else if (typeof aVal === "number") {
        aVal = aVal || 0;
        bVal = bVal || 0;
      }

      if (sortDirection === "asc") {
        return aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
      } else {
        return aVal < bVal ? 1 : aVal > bVal ? -1 : 0;
      }
    });
  }, [filteredTenants, sortField, sortDirection]);

  // Pagination
  const totalPages = Math.ceil(sortedTenants.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedTenants = sortedTenants.slice(
    startIndex,
    startIndex + itemsPerPage
  );

  // Handle sorting
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("desc");
    }
    setCurrentPage(1);
  };

  // Handle search
  const handleSearch = (value) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1);
  };
  const formatDate = (input) => {
  try {
    const timestamp = input?.endsWith('Z') ? input : input + 'Z';
    const date = new Date(timestamp);

    if (isNaN(date.getTime())) return 'Invalid date';

    const formatted = date.toLocaleString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true,
    });

    return formatted.replace(/\b(am|pm)\b/i, (match) => match.toUpperCase());
  } catch {
    return 'Invalid date';
  }
};


  // Sort icon component
  const SortIcon = ({ field }) => {
    if (sortField !== field) {
      return <span className="text-gray-400 ml-1">↕️</span>;
    }
    return (
      <span className="text-primary ml-1">
        {sortDirection === "asc" ? "↑" : "↓"}
      </span>
    );
  };

  // Replace the Pagination component in TenantsList.jsx with this:

  const Pagination = () => {
    const getVisiblePages = () => {
      const pages = [];
      const showPages = 5;
      let start = Math.max(1, currentPage - Math.floor(showPages / 2));
      let end = Math.min(totalPages, start + showPages - 1);

      if (end - start < showPages - 1) {
        start = Math.max(1, end - showPages + 1);
      }

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      return pages;
    };

    return (
      <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-3 sm:space-y-0">
          {/* Left side - Results info and items per page */}
          <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
            <p className="typography-body-sm text-gray-700 whitespace-nowrap">
              Showing{" "}
              <span className="font-weight-medium">
                {Math.min(startIndex + 1, sortedTenants.length)}
              </span>{" "}
              to{" "}
              <span className="font-weight-medium">
                {Math.min(startIndex + itemsPerPage, sortedTenants.length)}
              </span>{" "}
              of <span className="font-weight-medium">{sortedTenants.length}</span>{" "}
              results
              {searchTerm && (
                <span className="text-gray-500"> for {searchTerm}</span>
              )}
            </p>

            {/* Items per page selector */}
            <div className="flex items-center space-x-2 whitespace-nowrap">
              <label className="typography-body-sm text-gray-700">Show:</label>
              <select
                value={itemsPerPage}
                onChange={(e) => handleItemsPerPageChange(e.target.value)}
                className="border border-gray-300 rounded-md px-6 py-1 typography-body-sm focus:ring-2 focus:ring-primary focus:border-primary bg-white min-w-[100px]"
              >
                <option value={2}>2 per page</option>
                <option value={5}>5 per page</option>
                <option value={10}>10 per page</option>
                <option value={25}>25 per page</option>
                <option value={50}>50 per page</option>
              </select>
            </div>
          </div>

          {/* Right side - Navigation controls */}
          <div className="flex items-center space-x-2">
            {totalPages > 1 ? (
              <nav className="flex items-center space-x-1">
                <button
                  onClick={() => handlePageChange(1)}
                  disabled={currentPage === 1}
                  className="px-2 py-1 typography-body-sm font-weight-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  First
                </button>
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="px-2 py-1 typography-body-sm font-weight-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  ←
                </button>

                {getVisiblePages().map((page) => (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`px-3 py-1 typography-body-sm font-weight-medium border rounded-md ${
                      page === currentPage
                        ? "bg-primary-600 text-white border-primary-600 hover:bg-primary-700"
                        : "text-gray-700 bg-white border-gray-300 hover:bg-gray-50"
                    }`}
                  >
                    {page}
                  </button>
                ))}

                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="px-2 py-1 typography-body-sm font-weight-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  →
                </button>
                <button
                  onClick={() => handlePageChange(totalPages)}
                  disabled={currentPage === totalPages}
                  className="px-2 py-1 typography-body-sm font-weight-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Last
                </button>
              </nav>
            ) : (
              <div className="typography-body-sm text-gray-500 whitespace-nowrap">
                Page 1 of 1
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };
  // Loading skeleton
  const LoadingSkeleton = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="h-6 bg-gray-200 rounded w-48 animate-pulse" />
        <div className="h-8 bg-gray-200 rounded w-64 animate-pulse" />
      </div>
      <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg">
        <table className="min-w-full divide-y divide-gray-300">
          <thead className="bg-gray-50">
            <tr>
              {Array.from({ length: 8 }, (_, i) => (
                <th key={i} className="px-6 py-3">
                  <div className="h-4 bg-gray-200 rounded animate-pulse" />
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {Array.from({ length: 5 }, (_, i) => (
              <tr key={i}>
                {Array.from({ length: 8 }, (_, j) => (
                  <td key={j} className="px-6 py-4">
                    <div className="h-4 bg-gray-200 rounded animate-pulse" />
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {/* Skeleton pagination */}
      <div className="bg-white px-4 py-3 border-t border-gray-200">
        <div className="flex justify-between items-center">
          <div className="h-4 bg-gray-200 rounded w-48 animate-pulse" />
          <div className="flex space-x-2">
            {Array.from({ length: 5 }, (_, i) => (
              <div
                key={i}
                className="h-8 bg-gray-200 rounded w-8 animate-pulse"
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  // Error state
  const ErrorState = () => (
    <div className="text-center py-12">
      <div className="typography-heading-1 mb-6">⚠️</div>
      <h3 className="typography-heading-4 font-weight-medium mb-2 text-gray-900">
        Unable to Load Tenants
      </h3>
      <p className="typography-body-sm max-w-md mx-auto mb-4 text-gray-500">
        {error?.message ||
          "There was an error loading tenants. Please try again."}
      </p>
      {onRetry && (
        <Button onClick={onRetry} variant="outline">
          Try Again
        </Button>
      )}
    </div>
  );

  // Empty state
  const EmptyState = () => (
    <div className="text-center py-12">
      <div className="typography-heading-1 mb-6">🏢</div>
      <h3 className="typography-heading-4 font-weight-medium mb-2 text-gray-900">
        {searchTerm ? "No Tenants Found" : "No Tenants Available"}
      </h3>
      <p className="typography-body-sm max-w-md mx-auto text-gray-500">
        {searchTerm
          ? `No tenants match "${searchTerm}". Try adjusting your search.`
          : "No tenants have been found in the system."}
      </p>
      {searchTerm && (
        <Button
          onClick={() => handleSearch("")}
          variant="outline"
          className="mt-4"
        >
          Clear Search
        </Button>
      )}
    </div>
  );

  // Handle loading state
  if (loading && tenantsList.length === 0) {
    return <LoadingSkeleton />;
  }

  // Handle error state
  if (error && tenantsList.length === 0) {
    return <ErrorState />;
  }

  return (
    <div className="space-y-6">
      {/* Header with Search */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h2 className="typography-heading-4 font-weight-semibold text-gray-900">
          Tenants Overview
        </h2>
        <div className="w-full sm:w-64">
          <input
            type="text"
            placeholder="Search by Tenant ID or Name..."
            value={searchTerm}
            onChange={(e) => handleSearch(e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 typography-body-sm focus:ring-2 focus:ring-primary focus:border-primary"
          />
        </div>
      </div>

      {/* Table */}
      {sortedTenants.length === 0 ? (
        <EmptyState />
      ) : (
        <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg bg-white">
          <table className="min-w-full divide-y divide-gray-300">
            <thead className="bg-gray-50">
              <tr>
                <th
                  className="px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort("name")}
                >
                  <div className="flex items-center">
                    <span>Tenant</span>
                    <SortIcon field="name" />
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort("id")}
                >
                  <div className="flex items-center">
                    <span>ID</span>
                    <SortIcon field="id" />
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort("total_sessions")}
                >
                  <div className="flex items-center">
                    <span>Total Sessions</span>
                    <SortIcon field="total_sessions" />
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort("active_sessions")}
                >
                  <div className="flex items-center">
                    <span>Active</span>
                    <SortIcon field="active_sessions" />
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort("completed_sessions")}
                >
                  <div className="flex items-center">
                    <span>Completed</span>
                    <SortIcon field="completed_sessions" />
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort("total_cost")}
                >
                  <div className="flex items-center">
                    <span>Total Cost</span>
                    <SortIcon field="total_cost" />
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort("last_activity")}
                >
                  <div className="flex items-center">
                    <span>Last Activity</span>
                    <SortIcon field="last_activity" />
                  </div>
                </th>
                <th className="px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedTenants.map((tenant) => (
                <tr
                  key={tenant.id}
                  className="hover:bg-gray-50 transition-colors cursor-pointer"
                  onClick={() => onTenantSelect(tenant)}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8">
                        <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                          <span className="text-primary font-weight-medium typography-body-sm">
                            {tenant.name?.charAt(0) || "T"}
                          </span>
                        </div>
                      </div>
                      <div className="ml-3">
                        <div className="typography-body-sm font-weight-medium text-gray-900">
                          {tenant.name || "Unknown Tenant"}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="typography-body-sm text-gray-900 ">
                      {tenant.id}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="typography-body-sm text-gray-900 font-weight-semibold">
                      {tenant.total_sessions || 0}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full typography-caption font-weight-medium bg-green-100 text-green-800">
                      {tenant.active_sessions || 0}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full typography-caption font-weight-medium bg-primary-100 text-primary-800">
                      {tenant.completed_sessions || 0}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="typography-body-sm font-weight-semibold text-green-600">
                      {formatCurrency(tenant.total_cost || 0)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="typography-body-sm text-gray-500">
                      {tenant.last_activity
                        ? formatDate(tenant.last_activity)
                        : "Never"}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right typography-body-sm font-weight-medium">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        onTenantSelect(tenant);
                      }}
                    >
                      View Details
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {/* Always show pagination */}
          <Pagination />
        </div>
      )}
    </div>
  );
};

export default TenantsList;
