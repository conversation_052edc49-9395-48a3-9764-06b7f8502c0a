// components/dashboard/DashboardHeader.jsx
import But<PERSON> from '../ui/Button';

const DashboardHeader = ({ onRefresh, loading, lastUpdated }) => {
  return (
    <header className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          <h1 className="typography-heading-2 font-weight-bold text-gray-900">Super Admin Dashboard</h1>
          <div className="flex items-center space-x-4">
            <div className="typography-body-sm text-gray-500">
              Last updated: <span>{lastUpdated || '--'}</span>
            </div>
            <Button onClick={onRefresh} loading={loading}>
              🔄 Refresh
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default DashboardHeader;