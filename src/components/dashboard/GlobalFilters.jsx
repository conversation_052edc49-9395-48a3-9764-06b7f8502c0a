// components/dashboard/GlobalFilters.jsx
import React from 'react';
import Button from '../ui/Button';
import Select from '../ui/Select';

const GlobalFilters = ({ 
  filters, 
  onFiltersChange, 
  onApplyFilters, 
  onClearFilters,
  loading = false 
}) => {
  const handleDateFromChange = (value) => {
    onFiltersChange({ dateFrom: value || null });
  };

  const handleDateToChange = (value) => {
    onFiltersChange({ dateTo: value || null });
  };

  const handleStatusChange = (value) => {
    onFiltersChange({ status: value || null });
  };

  const hasActiveFilters = filters.dateFrom || filters.dateTo || filters.status;

  return (
    <div className="bg-white rounded-lg shadow p-4 mb-6">
      <div className="flex flex-wrap items-end gap-4">
        <div className="flex-1 min-w-[200px]">
          <label className="block typography-body-sm font-weight-medium text-gray-700 mb-1">
            From Date & Time
          </label>
          <input
            type="datetime-local"
            value={filters.dateFrom || ''}
            onChange={(e) => handleDateFromChange(e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 typography-body-sm focus:ring-2 focus:ring-primary focus:border-primary"
            max={filters.dateTo || undefined}
          />
        </div>

        <div className="flex-1 min-w-[200px]">
          <label className="block typography-body-sm font-weight-medium text-gray-700 mb-1">
            To Date & Time
          </label>
          <input
            type="datetime-local"
            value={filters.dateTo || ''}
            onChange={(e) => handleDateToChange(e.target.value)}
            className="w-full border border-gray-300 rounded-md px-3 py-2 typography-body-sm focus:ring-2 focus:ring-primary focus:border-primary"
            min={filters.dateFrom || undefined}
          />
        </div>

        <div className="flex-1 min-w-[150px]">
          <label className="block typography-body-sm font-weight-medium text-gray-700 mb-1">
            Session Status
          </label>
          <Select
            value={filters.status || ''}
            onChange={(e) => handleStatusChange(e.target.value)}
            className="w-full"
          >
            <option value="">All Statuses</option>
            <option value="active">Active</option>
            <option value="completed">Completed</option>
            <option value="failed">Failed</option>
          </Select>
        </div>

        <div className="flex gap-2">
          <Button 
            onClick={onApplyFilters}
            loading={loading}
            size="sm"
            disabled={loading}
          >
            Apply Filters
          </Button>
          
          <Button 
            onClick={onClearFilters}
            variant="outline"
            size="sm"
            disabled={!hasActiveFilters || loading}
          >
            Clear All
          </Button>
        </div>
      </div>

      {hasActiveFilters && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <div className="flex flex-wrap items-center gap-2">
            <span className="typography-body-sm text-gray-600 font-weight-medium">Active filters:</span>
            {filters.dateFrom && (
              <span className="inline-flex items-center px-2 py-1 rounded-full typography-caption bg-primary-100 text-primary-800">
                From: {new Date(filters.dateFrom).toLocaleString()}
              </span>
            )}
            {filters.dateTo && (
              <span className="inline-flex items-center px-2 py-1 rounded-full typography-caption bg-primary-100 text-primary-800">
                To: {new Date(filters.dateTo).toLocaleString()}
              </span>
            )}
            {filters.status && (
              <span className="inline-flex items-center px-2 py-1 rounded-full typography-caption bg-green-100 text-green-800 capitalize">
                Status: {filters.status}
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default GlobalFilters;