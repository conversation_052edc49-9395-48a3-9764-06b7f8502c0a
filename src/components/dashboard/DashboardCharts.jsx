import React, { useEffect, useRef, useState } from "react";
import { Chart as ChartJS, registerables } from "chart.js";
import { ChevronUp, ChevronDown } from "lucide-react";

// Register all Chart.js components
ChartJS.register(...registerables);

export default function DashboardCharts({
  dailyStats,
  hourlyTrends,
  statusDistribution,
  topEndpoints,
  topUsers,
  geographicData,
  timeRange,
}) {
  // Chart references
  const dailyTrendRef = useRef(null);
  const hourlyTrafficRef = useRef(null);
  const statusDistributionRef = useRef(null);

  // State for expandable sections
  const [showAllEndpoints, setShowAllEndpoints] = useState(false);
  const [showAllUsers, setShowAllUsers] = useState(false);

  // Chart instances
  const chartInstances = useRef({});

  // Utility functions
  const formatNumber = (num) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + "M";
    if (num >= 1000) return (num / 1000).toFixed(1) + "K";
    return num.toString();
  };

  const formatDate = (dateStr) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString("en-US", { month: "short", day: "numeric" });
  };

  const getInitials = (name) => {
    if (!name) return "??";
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  const getMethodClass = (method) => {
    const classes = {
      GET: "method-get",
      POST: "method-post",
      PUT: "method-put",
      DELETE: "method-delete",
      PATCH: "method-patch",
    };
    return classes[method] || "method-get";
  };

  // Generate extended date range for better visualization
  const generateExtendedDateRange = (dailyStats, timeRange) => {
    if (!dailyStats || dailyStats.length === 0) return [];

    const today = new Date();
    const extendedData = [];

    // Generate data for the past 'timeRange' days
    for (let i = timeRange - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(today.getDate() - i);
      const dateStr = date.toISOString().split("T")[0];

      // Find existing data for this date
      const existingData = dailyStats.find((d) => d.date === dateStr);

      extendedData.push({
        date: dateStr,
        requests: existingData ? existingData.requests : 0,
        errors: existingData ? existingData.errors : 0,
        avg_response_time: existingData ? existingData.avg_response_time : 0,
      });
    }

    return extendedData;
  };

  // Initialize charts
  useEffect(() => {
    if (!dailyStats || !hourlyTrends || !statusDistribution) return;

    // Destroy existing charts
    Object.values(chartInstances.current).forEach((chart) => {
      if (chart) chart.destroy();
    });

    initializeCharts();

    return () => {
      // Cleanup on unmount
      Object.values(chartInstances.current).forEach((chart) => {
        if (chart) chart.destroy();
      });
    };
  }, [dailyStats, hourlyTrends, statusDistribution, timeRange]);

  const initializeCharts = () => {
    const extendedDailyData = generateExtendedDateRange(
      dailyStats,
      Math.max(7, timeRange)
    );

    // Daily Trend Chart
    if (dailyTrendRef.current) {
      const ctx = dailyTrendRef.current.getContext("2d");
      chartInstances.current.dailyTrend = new ChartJS(ctx, {
        type: "line",
        data: {
          labels: extendedDailyData.map((d) => formatDate(d.date)),
          datasets: [
            {
              label: "Requests",
              data: extendedDailyData.map((d) => d.requests),
              borderColor: "#F97316",
              backgroundColor: "rgba(59, 130, 246, 0.1)",
              borderWidth: 3,
              fill: true,
              tension: 0.4,
              pointBackgroundColor: "#F97316",
              pointBorderColor: "#ffffff",
              pointBorderWidth: 2,
              pointRadius: 4,
            },
            {
              label: "Errors",
              data: extendedDailyData.map((d) => d.errors),
              borderColor: "#ef4444",
              backgroundColor: "rgba(239, 68, 68, 0.1)",
              borderWidth: 2,
              fill: false,
              tension: 0.4,
              pointBackgroundColor: "#ef4444",
              pointBorderColor: "#ffffff",
              pointBorderWidth: 2,
              pointRadius: 3,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: "top",
            },
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: {
                color: "#f3f4f6",
              },
            },
            x: {
              grid: {
                color: "#f3f4f6",
              },
            },
          },
        },
      });
    }

    // Hourly Traffic Chart - Fixed with proper hour formatting
    if (hourlyTrafficRef.current) {
      const ctx = hourlyTrafficRef.current.getContext("2d");
      chartInstances.current.hourlyTraffic = new ChartJS(ctx, {
        type: "bar",
        data: {
          labels: hourlyTrends.map((h) => {
            // Extract hour from "HH:MM" format or convert number to proper format
            const hourStr =
              typeof h.hour === "string"
                ? h.hour
                : `${h.hour.toString().padStart(2, "0")}:00`;
            return hourStr;
          }),
          datasets: [
            {
              label: "Requests",
              data: hourlyTrends.map((h) => h.requests),
              backgroundColor: "#6366f1",
              borderRadius: 4,
              borderSkipped: false,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false,
            },
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: {
                color: "#f3f4f6",
              },
              title: {
                display: true,
                text: "Number of Requests",
              },
            },
            x: {
              grid: {
                display: false,
              },
              title: {
                display: true,
                text: "Hour of Day",
              },
            },
          },
        },
      });
    }

    // Status Distribution Chart
    if (
      statusDistributionRef.current &&
      Object.keys(statusDistribution).length > 0
    ) {
      const ctx = statusDistributionRef.current.getContext("2d");
      const statusData = Object.entries(statusDistribution);
      chartInstances.current.statusDistribution = new ChartJS(ctx, {
        type: "doughnut",
        data: {
          labels: statusData.map(([code, _]) => `HTTP ${code}`),
          datasets: [
            {
              data: statusData.map(([_, count]) => count),
              backgroundColor: [
                "#10b981", // 200
                "#f59e0b", // 400
                "#ef4444", // 401
                "#6b7280", // 404
                "#dc2626", // 500
              ].slice(0, statusData.length),
              borderWidth: 0,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false,
            },
          },
        },
      });
    }
  };

  const displayedEndpoints = showAllEndpoints
    ? topEndpoints
    : topEndpoints?.slice(0, 5);
  const displayedUsers = showAllUsers ? topUsers : topUsers?.slice(0, 5);

  return (
    <>
      {/* Charts Row 1 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Daily Requests Trend */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="typography-body-lg font-weight-semibold text-gray-900">
              Daily Requests Trend
            </h3>
            <div className="flex items-center space-x-2">
              <button className="text-gray-400 hover:text-gray-600">
                <svg
                  className="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                  />
                </svg>
              </button>
            </div>
          </div>
          <div className="relative h-80">
            <canvas ref={dailyTrendRef}></canvas>
          </div>
        </div>

        {/* Hourly Traffic Pattern */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="typography-body-lg font-weight-semibold text-gray-900">
              Hourly Traffic Pattern
            </h3>
          </div>
          <div className="relative h-80">
            <canvas ref={hourlyTrafficRef}></canvas>
          </div>
        </div>
      </div>

      {/* Charts Row 2 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        {/* Status Code Distribution */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="typography-body-lg font-weight-semibold text-gray-900">
              Status Code Distribution
            </h3>
          </div>
          <div className="relative h-64">
            <canvas ref={statusDistributionRef}></canvas>
          </div>
          <div className="mt-4 space-y-2">
            {Object.entries(statusDistribution).map(([code, count]) => {
              const total = Object.values(statusDistribution).reduce(
                (a, b) => a + b,
                0
              );
              const percentage =
                total > 0 ? ((count / total) * 100).toFixed(1) : "0.0";
              const statusClass =
                {
                  200: "status-200",
                  201: "status-200",
                  400: "status-400",
                  401: "status-401",
                  404: "status-404",
                  500: "status-500",
                }[code] || "status-200";

              const statusLabel =
                {
                  200: "2xx Success",
                  201: "2xx Success",
                  400: "4xx Client Error",
                  401: "4xx Client Error",
                  404: "4xx Client Error",
                  500: "5xx Server Error",
                }[code] || `${code} Status`;

              return (
                <div
                  key={code}
                  className="flex items-center justify-between typography-body-sm"
                >
                  <div className="flex items-center">
                    <span className={`status-indicator ${statusClass}`}></span>
                    <span>{statusLabel}</span>
                  </div>
                  <span className="font-weight-medium">{percentage}%</span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Top Endpoints */}
        <div className="lg:col-span-2 bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="typography-body-lg font-weight-semibold text-gray-900">
              Top API Endpoints
            </h3>
            {topEndpoints && topEndpoints.length > 5 && (
              <button
                onClick={() => setShowAllEndpoints(!showAllEndpoints)}
                className="typography-body-sm text-primary hover:text-primary-800 font-weight-medium flex items-center"
              >
                {showAllEndpoints ? (
                  <>
                    Show Less <ChevronUp className="w-4 h-4 ml-1" />
                  </>
                ) : (
                  <>
                    View All <ChevronDown className="w-4 h-4 ml-1" />
                  </>
                )}
              </button>
            )}
          </div>
          <div
            className={`space-y-4 ${
              showAllEndpoints ? "max-h-96 overflow-y-auto" : ""
            }`}
          >
            {displayedEndpoints?.map((endpoint, index) => {
              const [method, ...pathParts] = endpoint.endpoint.split(" ");
              const path = pathParts.join(" ");
              return (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center">
                      <span
                        className={`endpoint-method ${getMethodClass(method)}`}
                      >
                        {method}
                      </span>
                      <span className="ml-3 font-weight-medium text-gray-900 truncate">
                        {path}
                      </span>
                    </div>
                    <div className="mt-1 typography-body-sm text-gray-600">
                      {formatNumber(endpoint.requests)} requests •{" "}
                      {endpoint.avg_response_time.toFixed(0)}ms avg •{" "}
                      {endpoint.error_rate.toFixed(1)}% errors
                    </div>
                  </div>
                  <div className="text-right ml-4">
                    <div className="typography-body-lg font-weight-semibold text-gray-900">
                      {formatNumber(endpoint.requests)}
                    </div>
                    <div className="progress-bar w-16 mt-1">
                      <div
                        className="progress-fill"
                        style={{
                          width: `${
                            topEndpoints && topEndpoints[0]
                              ? (endpoint.requests / topEndpoints[0].requests) *
                                100
                              : 0
                          }%`,
                        }}
                      ></div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Users and Geographic Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Top Users */}

        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="typography-body-lg font-weight-semibold text-gray-900">
              Most Active Users
            </h3>
            {topUsers && topUsers.length > 5 && (
              <button
                onClick={() => setShowAllUsers(!showAllUsers)}
                className="typography-body-sm text-primary hover:text-primary-800 font-weight-medium flex items-center"
              >
                {showAllUsers ? (
                  <>
                    Show Less <ChevronUp className="w-4 h-4 ml-1" />
                  </>
                ) : (
                  <>
                    View All <ChevronDown className="w-4 h-4 ml-1" />
                  </>
                )}
              </button>
            )}
          </div>
          <div
            className={`space-y-3 ${
              showAllUsers ? "max-h-96 overflow-y-auto" : ""
            }`}
          >
            {displayedUsers?.map((user, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors"
              >
                {/* Left side - User info */}
                <div className="flex items-center min-w-0 flex-1">
                  <div className="flex-shrink-0 h-10 w-10">
                    <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                      <span className="typography-body-sm font-weight-medium text-primary">
                        {getInitials(user.username || user.user_email)}
                      </span>
                    </div>
                  </div>
                  <div className="ml-3 min-w-0 flex-1">
                    <div className="typography-body-sm font-weight-medium text-gray-900 truncate">
                      {user.username || "Unknown"}
                    </div>
                    <div className="typography-caption text-gray-500 truncate">
                      {user.user_email}
                    </div>
                  </div>
                </div>

                {/* Right side - Stats and badge */}
                <div className="flex items-center space-x-3 ml-4">
                  {/* Request count */}
                  <div className="text-right">
                    <div className="typography-body-lg font-weight-semibold text-gray-900">
                      {formatNumber(user.requests)}
                    </div>
                    <div className="typography-caption text-gray-500">requests</div>
                  </div>

                  {/* Role badge */}
                  <div className="flex-shrink-0">
                    <span
                      className={`inline-flex px-3 py-1 typography-caption font-weight-semibold rounded-full ${
                        user.user_role === "admin"
                          ? "bg-purple-100 text-purple-800"
                          : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {user.user_role}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Geographic Data */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="typography-body-lg font-weight-semibold text-gray-900">
              Geographic Distribution
            </h3>
          </div>
          <div className="space-y-4">
            {geographicData?.length > 0 ? (
              geographicData.slice(0, 5).map((location, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  <div className="flex items-center">
                    <div className="typography-body-sm font-weight-medium text-gray-900">
                      {location._id?.country || "Unknown"}
                    </div>
                    <div className="typography-caption text-gray-500 ml-2">
                      {location._id?.region || ""}
                    </div>
                  </div>
                  <div className="typography-body-sm font-weight-semibold text-gray-900">
                    {formatNumber(location.requests)}
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center text-gray-500 py-8">
                <p>No geographic data available</p>
                <p className="typography-caption mt-1">
                  Data will appear when users access from different locations
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
