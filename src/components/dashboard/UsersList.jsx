import React, { useState, useMemo } from 'react';
import { formatCurrency, formatDate, truncateId } from '../../lib/utils';
import Button from '../ui/Button';

const UsersList = ({ 
  users, 
  tenantName, 
  onUserSelect, 
  onBackToOverview,
  loading = false,
  error = null,
  onRetry = null
}) => {
  // Table state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);
  const [sortField, setSortField] = useState('last_session');
  const [sortDirection, setSortDirection] = useState('desc');
  const [searchTerm, setSearchTerm] = useState('');

  // Safe users array handling
  const usersList = Array.isArray(users) ? users : [];

  // Filter users based on search term
  const filteredUsers = useMemo(() => {
    if (!searchTerm.trim()) return usersList;
    
    const searchLower = searchTerm.toLowerCase().trim();
    return usersList.filter(user => 
      user.name?.toLowerCase().includes(searchLower) ||
      user.name?.toLowerCase().includes(searchLower)
    );
  }, [usersList, searchTerm]);

  // Sort users
  const sortedUsers = useMemo(() => {
    return [...filteredUsers].sort((a, b) => {
      let aVal = a[sortField];
      let bVal = b[sortField];

      // Handle different data types
      if (sortField === 'last_session') {
        aVal = aVal ? new Date(aVal).getTime() : 0;
        bVal = bVal ? new Date(bVal).getTime() : 0;
      } else if (typeof aVal === 'string') {
        aVal = aVal.toLowerCase();
        bVal = bVal?.toLowerCase() || '';
      } else if (typeof aVal === 'number') {
        aVal = aVal || 0;
        bVal = bVal || 0;
      }

      if (sortDirection === 'asc') {
        return aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
      } else {
        return aVal < bVal ? 1 : aVal > bVal ? -1 : 0;
      }
    });
  }, [filteredUsers, sortField, sortDirection]);

  // Pagination
  const totalPages = Math.ceil(sortedUsers.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedUsers = sortedUsers.slice(startIndex, startIndex + itemsPerPage);

  // Handle sorting
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
    setCurrentPage(1);
  };

  // Handle search
  const handleSearch = (value) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1);
  };

  // Safe formatting functions
  const safeFormatCurrency = (amount) => {
    try {
      if (amount === null || amount === undefined || isNaN(amount)) {
        return '$0.00';
      }
      return formatCurrency(Number(amount));
    } catch (error) {
      console.warn('Error formatting currency:', error);
      return '$0.00';
    }
  };

  const safeFormatDate = (input) => {
  try {
    const timestamp = input?.endsWith('Z') ? input : input + 'Z';
    const date = new Date(timestamp);

    if (isNaN(date.getTime())) return 'Invalid date';

   const formatted = date.toLocaleString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true,
    });
  return formatted.replace(/\b(am|pm)\b/i, (match) => match.toUpperCase());
  } catch {
    return 'Invalid date';
  }
};


  const safeTruncateId = (id, length = 16) => {
    try {
      if (!id || typeof id !== 'string') {
        return 'Invalid ID';
      }
      return truncateId(id, length);
    } catch (error) {
      console.warn('Error truncating ID:', error);
      return String(id).substring(0, length) + '...';
    }
  };

  const safeFormatNumber = (number) => {
    try {
      if (number === null || number === undefined || isNaN(number)) {
        return '0';
      }
      return Number(number).toLocaleString();
    } catch (error) {
      console.warn('Error formatting number:', error);
      return '0';
    }
  };

  // Sort icon component
  const SortIcon = ({ field }) => {
    if (sortField !== field) {
      return <span className="text-gray-400 ml-1">↕️</span>;
    }
    return (
      <span className="text-primary ml-1">
        {sortDirection === 'asc' ? '↑' : '↓'}
      </span>
    );
  };

  // Pagination component
  const Pagination = () => {
    const getVisiblePages = () => {
      const pages = [];
      const showPages = 5;
      let start = Math.max(1, currentPage - Math.floor(showPages / 2));
      let end = Math.min(totalPages, start + showPages - 1);
      
      if (end - start < showPages - 1) {
        start = Math.max(1, end - showPages + 1);
      }
      
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      return pages;
    };

    return (
      <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-3 sm:space-y-0">
          {/* Left side - Results info and items per page */}
          <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
            <p className="typography-body-sm text-gray-700 whitespace-nowrap">
              Showing{' '}
              <span className="font-weight-medium">{Math.min(startIndex + 1, sortedUsers.length)}</span>
              {' '}to{' '}
              <span className="font-weight-medium">
                {Math.min(startIndex + itemsPerPage, sortedUsers.length)}
              </span>
              {' '}of{' '}
              <span className="font-weight-medium">{sortedUsers.length}</span>
              {' '}results
              {searchTerm && (
                <span className="text-gray-500"> for {searchTerm}</span>
              )}
            </p>
            
            {/* Items per page selector */}
            <div className="flex items-center space-x-2 whitespace-nowrap">
              <label className="typography-body-sm text-gray-700">Show:</label>
              <select
                value={itemsPerPage}
                onChange={(e) => handleItemsPerPageChange(e.target.value)}
                className="border border-gray-300 rounded-md px-6 py-1 typography-body-sm focus:ring-2 focus:ring-primary focus:border-primary bg-white min-w-[100px]"
              >
                <option value={2}>2 per page</option>
                <option value={5}>5 per page</option>
                <option value={10}>10 per page</option>
                <option value={25}>25 per page</option>
                <option value={50}>50 per page</option>
              </select>
            </div>
          </div>

          {/* Right side - Navigation controls */}
          <div className="flex items-center space-x-2">
            {totalPages > 1 ? (
              <nav className="flex items-center space-x-1">
                <button
                  onClick={() => handlePageChange(1)}
                  disabled={currentPage === 1}
                  className="px-2 py-1 typography-body-sm font-weight-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  First
                </button>
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  className="px-2 py-1 typography-body-sm font-weight-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  ←
                </button>
                
                {getVisiblePages().map((page) => (
                  <button
                    key={page}
                    onClick={() => handlePageChange(page)}
                    className={`px-3 py-1 typography-body-sm font-weight-medium border rounded-md ${
                      page === currentPage
                        ? 'bg-primary-600 text-white border-primary-600 hover:bg-primary-700'
                        : 'text-gray-700 bg-white border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {page}
                  </button>
                ))}
                
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  className="px-2 py-1 typography-body-sm font-weight-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  →
                </button>
                <button
                  onClick={() => handlePageChange(totalPages)}
                  disabled={currentPage === totalPages}
                  className="px-2 py-1 typography-body-sm font-weight-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Last
                </button>
              </nav>
            ) : (
              <div className="typography-body-sm text-gray-500 whitespace-nowrap">
                Page 1 of 1
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Loading skeleton
  const LoadingSkeleton = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="h-6 bg-gray-200 rounded w-48 animate-pulse" />
        <div className="flex space-x-4">
          <div className="h-8 bg-gray-200 rounded w-64 animate-pulse" />
          <div className="h-8 bg-gray-200 rounded w-32 animate-pulse" />
        </div>
      </div>
      <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg">
        <table className="min-w-full divide-y divide-gray-300">
          <thead className="bg-gray-50">
            <tr>
              {Array.from({ length: 6 }, (_, i) => (
                <th key={i} className="px-6 py-3">
                  <div className="h-4 bg-gray-200 rounded animate-pulse" />
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {Array.from({ length: 5 }, (_, i) => (
              <tr key={i}>
                {Array.from({ length: 6 }, (_, j) => (
                  <td key={j} className="px-6 py-4">
                    <div className="h-4 bg-gray-200 rounded animate-pulse" />
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <div className="bg-white px-4 py-3 border-t border-gray-200">
        <div className="flex justify-between items-center">
          <div className="h-4 bg-gray-200 rounded w-48 animate-pulse" />
          <div className="flex space-x-2">
            {Array.from({ length: 5 }, (_, i) => (
              <div key={i} className="h-8 bg-gray-200 rounded w-8 animate-pulse" />
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  // Error state
  const ErrorState = () => (
    <div className="text-center py-12">
      <div className="typography-heading-1 mb-6">⚠️</div>
      <h3 className="typography-heading-4 font-weight-medium mb-2 text-gray-900">Unable to Load Users</h3>
      <p className="typography-body-sm max-w-md mx-auto mb-4 text-gray-500">
        {error?.message || `There was an error loading users${tenantName ? ` for "${tenantName}"` : ''}. Please try again.`}
      </p>
      {onRetry && (
        <Button onClick={onRetry} variant="outline">
          Try Again
        </Button>
      )}
    </div>
  );

  // Empty state
  const EmptyState = () => (
    <div className="text-center py-12">
      <div className="typography-heading-1 mb-6">👥</div>
      <h3 className="typography-heading-4 font-weight-medium mb-2 text-gray-900">
        {searchTerm ? 'No Users Found' : 'No Users Available'}
      </h3>
      <p className="typography-body-sm max-w-md mx-auto text-gray-500">
        {searchTerm 
          ? `No users match "${searchTerm}". Try adjusting your search.`
          : tenantName 
            ? `No users have been found for "${tenantName}". Users will appear here once they start using the system.`
            : 'No users have been found for this tenant. Users will appear here once they start using the system.'
        }
      </p>
      {searchTerm && (
        <Button 
          onClick={() => handleSearch('')} 
          variant="outline" 
          className="mt-4"
        >
          Clear Search
        </Button>
      )}
    </div>
  );

  // Handle loading state
  if (loading || (!users && !error)) {
    return <LoadingSkeleton />;
  }

  // Handle error state
  if (error && usersList.length === 0) {
    return <ErrorState />;
  }

  // Safe tenant name handling
  const displayTenantName = tenantName || 'Unknown Tenant';

  return (
    <div className="space-y-6">
      {/* Header with Search and Back Button */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h2 className="typography-heading-4 font-weight-semibold text-gray-900">
          Users in{' '}
          <span className="text-primary break-all" title={displayTenantName}>
            {displayTenantName}
          </span>
        </h2>
        <div className="flex items-center space-x-4">
          <div className="w-full sm:w-64">
            <input
              type="text"
              placeholder="Search by User ID..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 typography-body-sm focus:ring-2 focus:ring-primary focus:border-primary"
            />
          </div>
          <Button variant="ghost" onClick={onBackToOverview}>
            ← Back to Overview
          </Button>
        </div>
      </div>

      {/* Table */}
      {sortedUsers.length === 0 ? (
        <EmptyState />
      ) : (
        <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg bg-white">
          <table className="min-w-full divide-y divide-gray-300">
            <thead className="bg-gray-50">
              <tr>
                <th 
                  className="px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort('id')}
                >
                  <div className="flex items-center">
                    <span>User</span>
                    <SortIcon field="id" />
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort('total_sessions')}
                >
                  <div className="flex items-center">
                    <span>Total Sessions</span>
                    <SortIcon field="total_sessions" />
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort('active_sessions')}
                >
                  <div className="flex items-center">
                    <span>Active Sessions</span>
                    <SortIcon field="active_sessions" />
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort('total_cost')}
                >
                  <div className="flex items-center">
                    <span>Total Cost</span>
                    <SortIcon field="total_cost" />
                  </div>
                </th>
                <th 
                  className="px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors"
                  onClick={() => handleSort('last_session')}
                >
                  <div className="flex items-center">
                    <span>Last Session</span>
                    <SortIcon field="last_session" />
                  </div>
                </th>
                <th className="px-6 py-3 text-left typography-caption font-weight-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {paginatedUsers.map((user, index) => {
                // Ensure user has valid structure
                if (!user || typeof user !== 'object') {
                  return (
                    <tr key={`invalid-user-${index}`} className="bg-yellow-50">
                      <td colSpan={6} className="px-6 py-4 text-center text-yellow-600">
                        Invalid user data
                      </td>
                    </tr>
                  );
                }

                const userId = user.id || user._id || `user-${index}`;
                const username = user.name || 'User'
                const totalSessions = user.total_sessions ?? 0;
                const activeSessions = user.active_sessions ?? 0;
                const totalCost = user.total_cost ?? 0;
                const lastSession = user.last_session;
                const hasValidData = userId !== `user-${index}`;

                return (
                  <tr 
                    key={userId}
                    className={`transition-colors cursor-pointer ${
                      hasValidData ? 'hover:bg-gray-50' : 'bg-yellow-50 cursor-default opacity-75'
                    }`}
                    onClick={() => hasValidData && onUserSelect && onUserSelect(user)}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8">
                          <div className={`h-8 w-8 rounded-full flex items-center justify-center ${
                            hasValidData ? 'bg-primary-100' : 'bg-gray-100'
                          }`}>
                            <span className={`font-weight-medium typography-body-sm ${
                              hasValidData ? 'text-primary' : 'text-gray-400'
                            }`}>
                              {hasValidData ? '👤' : '❓'}
                            </span>
                          </div>
                        </div>
                        <div className="ml-3">
                          <div className="typography-body-sm font-weight-medium text-gray-900">
                            {hasValidData ? 'User' : 'Invalid User'}
                          </div>
                          <div className="typography-caption text-gray-500  break-all max-w-xs" title={userId}>
                            {username}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="typography-body-sm text-gray-900 font-weight-semibold">
                        {safeFormatNumber(totalSessions)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full typography-caption font-weight-medium ${
                        activeSessions > 0 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-600'
                      }`}>
                        {safeFormatNumber(activeSessions)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`typography-body-sm font-weight-semibold ${
                        totalCost > 0 ? 'text-green-600' : 'text-gray-600'
                      }`}>
                        {safeFormatCurrency(totalCost)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="typography-body-sm text-gray-500">
                        {safeFormatDate(lastSession)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right typography-body-sm font-weight-medium">
                      <Button
                        size="sm"
                        variant="outline"
                        disabled={!hasValidData}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (hasValidData && onUserSelect) {
                            onUserSelect(user);
                          }
                        }}
                      >
                        View Sessions
                      </Button>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
          
          {/* Pagination */}
          <Pagination />
        </div>
      )}

      {/* Summary */}
      {!loading && sortedUsers.length > 0 && (
        <div className="typography-body-sm text-gray-500">
          {searchTerm ? (
            <span>
              Found {filteredUsers.length} user{filteredUsers.length !== 1 ? 's' : ''} matching {searchTerm}
              out of {usersList.length} total user{usersList.length !== 1 ? 's' : ''}
            </span>
          ) : (
            <span>
              Showing {usersList.length} user{usersList.length !== 1 ? 's' : ''}
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default UsersList;