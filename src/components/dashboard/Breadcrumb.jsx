// components/dashboard/Breadcrumb.jsx
import { VIEWS } from '../../hooks/useDashboard';
import { truncateId } from '../../lib/utils';

const Breadcrumb = ({ 
  currentView, 
  selectedTenant, 
  selectedUser, 
  onNavigateToOverview,
  onNavigateToTenant,
  onNavigateToUser
}) => {
  const breadcrumbItems = [
    {
      label: '🏠 Overview',
      onClick: onNavigateToOverview,
      active: currentView === VIEWS.OVERVIEW
    }
  ];

  if (selectedTenant && currentView !== VIEWS.OVERVIEW) {
    breadcrumbItems.push({
      label: selectedTenant.name,
      onClick: onNavigateToTenant,
      active: currentView === VIEWS.TENANT
    });
  }

  if (selectedUser && (currentView === VIEWS.USER || currentView === VIEWS.SESSION)) {
    breadcrumbItems.push({
      label: `User ${truncateId(selectedUser.name)}`,
      onClick: onNavigateToUser,
      active: currentView === VIEWS.USER
    });
  }

  if (currentView === VIEWS.SESSION) {
    breadcrumbItems.push({
      label: 'Session Details',
      active: true
    });
  }

  return (
    <nav className="flex mb-6" aria-label="Breadcrumb">
      <ol className="inline-flex items-center space-x-1 md:space-x-3">
        {breadcrumbItems.map((item, index) => (
          <li key={index} className={index === 0 ? 'inline-flex items-center' : ''}>
            {index > 0 && <span className="text-gray-400 mx-2">/</span>}
            {item.onClick && !item.active ? (
              <button
                onClick={item.onClick}
                className="text-gray-700 hover:text-primary transition-colors"
              >
                {item.label}
              </button>
            ) : (
              <span className={item.active ? 'text-gray-500' : 'text-gray-700'}>
                {item.label}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default Breadcrumb;