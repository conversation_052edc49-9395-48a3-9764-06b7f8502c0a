import React, { useState, useEffect } from 'react';
import { AlertCircle, CheckCircle, Info, XCircle, X } from 'lucide-react';
import { cn } from "@/lib/utils";

const AlertBox = ({
  title = "Notification",
  content,
  type = 'default',
  onClose,
  duration = 3000
}) => {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    if (!duration) return;

    const timer = setTimeout(() => {
      setVisible(false);
      onClose?.();
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  if (!visible || !content) return null;

  const variants = {
    success: {
      icon: CheckCircle
    },
    danger: {
      icon: XCircle
    },
    info: {
      icon: Info
    },
    warning: {
      icon: AlertCircle
    },
    error: {
      icon: XCircle
    },
    default: {
      icon: Info
    }
  };

  const variant = variants[type] || variants.default;
  const Icon = variant.icon;

  return (
    <div
      role="alert"
      className={cn(
        "toaster",
        `toaster-${type}`
      )}
    >
      <div className="toaster-content">
        <div className="toaster-icon">
          <Icon className="w-full h-full" />
        </div>

        <div className="toaster-body">
          <div className="toaster-message">
            {content || title}
          </div>
        </div>

        <button
          onClick={onClose}
          className="toaster-close"
          aria-label="Close notification"
        >
          <X className="w-3.5 h-3.5" />
        </button>
      </div>
    </div>
  );
};

export default AlertBox;
