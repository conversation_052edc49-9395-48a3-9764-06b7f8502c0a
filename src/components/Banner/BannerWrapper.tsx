'use client'
import { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { Banner } from '@/components/Banner/Banner';
import { getActiveAnnouncements, acknowledgeAnnouncement } from '@/utils/announcementAPI';
import Cookies from 'js-cookie';

interface ActiveAnnouncement {
  id: string;
  content: string;
  detailed_content?: string;
  type: 'alert' | 'maintenance' | 'announcement';
  published_at: string;
  expires_at?: string;
  is_active: boolean;
}

interface AcknowledgmentData {
  do_not_show_again: boolean;
}

export const BannerWrapper = () => {
  const pathname = usePathname();
  const [activeAnnouncement, setActiveAnnouncement] = useState<ActiveAnnouncement | null>(null);
  const [error, setError] = useState<string | null>(null);
  const username = Cookies.get("username");
  useEffect(() => {
    const fetchAnnouncements = async () => {
      try {
        const announcements = await getActiveAnnouncements();
        if (Array.isArray(announcements) && announcements.length > 0) {
          setActiveAnnouncement(announcements[0]);
        } else {
          setActiveAnnouncement(null);
        }
        // Clear any previous errors
        setError(null);
      } catch (error) {
        // This should now never happen with the updated getActiveAnnouncements
        
        setError('Failed to load announcements');
      }
    };

    if (username) {
      fetchAnnouncements();
    }
    
  }, [pathname, username]);

  const handleAcknowledge = async (id: string, doNotShowAgain: boolean) => {
    try {
      const acknowledgmentData: AcknowledgmentData = {
        do_not_show_again: doNotShowAgain
      };
      
      await acknowledgeAnnouncement(id, acknowledgmentData);
      setActiveAnnouncement(null);
    } catch (error) {
      
      // Optionally show an error message to the user
    }
  };

  if (error || !activeAnnouncement) {
    return null;
  }

  return (
    <Banner
      id={activeAnnouncement.id}
      type={activeAnnouncement.type}
      message={activeAnnouncement.content}
      detailedMessage={activeAnnouncement.detailed_content}
      showBanner={true}
      onAcknowledge={handleAcknowledge}
    />
  );
};