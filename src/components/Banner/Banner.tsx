'use client'
import { useState } from 'react';
import { Info, X, AlertTriangle, Volume2, EyeOff } from 'lucide-react';
import { renderHTML } from '@/utils/helpers';
import { cn } from "@/lib/utils";

interface BannerProps {
  id: string;
  type: 'alert' | 'maintenance' | 'announcement';
  message: string;
  detailedMessage?: string;
  showBanner: boolean;
  onAcknowledge?: (id: string, doNotShowAgain: boolean) => void;
}

export const Banner = ({
  id,
  type,
  message,
  detailedMessage,
  showBanner,
  onAcknowledge
}: BannerProps) => {
  const [isVisible, setIsVisible] = useState(showBanner);
  const [showModal, setShowModal] = useState<'detail' | 'confirm' | null>(null);

  if (!isVisible) return null;



  const getIcon = () => {
    switch(type) {
      case 'alert':
        return <AlertTriangle className="h-5 w-5" />;
      case 'maintenance':
        return <Info className="h-5 w-5" />;
      case 'announcement':
        return <Volume2 className="h-5 w-5" />;
      default:
        return <Info className="h-5 w-5" />;
    }
  };

  const handleSimpleClose = () => {
    setIsVisible(false);
  };

  const handleDoNotShow = () => {
    if (onAcknowledge) {
      onAcknowledge(id, true);
      setIsVisible(false);
      setShowModal(null);
    }
  };

  return (
    <>
      {/* Floating Banner */}
      <div
        className={cn(
          "rounded-lg border flash-banner",
          "shadow-lg hover:shadow-xl transition-all duration-300 ease-out",
          `flash-banner-${type}`
        )}
        style={{
          position: 'fixed',
          top: '1rem',
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 9999,
          maxWidth: '64rem',
          width: 'calc(100% - 2rem)',
          margin: '0 auto'
        }}
      >
        <div className="px-4 py-2.5">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-3 flex-1 min-w-0">
              <div className="flex-shrink-0 rounded-full p-1.5 bg-white/20 backdrop-blur-sm">
                {getIcon()}
              </div>
              <p className="font-medium text-sm truncate">{message}</p>
            </div>

            <div className="flex items-center gap-2 flex-shrink-0">
              {detailedMessage && (
                <button
                  onClick={() => setShowModal('detail')}
                  className={cn(
                    "px-3 py-1.5 bg-white/20 rounded-md text-xs font-medium",
                    "hover:bg-white/30 transition-all duration-150",
                    "backdrop-blur-sm border border-white/20"
                  )}
                >
                  Learn More
                </button>
              )}
              <div className="flex items-center gap-1">
                {onAcknowledge && (
                  <button
                    onClick={() => setShowModal('confirm')}
                    className={cn(
                      "p-1.5 hover:bg-white/20 rounded-full transition-all duration-150",
                      "focus:outline-none focus:ring-2 focus:ring-white/30"
                    )}
                    title="Don't show again"
                    aria-label="Don't show again"
                  >
                    <EyeOff className="w-4 h-4" />
                  </button>
                )}
                <button
                  onClick={handleSimpleClose}
                  className={cn(
                    "p-1.5 hover:bg-white/20 rounded-full transition-all duration-150",
                    "focus:outline-none focus:ring-2 focus:ring-white/30"
                  )}
                  aria-label="Close"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Content Modal */}
      {showModal === 'detail' && (
        <div
          className={cn(
            "fixed inset-0 z-[9999] flex items-center justify-center p-4",
            "flash-modal-backdrop"
          )}
          onClick={(e) => {
            if (e.target === e.currentTarget) setShowModal(null);
          }}
        >
          <div className={cn(
            "bg-custom-bg-primary rounded-lg w-full max-w-lg max-h-[90vh]",
            "flex flex-col relative animate-modal-fade-in",
            "shadow-xl border border-custom-border"
          )}>
            {/* Header - Fixed */}
            <div className="flex justify-between items-center p-4 border-b border-custom-border">
              <h3 className="text-base font-semibold text-custom-text-primary">{message}</h3>
              <button
                onClick={() => setShowModal(null)}
                className={cn(
                  "p-1.5 hover:bg-custom-bg-secondary rounded-full transition-colors",
                  "text-custom-text-secondary hover:text-custom-text-primary"
                )}
              >
                <X className="h-4 w-4" />
              </button>
            </div>

            {/* Content - Scrollable */}
            <div className="flex-1 overflow-y-auto p-4 min-h-[200px]">
              <div className="prose prose-sm max-w-none banner-sp652-div text-custom-text-primary">
                <div dangerouslySetInnerHTML={{
                  __html: renderHTML(detailedMessage || '')
                    .replace(/'/g, '&#39;') // Use `&#39;` for single quotes
                    .replace(/"/g, '&quot;') // Use `&quot;` for double quotes
                }} />
              </div>
            </div>

            {/* Footer - Fixed */}
            <div className="border-t border-custom-border p-4">
              <div className="flex justify-end">
                <button
                  onClick={() => setShowModal(null)}
                  className={cn(
                    "px-4 py-2 bg-custom-bg-secondary text-custom-text-primary rounded-md",
                    "hover:bg-custom-bg-muted transition-colors text-sm font-medium"
                  )}
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Don't Show Again Confirmation Modal */}
      {showModal === 'confirm' && (
        <div className={cn(
          "fixed inset-0 z-[9999] flex items-center justify-center p-4",
          "flash-modal-backdrop"
        )}>
          <div className={cn(
            "bg-custom-bg-primary rounded-lg max-w-sm w-full p-4 relative animate-modal-fade-in",
            "shadow-xl border border-custom-border"
          )}>
            <div className="flex items-center gap-3 mb-3">
              <div className="p-2 bg-primary-100 rounded-full">
                <EyeOff className="h-5 w-5 text-primary-600" />
              </div>
              <h3 className="text-base font-semibold text-custom-text-primary">Hide Announcement</h3>
            </div>

            <p className="text-custom-text-secondary text-sm mb-4">
              This will hide this announcement permanently. Are you sure?
            </p>

            <div className="flex justify-end gap-2">
              <button
                onClick={() => setShowModal(null)}
                className={cn(
                  "px-3 py-1.5 text-sm text-custom-text-secondary",
                  "hover:bg-custom-bg-secondary rounded transition-colors"
                )}
              >
                Cancel
              </button>
              <button
                onClick={handleDoNotShow}
                className={cn(
                  "px-3 py-1.5 text-sm bg-primary-500 text-white rounded",
                  "hover:bg-primary-600 transition-colors font-medium"
                )}
              >
                Yes, don't show again
              </button>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        @keyframes modalFadeIn {
          from { opacity: 0; transform: scale(0.95); }
          to { opacity: 1; transform: scale(1); }
        }
        .animate-modal-fade-in {
          animation: modalFadeIn 0.2s ease-out;
        }
      `}</style>
    </>
  );
};
