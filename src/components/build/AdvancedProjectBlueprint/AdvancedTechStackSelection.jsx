import React from 'react';
import { Server, Laptop, Database } from 'lucide-react';

const AdvancedTechStackSelection = ({
  techStack = { frontend: ["None"], backend: ["None"], database: ["None"] },
  frontendOptions,
  backendOptions,
  databaseOptions,
  onChange,
  isDarkMode = false // This prop is now ignored, always using light mode
}) => {
  // Extract values safely with defaults if undefined
  const frontendValue = techStack?.frontend?.[0] || "None";
  const backendValue = techStack?.backend?.[0] || "None";
  const databaseValue = techStack?.database?.[0] || "None";

  return (
    <div className="mb-6">
      <h3 className="text-lg font-medium mb-4 text-gray-800">Tech Stack</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Frontend Selection */}
        <div 
          className={`border rounded-lg p-4 transition-colors duration-200 ${
            frontendValue === "None"
              ? "border-gray-200 bg-gray-50"
              : "border-primary/20 bg-primary/5"
          }`}
        >
          <div className="flex items-center mb-3">
            <Laptop 
              size={18} 
              className={`mr-2 transition-colors duration-200 ${
                frontendValue === "None"
                  ? "text-gray-400"
                  : "text-primary"
              }`}
            />
            <h4 
              className={`font-medium transition-colors duration-200 ${
                frontendValue === "None" 
                  ? "text-gray-500"
                  : "text-gray-800"
              }`}
            >
              Frontend
            </h4>
          </div>
          <select
            value={frontendValue}
            onChange={(e) => onChange('frontend', e.target.value)}
            className={`w-full p-2 border rounded-md focus:ring-1 focus:ring-primary focus:border-primary transition-colors duration-200 ${
              frontendValue === "None"
                ? "border-gray-300 bg-gray-100 text-gray-500"
                : "border-primary/20 bg-white text-gray-800"
            }`}
          >
            {frontendOptions.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        </div>
        
        {/* Backend Selection */}
        <div 
          className={`border rounded-lg p-4 transition-colors duration-200 ${
            backendValue === "None"
              ? "border-gray-200 bg-gray-50"
              : "border-primary/20 bg-primary/5"
          }`}
        >
          <div className="flex items-center mb-3">
            <Server 
              size={18} 
              className={`mr-2 transition-colors duration-200 ${
                backendValue === "None"
                  ? "text-gray-400"
                  : "text-primary"
              }`}
            />
            <h4 
              className={`font-medium transition-colors duration-200 ${
                backendValue === "None" 
                  ? "text-gray-500"
                  : "text-gray-800"
              }`}
            >
              Backend
            </h4>
          </div>
          <select
            value={backendValue}
            onChange={(e) => onChange('backend', e.target.value)}
            className={`w-full p-2 border rounded-md focus:ring-1 focus:ring-primary focus:border-primary transition-colors duration-200 ${
              backendValue === "None"
                ? "border-gray-300 bg-gray-100 text-gray-500"
                : "border-primary/20 bg-white text-gray-800"
            }`}
          >
            {backendOptions.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        </div>

        {/* Database Selection */}
        <div 
          className={`border rounded-lg p-4 transition-colors duration-200 ${
            databaseValue === "None"
              ? "border-gray-200 bg-gray-50"
              : "border-primary/20 bg-primary/5"
          }`}
        >
          <div className="flex items-center mb-3">
            <Database 
              size={18} 
              className={`mr-2 transition-colors duration-200 ${
                databaseValue === "None"
                  ? "text-gray-400"
                  : "text-primary"
              }`}
            />
            <h4 
              className={`font-medium transition-colors duration-200 ${
                databaseValue === "None" 
                  ? "text-gray-500"
                  : "text-gray-800"
              }`}
            >
              Database
            </h4>
          </div>
          <select
            value={databaseValue}
            onChange={(e) => onChange('database', e.target.value)}
            className={`w-full p-2 border rounded-md focus:ring-1 focus:ring-primary focus:border-primary transition-colors duration-200 ${
              databaseValue === "None"
                ? "border-gray-300 bg-gray-100 text-gray-500"
                : "border-primary/20 bg-white text-gray-800"
            }`}
          >
            {databaseOptions.map(option => (
              <option key={option} value={option}>{option}</option>
            ))}
          </select>
        </div>
      </div>
      
      {/* Warning if all three are None - require at least one selection */}
      {frontendValue === "None" && backendValue === "None" && databaseValue === "None" && (
        <div className="mt-3 px-4 py-2 rounded-md text-sm bg-red-50 border border-red-200 text-red-700">
          Please select at least one option from Frontend, Backend, or Database.
        </div>
      )}
      
      {/* Warning if database is selected but backend is None */}
      {databaseValue !== "None" && backendValue === "None" && (
        <div className="mt-3 px-4 py-2 rounded-md text-sm bg-yellow-50 border border-yellow-200 text-yellow-700">
          You need to choose a backend if you select a database.
        </div>
      )}
    </div>
  );
};

export default AdvancedTechStackSelection; 