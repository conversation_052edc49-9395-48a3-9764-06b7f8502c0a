export interface Template {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  category: 'web' | 'mobile' | 'backend';
  framework: string;
  features: string[];
  complexity: 'simple' | 'medium' | 'complex';
}

export interface ProjectBlueprint {
  id: string;
  name: string;
  description: string;
  features: {
    id: string;
    name: string;
    description: string;
    isEnabled: boolean;
  }[];
  techStack: {
    frontend?: string[];
    backend?: string[];
    database?: string[];
    deployment?: string[];
  };
  estimatedTime: string;
  complexity: 'simple' | 'medium' | 'complex';
}

export interface ProjectCreationState {
  currentStep: 'template' | 'blueprint';
  selectedTemplate: Template | null;
  projectBlueprint: ProjectBlueprint | null;
  customRequirements: string;
} 