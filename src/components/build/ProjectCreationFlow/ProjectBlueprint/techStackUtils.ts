/**
 * Utility function to handle tech stack updates without UI flickering
 * 
 * This function creates a complete tech stack update that maintains exclusivity
 * between frontend and backend frameworks (when one is selected, the other is set to "None")
 * 
 * @param currentTechStack The current tech stack state
 * @param category The category being updated ('frontend', 'backend', 'database')
 * @param value The new value for the category
 * @param backendOptions An array of backend options
 * @returns A complete updated tech stack object
 */
export function createTechStackUpdate(
  currentTechStack: { 
    frontend: string[], 
    backend: string[],
    database: string[]
  },
  category: 'frontend' | 'backend' | 'database',
  value: string,
  backendOptions: string[] = ["Node.js with Express", "Python with Django", "Ruby on Rails", "PHP with Laravel", "Java Spring Boot", "FastAPI"] // default fallback
): { frontend: string[], backend: string[], database: string[] } {
  // Create a complete copy of the current tech stack
  const newTechStack = {
    ...currentTechStack
  };
  
  // Check if setting this value to "None" would result in all being "None"
  const wouldAllBeNone = (
    (category === 'frontend' && value === "None" && newTechStack.backend[0] === "None" && newTechStack.database[0] === "None") ||
    (category === 'backend' && value === "None" && newTechStack.frontend[0] === "None" && newTechStack.database[0] === "None") ||
    (category === 'database' && value === "None" && newTechStack.frontend[0] === "None" && newTechStack.backend[0] === "None")
  );
  
  // If setting to "None" would make all None, don't allow it
  if (wouldAllBeNone) {
    return newTechStack; // Return unchanged
  }
  
  if (category === 'frontend') {
    newTechStack.frontend = [value];
  } 
  else if (category === 'backend') {
    newTechStack.backend = [value];
  }
  else if (category === 'database') {
    newTechStack.database = [value];
    // If database is selected and backend is None, force backend to first available
    if (value !== "None" && (!newTechStack.backend || newTechStack.backend[0] === "None")) {
      // Find first backend option that is not None
      const firstBackend = backendOptions.find(opt => opt !== "None");
      if (firstBackend) {
        newTechStack.backend = [firstBackend];
      }
    }
  }
  
  return newTechStack;
}

/**
 * TO SUPPORT ALL THREE SELECTIONS SIMULTANEOUSLY:
 * 
 * Remove the mutual exclusivity logic by modifying the above function as follows:
 * 
 * export function createTechStackUpdate(
 *   currentTechStack: { 
 *     frontend: string[], 
 *     backend: string[],
 *     database: string[]
 *   },
 *   category: 'frontend' | 'backend' | 'database',
 *   value: string
 * ): { frontend: string[], backend: string[], database: string[] } {
 *   // Create a complete copy of the current tech stack
 *   const newTechStack = {
 *     ...currentTechStack
 *   };
 *   
 *   // Type-safe property assignment without mutual exclusivity
 *   if (category === 'frontend') {
 *     newTechStack.frontend = [value];
 *     // No longer forces backend to "None"
 *   } 
 *   else if (category === 'backend') {
 *     newTechStack.backend = [value];
 *     // No longer forces frontend to "None"
 *   }
 *   else if (category === 'database') {
 *     newTechStack.database = [value];
 *   }
 *   
 *   return newTechStack;
 * }
 */

/**
 * Updates the blueprint state with a new tech stack in a single operation
 * to avoid flickering from multiple state updates
 */
export function updateBlueprintWithTechStack<T extends { techStack: { frontend: string[], backend: string[], database: string[] } }>(
  setBlueprint: React.Dispatch<React.SetStateAction<T>>,
  category: 'frontend' | 'backend' | 'database',
  value: string
): void {
  setBlueprint(prevBlueprint => {
    const newTechStack = createTechStackUpdate(
      prevBlueprint.techStack,
      category,
      value
    );
    
    return {
      ...prevBlueprint,
      techStack: newTechStack
    } as T;
  });
}
