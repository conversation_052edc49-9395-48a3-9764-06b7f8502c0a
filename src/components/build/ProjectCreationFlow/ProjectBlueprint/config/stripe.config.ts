/**
 * Stripe Configuration
 * 
 * Centralized configuration for Stripe integration.
 * This file contains all Stripe-related settings and can be easily
 * modified for different environments (development, staging, production).
 */

// Environment variables (to be set in .env files)
const getEnvVar = (key: string, defaultValue?: string): string => {
  if (typeof process !== 'undefined' && process.env) {
    return process.env[key] || defaultValue || '';
  }
  return defaultValue || '';
};

// Stripe API Configuration
export const STRIPE_CONFIG = {
  // API Keys (should be set via environment variables)
  PUBLISHABLE_KEY: {
    TEST: getEnvVar('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY_TEST', ''),
    LIVE: getEnvVar('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY_LIVE', '')
  },
  
  SECRET_KEY: {
    TEST: getEnvVar('STRIPE_SECRET_KEY_TEST', ''),
    LIVE: getEnvVar('STRIPE_SECRET_KEY_LIVE', '')
  },

  // Webhook Configuration
  WEBHOOK: {
    ENDPOINT: getEnvVar('STRIPE_WEBHOOK_ENDPOINT', '/api/webhooks/stripe'),
    SECRET: getEnvVar('STRIPE_WEBHOOK_SECRET', ''),
    EVENTS: [
      'customer.created',
      'customer.updated',
      'customer.deleted',
      'customer.subscription.created',
      'customer.subscription.updated',
      'customer.subscription.deleted',
      'invoice.created',
      'invoice.payment_succeeded',
      'invoice.payment_failed',
      'payment_intent.succeeded',
      'payment_intent.payment_failed',
      'charge.succeeded',
      'charge.failed'
    ]
  },

  // API Settings
  API: {
    VERSION: '2023-10-16', // Latest Stripe API version
    MAX_RETRIES: 3,
    TIMEOUT: 10000, // 10 seconds
    BASE_URL: 'https://api.stripe.com'
  },

  // Feature Flags
  FEATURES: {
    CONNECT: getEnvVar('STRIPE_CONNECT_ENABLED', 'false') === 'true',
    SUBSCRIPTIONS: getEnvVar('STRIPE_SUBSCRIPTIONS_ENABLED', 'true') === 'true',
    ONE_TIME_PAYMENTS: getEnvVar('STRIPE_ONE_TIME_PAYMENTS_ENABLED', 'true') === 'true',
    INVOICING: getEnvVar('STRIPE_INVOICING_ENABLED', 'true') === 'true',
    TAX_CALCULATION: getEnvVar('STRIPE_TAX_ENABLED', 'false') === 'true'
  },

  // Default Settings
  DEFAULTS: {
    CURRENCY: getEnvVar('STRIPE_DEFAULT_CURRENCY', 'usd'),
    COUNTRY: getEnvVar('STRIPE_DEFAULT_COUNTRY', 'US'),
    LOCALE: getEnvVar('STRIPE_DEFAULT_LOCALE', 'en'),
    TEST_MODE: getEnvVar('NODE_ENV', 'development') !== 'production'
  },

  // Product Configuration
  PRODUCTS: {
    FETCH_LIMIT: 100,
    AUTO_SYNC_INTERVAL: 5 * 60 * 1000, // 5 minutes
    CACHE_TTL: 10 * 60 * 1000 // 10 minutes
  },

  // UI Configuration
  UI: {
    MODAL_ANIMATION_DURATION: 300,
    LOADING_DELAY: 1000,
    ERROR_DISPLAY_DURATION: 5000,
    SUCCESS_DISPLAY_DURATION: 2000
  }
} as const;

// Utility functions for configuration

/**
 * Get the appropriate API key based on environment
 */
export function getStripeApiKey(isTest: boolean = STRIPE_CONFIG.DEFAULTS.TEST_MODE): string {
  return isTest ? STRIPE_CONFIG.SECRET_KEY.TEST : STRIPE_CONFIG.SECRET_KEY.LIVE;
}

/**
 * Get the appropriate publishable key based on environment
 */
export function getStripePublishableKey(isTest: boolean = STRIPE_CONFIG.DEFAULTS.TEST_MODE): string {
  return isTest ? STRIPE_CONFIG.PUBLISHABLE_KEY.TEST : STRIPE_CONFIG.PUBLISHABLE_KEY.LIVE;
}

/**
 * Check if we're in test mode
 */
export function isTestMode(): boolean {
  return STRIPE_CONFIG.DEFAULTS.TEST_MODE;
}

/**
 * Check if a feature is enabled
 */
export function isFeatureEnabled(feature: keyof typeof STRIPE_CONFIG.FEATURES): boolean {
  return STRIPE_CONFIG.FEATURES[feature];
}

/**
 * Get webhook configuration
 */
export function getWebhookConfig() {
  return {
    endpoint: STRIPE_CONFIG.WEBHOOK.ENDPOINT,
    secret: STRIPE_CONFIG.WEBHOOK.SECRET,
    events: STRIPE_CONFIG.WEBHOOK.EVENTS
  };
}

/**
 * Validate Stripe configuration
 */
export function validateStripeConfig(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check for required API keys
  if (!STRIPE_CONFIG.SECRET_KEY.TEST && isTestMode()) {
    errors.push('Test secret key is required in development mode');
  }

  if (!STRIPE_CONFIG.SECRET_KEY.LIVE && !isTestMode()) {
    errors.push('Live secret key is required in production mode');
  }

  if (!STRIPE_CONFIG.PUBLISHABLE_KEY.TEST && isTestMode()) {
    warnings.push('Test publishable key is not set');
  }

  if (!STRIPE_CONFIG.PUBLISHABLE_KEY.LIVE && !isTestMode()) {
    warnings.push('Live publishable key is not set');
  }

  // Check webhook configuration
  if (!STRIPE_CONFIG.WEBHOOK.SECRET) {
    warnings.push('Webhook secret is not set - webhooks will not be verified');
  }

  // Check API settings
  if (STRIPE_CONFIG.API.TIMEOUT < 5000) {
    warnings.push('API timeout is very low - consider increasing it');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Development-specific configuration
 */
export const DEV_CONFIG = {
  // Mock data settings
  MOCK_DATA: {
    ENABLED: getEnvVar('STRIPE_MOCK_ENABLED', 'true') === 'true',
    DELAY_MS: parseInt(getEnvVar('STRIPE_MOCK_DELAY', '1000'), 10),
    FAILURE_RATE: parseFloat(getEnvVar('STRIPE_MOCK_FAILURE_RATE', '0.1'))
  },

  // Debug settings
  DEBUG: {
    LOG_API_CALLS: getEnvVar('STRIPE_DEBUG_API', 'false') === 'true',
    LOG_WEBHOOKS: getEnvVar('STRIPE_DEBUG_WEBHOOKS', 'false') === 'true',
    VERBOSE_ERRORS: getEnvVar('STRIPE_DEBUG_VERBOSE', 'true') === 'true'
  }
};

/**
 * Production-specific configuration
 */
export const PROD_CONFIG = {
  // Security settings
  SECURITY: {
    REQUIRE_HTTPS: true,
    VALIDATE_WEBHOOKS: true,
    LOG_SENSITIVE_DATA: false
  },

  // Performance settings
  PERFORMANCE: {
    CACHE_ENABLED: true,
    BATCH_REQUESTS: true,
    REQUEST_POOLING: true
  }
};

/**
 * Get environment-specific configuration
 */
export function getEnvironmentConfig() {
  return isTestMode() ? DEV_CONFIG : PROD_CONFIG;
}

// Type exports for better TypeScript support
export type StripeConfigType = typeof STRIPE_CONFIG;
export type StripeFeature = keyof typeof STRIPE_CONFIG.FEATURES;
export type StripeCurrency = string; // Could be expanded to specific currency codes
export type StripeLocale = string; // Could be expanded to specific locale codes

// Export default configuration
export default STRIPE_CONFIG; 