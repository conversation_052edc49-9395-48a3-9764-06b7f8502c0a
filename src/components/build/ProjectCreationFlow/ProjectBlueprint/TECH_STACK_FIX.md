# Tech Stack Flickering Fix

To fix the flickering issue when selecting frontend/backend frameworks and to remove the helper text under the dropdowns, follow these steps:

## Step 1: Update the handleTechStackChange function

In `src/components/build/ProjectCreationFlow/ProjectBlueprint/index.tsx`, find the `handleTechStackChange` function and replace it with:

```tsx
// Handle tech stack change with batched updates to prevent flickering
const handleTechStackChange = (category: string, value: string) => {
  // Create a complete copy of the current tech stack to avoid partial updates
  const newTechStack = {
    ...blueprint.techStack,
    [category]: [value]
  };
  
  // Handle mutual exclusivity - if one is selected, the other should be None
  if (category === 'frontend' && value !== "None") {
    newTechStack.backend = ["None"];
  } 
  else if (category === 'backend' && value !== "None") {
    newTechStack.frontend = ["None"];
  }
  
  // Update the entire blueprint with the new tech stack in a single operation
  setBlueprint(prev => ({
    ...prev,
    techStack: newTechStack
  }));
};
```

## Step 2: Replace the Tech Stack section in the render function

Find the Tech Stack section in the render function and replace it with the following code to remove the helper text and add improved visual cues:

```tsx
{/* Tech Stack */}
<div className="mb-6">
  <h3 className="text-lg font-medium mb-4">Tech Stack</h3>
  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
    <div className={`border rounded-lg p-4 transition-colors duration-200 ${
      blueprint.techStack.frontend[0] === "None" 
        ? "border-gray-200 bg-gray-50" 
        : "border-[#f26a1b]/20 bg-orange-50"
    }`}>
      <div className="flex items-center mb-3">
        <Laptop 
          size={18} 
          className={`mr-2 transition-colors duration-200 ${
            blueprint.techStack.frontend[0] === "None" ? "text-gray-400" : "text-[#f26a1b]"
          }`} 
        />
        <h4 
          className={`font-medium ${
            blueprint.techStack.frontend[0] === "None" ? "text-gray-500" : "text-gray-800"
          }`}
        >
          Frontend
        </h4>
      </div>
      <select
        value={blueprint.techStack.frontend[0]}
        onChange={(e) => handleTechStackChange('frontend', e.target.value)}
        className={`w-full p-2 border rounded-md focus:ring-1 focus:ring-[#f26a1b] ${
          blueprint.techStack.frontend[0] === "None" 
            ? "border-gray-300 bg-gray-100 text-gray-500" 
            : "border-[#f26a1b]/20 bg-white text-gray-800"
        }`}
      >
        {frontendOptions.map(option => (
          <option key={option} value={option}>{option}</option>
        ))}
      </select>
    </div>
    
    <div className={`border rounded-lg p-4 transition-colors duration-200 ${
      blueprint.techStack.backend[0] === "None" 
        ? "border-gray-200 bg-gray-50" 
        : "border-[#f26a1b]/20 bg-orange-50"
    }`}>
      <div className="flex items-center mb-3">
        <Server 
          size={18} 
          className={`mr-2 transition-colors duration-200 ${
            blueprint.techStack.backend[0] === "None" ? "text-gray-400" : "text-[#f26a1b]"
          }`} 
        />
        <h4 
          className={`font-medium ${
            blueprint.techStack.backend[0] === "None" ? "text-gray-500" : "text-gray-800"
          }`}
        >
          Backend
        </h4>
      </div>
      <select
        value={blueprint.techStack.backend[0]}
        onChange={(e) => handleTechStackChange('backend', e.target.value)}
        className={`w-full p-2 border rounded-md focus:ring-1 focus:ring-[#f26a1b] ${
          blueprint.techStack.backend[0] === "None" 
            ? "border-gray-300 bg-gray-100 text-gray-500" 
            : "border-[#f26a1b]/20 bg-white text-gray-800"
        }`}
      >
        {backendOptions.map(option => (
          <option key={option} value={option}>{option}</option>
        ))}
      </select>
    </div>
    
    <div className="border border-gray-200 rounded-lg p-4">
      <div className="flex items-center mb-3">
        <Code size={18} className="mr-2 text-[#f26a1b]" />
        <h4 className="font-medium">Language</h4>
      </div>
      <select
        value={blueprint.techStack.language[0]}
        onChange={(e) => handleTechStackChange('language', e.target.value)}
        className="w-full p-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-[#f26a1b]"
      >
        {defaultLanguageOptions.map(option => (
          <option key={option} value={option}>{option}</option>
        ))}
      </select>
    </div>
  </div>
  
  {/* Warning shown only when both are None */}
  {blueprint.techStack.frontend[0] === "None" && blueprint.techStack.backend[0] === "None" && (
    <div className="mt-3 px-4 py-2 bg-yellow-50 border border-yellow-200 text-yellow-700 rounded-md text-sm inline-block">
      Please select at least one framework type
    </div>
  )}
</div>
```

## Why this works

1. The key to fixing the flickering is to ensure we update both the frontend and backend values in a single state update operation
2. The visual cues change color based on whether a framework is selected, making it clear when "None" is selected
3. No helper text is shown under the dropdowns, only a warning appears when both frontend and backend are set to "None"
4. Transition effects are added to make the color changes smooth

If you still face issues with flickering, you can use the utility functions provided in `techStackUtils.ts`. 