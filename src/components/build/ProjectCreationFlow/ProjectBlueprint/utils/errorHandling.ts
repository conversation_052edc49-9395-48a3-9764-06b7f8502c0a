import { StripeConnectionError } from '../types/stripe';

/**
 * Error handling utilities for Stripe integration
 */

/**
 * Stripe error codes and their user-friendly messages
 */
export const STRIPE_ERROR_MESSAGES: Record<string, string> = {
  // Authentication errors
  'authentication_error': 'Invalid API key. Please check your Stripe API key.',
  'invalid_api_key': 'The provided API key is invalid.',
  'api_key_expired': 'Your API key has expired. Please generate a new one.',
  'test_mode_live_key': 'You cannot use a live API key in test mode.',
  'live_mode_test_key': 'You cannot use a test API key in live mode.',

  // Connection errors
  'connection_failed': 'Unable to connect to Stripe. Please check your internet connection.',
  'network_error': 'Network error. Please try again.',
  'timeout_error': 'Request timed out. Please try again.',
  'service_unavailable': 'Stripe service is temporarily unavailable.',

  // API errors
  'api_error': 'An error occurred with the Stripe API.',
  'rate_limit_error': 'Too many requests. Please wait and try again.',
  'invalid_request_error': 'Invalid request. Please check your parameters.',

  // Product errors
  'product_not_found': 'Product not found in your Stripe account.',
  'no_products': 'No products found in your Stripe account.',
  'product_sync_failed': 'Failed to sync products from Stripe.',

  // Permission errors
  'insufficient_permissions': 'Insufficient permissions to access this resource.',
  'restricted_api_key': 'This API key has restricted permissions.',

  // General errors
  'unknown_error': 'An unknown error occurred. Please try again.',
  'validation_error': 'Validation error. Please check your input.'
};

/**
 * Map Stripe error types to user-friendly messages
 */
export function getErrorMessage(error: StripeConnectionError): string {
  // First try to get specific message for the error code
  if (STRIPE_ERROR_MESSAGES[error.code]) {
    return STRIPE_ERROR_MESSAGES[error.code];
  }

  // Fallback to type-based messages
  if (STRIPE_ERROR_MESSAGES[error.type]) {
    return STRIPE_ERROR_MESSAGES[error.type];
  }

  // Use the provided message if available
  if (error.message && error.message.trim()) {
    return error.message;
  }

  // Final fallback
  return STRIPE_ERROR_MESSAGES.unknown_error;
}

/**
 * Determine if an error is recoverable (user can retry)
 */
export function isRecoverableError(error: StripeConnectionError): boolean {
  const recoverableTypes = [
    'network_error',
    'timeout_error',
    'service_unavailable',
    'rate_limit_error'
  ];

  const recoverableCodes = [
    'connection_failed',
    'network_error',
    'timeout_error',
    'service_unavailable',
    'rate_limit_error'
  ];

  return recoverableTypes.includes(error.type) || recoverableCodes.includes(error.code);
}

/**
 * Determine if an error requires user action (e.g., updating API key)
 */
export function requiresUserAction(error: StripeConnectionError): boolean {
  const userActionTypes = [
    'authentication_error',
    'invalid_request_error'
  ];

  const userActionCodes = [
    'authentication_error',
    'invalid_api_key',
    'api_key_expired',
    'test_mode_live_key',
    'live_mode_test_key',
    'insufficient_permissions',
    'restricted_api_key',
    'validation_error'
  ];

  return userActionTypes.includes(error.type) || userActionCodes.includes(error.code);
}

/**
 * Get suggested actions for an error
 */
export function getSuggestedActions(error: StripeConnectionError): string[] {
  const actions: string[] = [];

  switch (error.code) {
    case 'authentication_error':
    case 'invalid_api_key':
      actions.push('Verify your API key in the Stripe Dashboard');
      actions.push('Ensure you\'re using the correct key for your environment');
      break;

    case 'api_key_expired':
      actions.push('Generate a new API key in your Stripe Dashboard');
      actions.push('Update your application with the new key');
      break;

    case 'test_mode_live_key':
      actions.push('Use a test API key (starts with sk_test_) for development');
      break;

    case 'live_mode_test_key':
      actions.push('Use a live API key (starts with sk_live_) for production');
      break;

    case 'insufficient_permissions':
    case 'restricted_api_key':
      actions.push('Check your API key permissions in the Stripe Dashboard');
      actions.push('Ensure the key has the required permissions');
      break;

    case 'connection_failed':
    case 'network_error':
      actions.push('Check your internet connection');
      actions.push('Try again in a few moments');
      break;

    case 'rate_limit_error':
      actions.push('Wait a few minutes before trying again');
      actions.push('Reduce the frequency of API requests');
      break;

    case 'no_products':
      actions.push('Create products in your Stripe Dashboard');
      actions.push('Ensure products are active and published');
      break;

    default:
      if (isRecoverableError(error)) {
        actions.push('Try again in a few moments');
      }
      if (requiresUserAction(error)) {
        actions.push('Check your Stripe Dashboard for more details');
      }
      break;
  }

  return actions;
}

/**
 * Create a standardized error object from various error sources
 */
export function createStripeError(
  code: string,
  message: string,
  type: StripeConnectionError['type'] = 'api_error'
): StripeConnectionError {
  return {
    code,
    message,
    type
  };
}

/**
 * Parse error from various sources (API responses, network errors, etc.)
 */
export function parseError(error: unknown): StripeConnectionError {
  // If it's already a StripeConnectionError
  if (isStripeConnectionError(error)) {
    return error;
  }

  // If it's a standard Error object
  if (error instanceof Error) {
    return createStripeError(
      'unknown_error',
      error.message,
      'api_error'
    );
  }

  // If it's a string
  if (typeof error === 'string') {
    return createStripeError(
      'unknown_error',
      error,
      'api_error'
    );
  }

  // If it's an object with error information
  if (typeof error === 'object' && error !== null) {
    const errorObj = error as any;
    
    return createStripeError(
      errorObj.code || 'unknown_error',
      errorObj.message || 'An unknown error occurred',
      errorObj.type || 'api_error'
    );
  }

  // Fallback for unknown error types
  return createStripeError(
    'unknown_error',
    'An unknown error occurred',
    'api_error'
  );
}

/**
 * Type guard to check if an object is a StripeConnectionError
 */
export function isStripeConnectionError(error: unknown): error is StripeConnectionError {
  return (
    typeof error === 'object' &&
    error !== null &&
    'code' in error &&
    'message' in error &&
    'type' in error
  );
}

/**
 * Log error for debugging (in development) or monitoring (in production)
 */
export function logError(error: StripeConnectionError, context?: string): void {
  const errorInfo = {
    code: error.code,
    message: error.message,
    type: error.type,
    context,
    timestamp: new Date().toISOString()
  };

  // In development, log to console
  if (process.env.NODE_ENV === 'development') {
    console.error('Stripe Error:', errorInfo);
  }

  // In production, you might want to send to a monitoring service
  // Example: sendToMonitoringService(errorInfo);
}

/**
 * Retry configuration for different error types
 */
export const RETRY_CONFIG = {
  maxAttempts: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  
  // Errors that should be retried
  retryableErrors: [
    'network_error',
    'timeout_error',
    'service_unavailable',
    'rate_limit_error',
    'connection_failed'
  ],

  // Delay multiplier for each retry
  backoffMultiplier: 2
};

/**
 * Calculate retry delay with exponential backoff
 */
export function calculateRetryDelay(attempt: number): number {
  const delay = RETRY_CONFIG.baseDelay * Math.pow(RETRY_CONFIG.backoffMultiplier, attempt - 1);
  return Math.min(delay, RETRY_CONFIG.maxDelay);
}

/**
 * Determine if an error should be retried
 */
export function shouldRetry(error: StripeConnectionError, attempt: number): boolean {
  if (attempt >= RETRY_CONFIG.maxAttempts) {
    return false;
  }

  return RETRY_CONFIG.retryableErrors.includes(error.code) || 
         RETRY_CONFIG.retryableErrors.includes(error.type);
} 