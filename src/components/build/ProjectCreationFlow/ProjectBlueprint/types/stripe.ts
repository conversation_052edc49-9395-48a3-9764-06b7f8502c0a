// Core Stripe API Types for Integration-Ready Implementation

// Basic Stripe API Response Structure
export interface StripeApiResponse<T> {
  object: string;
  data: T[];
  has_more: boolean;
  url: string;
}

// Main Stripe Product Interface (compatible with Stripe API)
export interface StripeProduct {
  id: string;
  object: 'product';
  active: boolean;
  created: number;
  default_price?: string | StripePrice;
  description: string | null;
  features: StripeProductFeature[];
  images: string[];
  livemode: boolean;
  metadata: Record<string, string>;
  name: string;
  type: 'good' | 'service';
  updated: number;
  url?: string | null;
  // UI-specific fields (not part of Stripe API)
  selected?: boolean;
}

// Stripe Price Interface
export interface StripePrice {
  id: string;
  object: 'price';
  active: boolean;
  billing_scheme: 'per_unit' | 'tiered';
  created: number;
  currency: string;
  livemode: boolean;
  lookup_key?: string | null;
  metadata: Record<string, string>;
  nickname?: string | null;
  product: string | StripeProduct;
  recurring?: StripeRecurring | null;
  type: 'one_time' | 'recurring';
  unit_amount?: number | null;
  unit_amount_decimal?: string | null;
}

// Stripe Account Interface
export interface StripeAccount {
  id: string;
  object: 'account';
  business_type?: 'company' | 'government_entity' | 'individual' | 'non_profit' | null;
  charges_enabled: boolean;
  country: string;
  created: number;
  default_currency?: string;
  details_submitted: boolean;
  email?: string | null;
  metadata: Record<string, string>;
  payouts_enabled: boolean;
  type: 'custom' | 'express' | 'standard';
}

// Stripe Customer Interface
export interface StripeCustomer {
  id: string;
  object: 'customer';
  address?: StripeAddress | null;
  balance: number;
  created: number;
  currency?: string | null;
  delinquent: boolean;
  description?: string | null;
  email?: string | null;
  livemode: boolean;
  metadata: Record<string, string>;
  name?: string | null;
  phone?: string | null;
}

// Supporting Types
export interface StripeProductFeature {
  name: string;
}

export interface StripeRecurring {
  interval: 'day' | 'month' | 'week' | 'year';
  interval_count: number;
  trial_period_days?: number | null;
  usage_type: 'licensed' | 'metered';
}

export interface StripeAddress {
  city?: string | null;
  country?: string | null;
  line1?: string | null;
  line2?: string | null;
  postal_code?: string | null;
  state?: string | null;
}

// Connection and Configuration Types
export interface StripeConnectionConfig {
  apiKey: string;
  isTestMode: boolean;
  webhookEndpoint?: string;
  webhookSecret?: string;
  accountId?: string;
}

export interface StripeConnectionState {
  isConnected: boolean;
  isVerifying: boolean;
  lastVerified?: Date;
  account?: StripeAccount;
  errors?: StripeConnectionError[];
}

export interface StripeConnectionError {
  code: string;
  message: string;
  type: 'api_error' | 'authentication_error' | 'card_error' | 'idempotency_error' | 'invalid_request_error' | 'rate_limit_error';
  param?: string;
}

// Service Layer Types
export interface StripeServiceConfig {
  apiKey: string;
  apiVersion?: string;
  maxRetries?: number;
  timeout?: number;
}

export interface StripeListOptions {
  limit?: number;
  starting_after?: string;
  ending_before?: string;
}

// Response Types for Service Layer
export interface StripeProductsResponse extends StripeApiResponse<StripeProduct> {}
export interface StripePricesResponse extends StripeApiResponse<StripePrice> {}
export interface StripeCustomersResponse extends StripeApiResponse<StripeCustomer> {}

// UI State Types (for managing component state)
export interface StripeUIState {
  currentStep: 'api-key' | 'verifying' | 'product-selection' | 'completing' | 'success';
  apiKey: string;
  products: StripeProduct[];
  selectedProducts: string[];
  loading: boolean;
  error: string;
}

// Integration Settings
export interface StripeIntegrationSettings {
  selectedProducts: string[];
  webhookEvents: string[];
  customMetadata: Record<string, string>;
  defaultCurrency: string;
  testMode: boolean;
} 