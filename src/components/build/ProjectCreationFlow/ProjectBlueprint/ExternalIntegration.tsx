import React, { useEffect, useState, useContext } from 'react';
import { Link2, ChevronLeft, ChevronRight, ChevronDown, Plus, Check } from 'lucide-react';
// import StripeConnectionModal from './StripeConnectionModal'; // Temporarily disabled
import SupabaseConnectionModal from './SupabaseConnectionModal';
import { connectToSupabase, disconnectSupabaseProject } from '@/utils/api';

import { getGitHubConfigurations } from '@/utils/scmAPI';
import { getOAuthLoginURL } from '@/utils/scmAPI';
import { AlertContext } from '@/components/NotificationAlertService/AlertList';

import { GitHubLogo, SupabaseLogo, StripeLogo } from './ExternalIntegrationLogos';

interface Integration {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  status: 'connected' | 'not_connected' | 'locked';
  color: {
    bg?: string;
    text: string;
    border: string;
    lightBg?: string;
  };
}

const ExternalIntegrations: React.FC = () => {
  // const [stripeModalOpen, setStripeModalOpen] = useState(false); // Temporarily disabled
  const [supabaseModalOpen, setSupabaseModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(0);
  const [selectedGithubOrg, setSelectedGithubOrg] = useState<any>(null);
  const [showGithubDropdown, setShowGithubDropdown] = useState(false);
  const [githubOrganizations, setGithubOrganizations] = useState([]);
  const [loadingGithubOrgs, setLoadingGithubOrgs] = useState(false);
  const [connectingGithub, setConnectingGithub] = useState(false);
  const { showAlert } = useContext(AlertContext)
  const [loadingIntegration, setLoadingIntegration] = useState<string | null>(null);

  // Integration status for non-GitHub integrations
  const [integrationStatus, setIntegrationStatus] = useState<Record<string, 'connected' | 'not_connected'>>({
    supabase: 'not_connected',
    stripe: 'not_connected'
  });

  // Initialize session storage on mount
  useEffect(() => {
    sessionStorage.setItem('selected_github_id', '');
  }, []);

  const checkSupabaseConnection = async () => {
    try {
      const projectId = sessionStorage.getItem('generated_project_id');
      if (!projectId) return;

      const response = await connectToSupabase(projectId.toString());
      if (response && response.status === "already_connected") {
        setIntegrationStatus(prev => ({
          ...prev,
          supabase: 'connected'
        }));
      }
    } catch (error) {
      console.error("Error checking supabase connection:", error);
    }
  };

  const loadGitHubOrganizations = async () => {
    setLoadingGithubOrgs(true);
    try {
      const response: any = await getGitHubConfigurations();
      if (response && response.data && response.data.configurations) {
        setGithubOrganizations(response.data.configurations);
      }
    } catch (error) {
      console.error("Error loading GitHub organizations:", error);
    } finally {
      setLoadingGithubOrgs(false);
    }
  };

  const integrations: Integration[] = [
    {
      id: 'github',
      name: 'GitHub',
      description: 'Code Repository & Version Control',
      icon: <GitHubLogo />,
      status: selectedGithubOrg ? 'connected' : 'not_connected',
      color: {
        bg: 'bg-gray-900',
        text: 'text-gray-900',
        border: 'border-gray-900',
        lightBg: 'bg-gray-50'
      }
    },
    {
      id: 'supabase',
      name: 'Supabase',
      description: 'Database & Authentication',
      icon: <SupabaseLogo />,
      status: integrationStatus.supabase,
      color: {
        text: 'text-green-500',
        border: 'border-green-500',
        lightBg: 'bg-green-50'
      }
    },
    {
      id: 'stripe',
      name: 'Stripe',
      description: 'Payments & Subscriptions',
      icon: <StripeLogo />,
      status: 'locked', // Set as locked instead of using integrationStatus
      color: {
        bg: 'bg-purple-600',
        text: 'text-purple-600',
        border: 'border-purple-600',
        lightBg: 'bg-purple-50'
      }
    }
  ];

  // Pagination settings
  const itemsPerPage = 2;
  const totalPages = Math.ceil(integrations.length / itemsPerPage);
  const showCarouselControls = integrations.length > itemsPerPage;

  // Get current page items
  const startIndex = currentPage * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentIntegrations = integrations.slice(startIndex, endIndex);

  const handleConnect = (integrationId: string) => {
    // Don't allow connecting to locked integrations
    const integration = integrations.find(i => i.id === integrationId);
    if (integration?.status === 'locked') {
      showAlert('This integration is coming soon!', 'info');
      return;
    }

    if (integrationId === 'supabase') {
      setSupabaseModalOpen(true);
    } else if (integrationId === 'github') {
      setShowGithubDropdown(true);
    }
  };

  const handleDisconnect = async (integrationId: string) => {
    if (integrationId === 'github') {
      setSelectedGithubOrg(null);
      setShowGithubDropdown(false);
      sessionStorage.setItem('selected_github_id', '');
    }
    else if (integrationId === 'supabase') {
      const projectId = sessionStorage.getItem('generated_project_id');
      if (!projectId) return;

      try {
        setLoadingIntegration(integrationId);
        // Call the disconnectSupabase endpoint with project_id
        const response = await disconnectSupabaseProject(projectId.toString())
        if (response.success && response.status === 'disconnected') {
          // Update integration status to not_connected
          setIntegrationStatus(prev => ({
            ...prev,
            [integrationId]: 'not_connected'
          }));
          showAlert("Supabase disconnected successfully", "success")
        }
      } catch (error) {
        showAlert("Failed to disconnect the supabase", "danger")
      } finally {
        setLoadingIntegration(null);
      }
    } else {
      setIntegrationStatus(prev => ({
        ...prev,
        [integrationId]: 'not_connected'
      }));
    }
  };

  // const handleStripeConnectionComplete = () => {
  //   setIntegrationStatus(prev => ({
  //     ...prev,
  //     stripe: 'connected'
  //   }));
  // }; // Temporarily disabled

  const handleSupabaseConnectionComplete = () => {
    setIntegrationStatus(prev => ({
      ...prev,
      supabase: 'connected'
    }));
    setSupabaseModalOpen(false);
  };

  const goToPreviousPage = () => {
    setCurrentPage(prev => Math.max(0, prev - 1));
  };

  const goToNextPage = () => {
    setCurrentPage(prev => Math.min(totalPages - 1, prev + 1));
  };

  const handleSupabaseModalClose = () => {
    setSupabaseModalOpen(false);
    setTimeout(() => {
      checkSupabaseConnection();
    }, 100);
  };

  // Handle GitHub organization selection
  const handleGithubOrgChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedId = e.target.value;
    if (selectedId) {
      sessionStorage.setItem("selected_github_id", selectedId);
      const selected = githubOrganizations.find(
        (org: any) => org.encrypted_scm_id === selectedId
      );
      setSelectedGithubOrg(selected || null);
    } else {
      setSelectedGithubOrg(null);
      sessionStorage.setItem('selected_github_id', '');
    }
  };

  useEffect(() => {
    checkSupabaseConnection();
    loadGitHubOrganizations();
  }, []);

  const handleGitHubConnect = async () => {
    try {
      setConnectingGithub(true);
      const data: any = await getOAuthLoginURL('github');
      const url = data.data.url;
      const width = 1025, height = 700;
      const left = (window.screen.width / 2) - (width / 2);
      const top = (window.screen.height / 2) - (height / 2);

      const popup = window.open(
        url,
        'GitHub OAuth',
        `width=${width},height=${height},top=${top},left=${left}`
      );

      if (!popup) {
        setConnectingGithub(false);
        return;
      }

      let messageReceived = false;

      // Listen for postMessage from popup
      const handleMessage = async (event: any) => {
        if (event.data === 'github_connected') {
          messageReceived = true;
          await loadGitHubOrganizations();
          setShowGithubDropdown(true);
          cleanup();
        }
      };

      // Cleanup function
      const cleanup = () => {
        setConnectingGithub(false);
        clearInterval(checkClosed);
        window.removeEventListener('message', handleMessage);
      };

      // Check if popup is closed
      const checkClosed = setInterval(async () => {
        if (popup.closed) {
          // Always reload organizations when popup closes, regardless of success message
          await loadGitHubOrganizations();

          // If we received a success message, show the dropdown
          if (messageReceived) {
            setShowGithubDropdown(true);
          }

          cleanup();
        }
      }, 1000);

      window.addEventListener('message', handleMessage);

      // Timeout cleanup after 5 minutes
      setTimeout(() => {
        if (!popup.closed) {
          popup.close();
        }
        cleanup();
      }, 5 * 60 * 1000);

    } catch (error) {
      console.error('GitHub OAuth failed', error);
      setConnectingGithub(false);
    }
  };

  return (
    <>
      <div className="mb-6">
        <h3 className="typography-body-lg font-weight-medium mb-4 text-gray-800">
          Third party Integrations
        </h3>

        <div className="relative">
          {/* Carousel Container */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {currentIntegrations.map((integration) => {
              const isConnected = integration.status === 'connected';
              const isLocked = integration.status === 'locked';

              return (
                <div
                  key={integration.id}
                  className={`border rounded-lg p-4 bg-white transition-all duration-200 ${
                    isConnected
                      ? `border-green-500 bg-green-50`
                      : isLocked
                      ? 'border-gray-300 bg-gray-50 opacity-75'
                      : 'border-gray-200 hover:border-gray-300'
                    }`}
                >
                  <div className="flex items-start space-x-3">
                    {/* Icon */}
                    <div
                      className={`w-10 h-10 rounded-full ${integration.color.bg || 'bg-gray-100'} flex items-center justify-center flex-shrink-0`}
                    >
                      {integration.icon}
                    </div>

                    {/* Content */}
                    <div className="flex-1">
                      <h4 className="typography-body-sm font-weight-medium text-gray-900">
                        {integration.name}
                      </h4>
                      <p className="typography-caption text-gray-500 mt-0.5">
                        {integration.description}
                      </p>

                      {/* Status */}
                      <div className="flex items-center mt-2">
                        {isLocked ? (
                          <>
                            <svg className="w-3 h-3 mr-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                            </svg>
                            <span className="typography-caption text-gray-500">
                              Coming Soon
                            </span>
                          </>
                        ) : (
                          <>
                            <span className={`w-2 h-2 rounded-full mr-2 ${isConnected ? 'bg-green-500' : 'bg-gray-300'
                              }`}></span>
                            <span className={`typography-caption ${isConnected ? 'text-green-600' : 'text-gray-500'
                              }`}>
                              {isConnected ? 'Connected' : 'Not Connected'}
                            </span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* GitHub Special Handling */}
                  {integration.id === 'github' ? (
                    <div className="mt-4 space-y-3">
                      {/* Selected Organization Display */}
                      {selectedGithubOrg && (
                        <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-2">
                              <Check size={16} className="text-green-600" />
                              <span className="typography-body-sm font-weight-medium text-green-800">
                                {selectedGithubOrg.organization || selectedGithubOrg.credentials?.organization}
                              </span>
                            </div>
                            <button
                              onClick={() => handleDisconnect('github')}
                              className="typography-caption text-green-600 hover:text-green-800 font-weight-medium"
                            >
                              Change
                            </button>
                          </div>
                        </div>
                      )}

                      {/* GitHub Account Selection */}
                      {(showGithubDropdown || githubOrganizations.length > 0) && !selectedGithubOrg && (
                        <div className="space-y-2">
                          <label className="typography-caption text-gray-600 font-weight-medium block">
                            Select GitHub Account
                          </label>
                          <div className="relative">
                            <select
                              value=""
                              onChange={handleGithubOrgChange}
                              disabled={loadingGithubOrgs}
                              className={`w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary appearance-none bg-white typography-body-sm ${loadingGithubOrgs ? 'cursor-not-allowed opacity-60' : ''
                                }`}
                            >
                              <option value="">
                                {loadingGithubOrgs ? 'Loading accounts...' : 'Choose an account'}
                              </option>
                              {!loadingGithubOrgs && githubOrganizations.map((org: any) => (
                                <option key={org.encrypted_scm_id} value={org.encrypted_scm_id}>
                                  {org.organization || org.credentials?.organization}
                                </option>
                              ))}
                            </select>
                            <ChevronDown size={16} className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" />
                          </div>
                        </div>
                      )}

                      {/* Connect New Account Button */}
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={handleGitHubConnect}
                          disabled={connectingGithub || loadingGithubOrgs}
                          className={`flex-1 px-4 py-2.5 border border-gray-900 text-gray-900 bg-white rounded-lg hover:bg-gray-50 flex items-center justify-center transition-all duration-200 typography-body-sm font-weight-medium ${(connectingGithub || loadingGithubOrgs) ? 'cursor-not-allowed opacity-60' : ''
                            }`}
                        >
                          {connectingGithub ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900 mr-2"></div>
                              Connecting...
                            </>
                          ) : (
                            <>
                              <Plus size={16} className="mr-2" />
                              {githubOrganizations.length > 0 ? 'Add Account' : 'Connect GitHub'}
                            </>
                          )}
                        </button>

                        {/* Disconnect Button */}
                        {selectedGithubOrg && (
                          <button
                            onClick={() => handleDisconnect('github')}
                            className="px-4 py-2.5 border border-red-300 text-red-600 bg-white rounded-lg hover:bg-red-50 typography-body-sm font-weight-medium transition-colors duration-200"
                          >
                            Disconnect
                          </button>
                        )}
                      </div>

                      {/* Initial Connect Button (when no accounts available) */}
                      {!showGithubDropdown && githubOrganizations.length === 0 && !selectedGithubOrg && (
                        <button
                          onClick={() => handleConnect(integration.id)}
                          disabled={loadingGithubOrgs}
                          className={`w-full px-4 py-2.5 border border-gray-900 text-gray-900 bg-white rounded-lg hover:bg-gray-50 flex items-center justify-center transition-colors duration-200 typography-body-sm font-weight-medium ${loadingGithubOrgs ? 'cursor-not-allowed opacity-60' : ''
                            }
                          ${!loadingGithubOrgs && githubOrganizations.length === 0 ? 'hidden' : ''}
                          `}
                        >
                          {loadingGithubOrgs ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900 mr-2"></div>
                              Loading...
                            </>
                          ) : (
                            <>
                            </>
                          )}
                        </button>
                      )}
                    </div>
                  ) : (
                    /* Other Integrations Connect/Disconnect */
                    <div className="mt-4">
                      {isLocked ? (
                        <button
                          disabled
                          className="w-full px-4 py-2.5 border border-gray-300 text-gray-400 bg-gray-100 rounded-lg cursor-not-allowed flex items-center justify-center transition-colors duration-200 typography-body-sm font-weight-medium"
                        >
                          <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                          </svg>
                          Coming Soon
                        </button>
                      ) : isConnected ? (
                        <button
                          onClick={() => handleDisconnect(integration.id)}
                          className="w-full px-4 py-2.5 border border-gray-300 text-gray-600 bg-white rounded-lg hover:bg-gray-50 flex items-center justify-center transition-colors duration-200 typography-body-sm font-weight-medium"
                          disabled={loadingIntegration === 'supabase'}
                        >
                          {loadingIntegration === 'supabase' ? (
                            <div className="flex items-center gap-2">
                              <svg
                                className="w-4 h-4 animate-spin text-gray-600"
                                xmlns="http://www.w3.org/2000/svg"
                                fill="none"
                                viewBox="0 0 24 24"
                              >
                                <circle
                                  className="opacity-25"
                                  cx="12"
                                  cy="12"
                                  r="10"
                                  stroke="currentColor"
                                  strokeWidth="4"
                                ></circle>
                                <path
                                  className="opacity-75"
                                  fill="currentColor"
                                  d="M4 12a8 8 0 018-8v8H4z"
                                ></path>
                              </svg>
                              Disconnecting...
                            </div>
                          ) : (
                            "Disconnect"
                          )}
                        </button>
                      ) : (
                        <button
                          onClick={() => handleConnect(integration.id)}
                          className={`w-full px-4 py-2.5 border ${integration.color.border} ${integration.color.text} bg-white rounded-lg hover:bg-gray-50 flex items-center justify-center transition-colors duration-200 typography-body-sm font-weight-medium`}
                        >
                          <Link2 size={16} className="mr-2" />
                          Connect
                        </button>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* Floating Arrow Controls */}
          {showCarouselControls && (
            <>
              {/* Left Arrow */}
              <button
                onClick={goToPreviousPage}
                disabled={currentPage === 0}
                className={`absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 bg-white rounded-full shadow-lg p-2 transition-all duration-200 ${currentPage === 0
                    ? 'opacity-50 cursor-not-allowed'
                    : 'hover:shadow-xl hover:scale-110 cursor-pointer'
                  }`}
                aria-label="Previous page"
              >
                <ChevronLeft size={20} className="text-gray-600" />
              </button>

              {/* Right Arrow */}
              <button
                onClick={goToNextPage}
                disabled={currentPage === totalPages - 1}
                className={`absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 bg-white rounded-full shadow-lg p-2 transition-all duration-200 ${currentPage === totalPages - 1
                    ? 'opacity-50 cursor-not-allowed'
                    : 'hover:shadow-xl hover:scale-110 cursor-pointer'
                  }`}
                aria-label="Next page"
              >
                <ChevronRight size={20} className="text-gray-600" />
              </button>
            </>
          )}
        </div>

        {/* Dot Indicators */}
        {showCarouselControls && (
          <div className="flex justify-center items-center space-x-2 mt-4">
            {Array.from({ length: totalPages }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentPage(index)}
                className={`w-2 h-2 rounded-full transition-all duration-200 ${index === currentPage
                    ? 'bg-gray-800 w-6'
                    : 'bg-gray-300 hover:bg-gray-400'
                  }`}
                aria-label={`Go to page ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>

      {/* Stripe Connection Modal - Temporarily disabled */}
      {/* <StripeConnectionModal
        isOpen={stripeModalOpen}
        onClose={() => setStripeModalOpen(false)}
        onConnectionComplete={handleStripeConnectionComplete}
      /> */}

      {/* Supabase Connection Modal */}
      <SupabaseConnectionModal
        isOpen={supabaseModalOpen}
        onClose={handleSupabaseModalClose}
        onConnectionComplete={handleSupabaseConnectionComplete}
      />
    </>
  );
};

export default ExternalIntegrations;