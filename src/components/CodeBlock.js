"use client";
import React, { useState, useRef, useEffect, useCallback, memo, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> } from "lucide-react";
import { PrismLight as SyntaxHighlighter } from "react-syntax-highlighter";
import {
  tomorrow,
  solarizedlight,
} from "react-syntax-highlighter/dist/esm/styles/prism";
import jsx from "react-syntax-highlighter/dist/esm/languages/prism/jsx";
import python from "react-syntax-highlighter/dist/esm/languages/prism/python";
import { renderHTML } from "../utils/helpers";
import { useRightContent } from '@/components/Context/RightContentContext';

SyntaxHighlighter.registerLanguage("jsx", jsx);
SyntaxHighlighter.registerLanguage("python", python);


const CodeSnippetViewer = memo(({ code, language }) => {
  const [copied, setCopied] = useState(false);
  const [darkMode, setDarkMode] = useState(true);
  const containerRef = useRef(null);

  const copyToClipboard = useCallback(() => {
    navigator.clipboard.writeText(code).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  }, [code]);

  const bgColor = darkMode ? "#1a202c" : "#f7fafc";

  // Memoize the customStyle object to prevent it from being recreated on each render
  const customStyle = useMemo(() => ({
    margin: 0,
    padding: "1rem",
    backgroundColor: "transparent",
    fontSize: "var(--font-size-code)",
    lineHeight: "1.5",
    whiteSpace: "pre-wrap",
    wordBreak: "break-word",
    overflowWrap: "break-word",
    maxWidth: "100%",
  }), []);

  return (
    <div
      ref={containerRef}
      className="rounded-lg border relative w-full overflow-hidden"
      style={{ backgroundColor: bgColor }}
    >
      <div
        className={`px-4 py-2 flex justify-between items-center rounded-lg ${
          darkMode ? "bg-semantic-gray-200" : "bg-semantic-gray-100"
        }`}
      >
        <span
          className={`typography-body-sm font-weight-medium ${
            darkMode ? "text-semantic-gray-700" : "text-semantic-gray-600"
          }`}
        >
          {language}
        </span>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setDarkMode(!darkMode)}
            className={`p-1 rounded-full ${
              darkMode
                ? "bg-semantic-gray-300 text-semantic-gray-700"
                : "bg-semantic-gray-200 text-semantic-gray-600"
            }`}
          >
            {darkMode ? <Sun size={18} /> : <Moon size={18} />}
          </button>
          <button
            onClick={copyToClipboard}
            className={`${
              darkMode
                ? "text-semantic-gray-500 hover:text-semantic-gray-700"
                : "text-semantic-gray-500 hover:text-semantic-gray-700"
            }`}
          >
            {copied ? <Check size={18} /> : <Copy size={18} />}
          </button>
        </div>
      </div>
      <div
        className="overflow-x-auto mt-4 custom-scrollbar rounded-lg"
        style={{ backgroundColor: bgColor, maxWidth: "100%" }}
      >
        <SyntaxHighlighter
          language={language.toLowerCase()}
          style={darkMode ? tomorrow : solarizedlight}
          customStyle={customStyle}
        >
          {code}
        </SyntaxHighlighter>
      </div>
      {copied && (
        <div className="mt-2 p-4 bg-green-100 border-l-4 border-green-500 text-green-700">
          <p className="typography-body-sm">Code copied to clipboard!</p>
        </div>
      )}
    </div>
  );
}, (prevProps, nextProps) => {
  return prevProps.code === nextProps.code &&
         prevProps.language === nextProps.language;
});
CodeSnippetViewer.displayName = 'CodeSnippetViewer';


// Memoize the splitTextAndCode function since it's expensive
const splitTextAndCode = (input) => {
  if (!input) return [];
  if(input.includes('-------------------')){
    input = input.replace(/-------------------/g, '\n');
  }

  const blocks = [];
  let currentBlock = { content: '', iscode: false, language: null };
  const lines = input.split('\n');

  lines.forEach((line) => {
    if (line.trim().startsWith('```')) {
      if (!currentBlock.iscode) {
        if (currentBlock.content.trim()) {
          blocks.push({ ...currentBlock });
        }
        const language = line.trim().slice(3).trim();
        currentBlock = {
          content: '',
          iscode: true,
          language: language
        };
      } else {
        if (currentBlock.content.trim()) {
          blocks.push({ ...currentBlock });
        }
        currentBlock = { content: '', iscode: false, language: null };
      }
    } else {
      if (currentBlock.content || line.trim()) {
        currentBlock.content += (currentBlock.content ? '\n' : '') + line;
      }
    }
  });

  if (currentBlock.content.trim()) {
    blocks.push(currentBlock);
  }

  return blocks;
};

const CodeBlock = memo(({ markdownString, message_end, timestamp, user=null }) => {
  const [codeBlocks, setCodeBlocks] = useState([]);
  const { setRightContent } = useRightContent();

  // Memoize parseDocumentSections to prevent recreating on every render
  const parseDocumentSections = useCallback((content) => {
    if (!content) return [];

    const lines = content.split('\n');
    const sections = [];
    let currentSection = null;
    let isInCodeBlock = false;
    let codeContent = '';
    let codeLanguage = '';

    lines.forEach((line) => {
      // Handle code blocks
      if (line.trim().startsWith('```')) {
        if (!isInCodeBlock) {
          // Starting a code block
          if (currentSection) {
            sections.push(currentSection);
            currentSection = null;
          }
          isInCodeBlock = true;
          codeLanguage = line.trim().slice(3).trim();
          codeContent = '';
        } else {
          // Ending a code block
          if (codeContent.trim()) {
            sections.push({
              type: codeLanguage.toLowerCase() === 'mermaid' ? 'mermaid' : 'code',
              language: codeLanguage,
              content: codeContent.trim()
            });
          }
          isInCodeBlock = false;
        }
        return;
      }

      if (isInCodeBlock) {
        codeContent += line + '\n';
        return;
      }

      // Handle regular content
      if (line.startsWith('#')) {
        if (currentSection) {
          sections.push(currentSection);
        }
        currentSection = line;
      } else {
        if (currentSection) {
          currentSection += '\n' + line;
        } else {
          currentSection = line;
        }
      }
    });

    // Add the last section
    if (currentSection) {
      sections.push(currentSection);
    }

    return sections;
  }, []);

  // Extract document processing to a separate function to improve readability
  const processDocument = useCallback((markdownString, blocks) => {
    // Check for Document: format
    if (markdownString.includes('Document:')) {
      // Split the string at "Document:" to isolate the document content
      const parts = markdownString.split('Document:');
      // Get the content after "Document:"
      const documentContent = parts[1].trim();

      // Parse the document content into sections
      const sections = parseDocumentSections(documentContent);

      // Extract title (first line or first heading)
      let title = '';
      let contentSections = sections;

      // If the first section is a string and starts with a heading
      if (sections.length > 0 && typeof sections[0] === 'string') {
        // Extract only the first line for the title
        const firstLineEndIndex = sections[0].indexOf('\n');

        if (firstLineEndIndex !== -1) {
          // If there's a newline, extract just the first line as title
          title = sections[0].substring(0, firstLineEndIndex).replace(/^#+\s*/, '').trim();
          // Keep the remaining content as the first section
          sections[0] = sections[0].substring(firstLineEndIndex + 1);
          contentSections = sections[0].trim() ? sections : sections.slice(1);
        } else {
          // If there's no newline, use the whole first section as title
          title = sections[0].replace(/^#+\s*/, '').trim();
          contentSections = sections.slice(1);
        }
      }

      return {
        title,
        sections: [
          `# ${title}` || '',
          ...contentSections
        ],
        id: Date.now().toString(),
        timestamp: timestamp || "Unknown",
        isComplete: true
      };
    }
    else if (markdownString) {
      // Handle markdown with headings but without "Document:" format
      const documentContent = markdownString
        .split('\n')
        .map(line => line.replace(/\s+$/, ''))
        .filter(line => !line.startsWith('-------------------'))
        .join('\n')
        .replace(/\n+$/, '');

      const sections = parseDocumentSections(documentContent);

      // Extract title (first heading only)
      let title = '';
      let contentSections = sections;

      if (sections.length > 0 && typeof sections[0] === 'string' && sections[0].startsWith('#')) {
        const firstLineEndIndex = sections[0].indexOf('\n');
        if (firstLineEndIndex !== -1) {
          // Extract just the first line as the title
          title = sections[0].substring(0, firstLineEndIndex).replace(/^#+\s*/, '').trim();
          sections[0] = sections[0].substring(firstLineEndIndex + 1);
          contentSections = !sections[0].trim() ? sections.slice(1) : sections;
        } else {
          title = sections[0].replace(/^#+\s*/, '').trim();
          contentSections = sections.slice(1);
        }
      }

      return {
        title,
        sections: [
          title || '',
          ...contentSections
        ],
        id: Date.now().toString() + Math.floor(100000 + Math.random() * 900000).toString(),
        timestamp: timestamp || "Unknown",
        isComplete: true
      };
    }
    else {
      // Handle mermaid diagrams
      const mermaidBlocks = blocks.filter(
        block => block.iscode && block.language?.toLowerCase() === 'mermaid'
      );

      if (mermaidBlocks.length > 0) {
        const textBlocks = blocks
          .filter(block => !block.iscode || block.language?.toLowerCase() !== 'mermaid')
          .map(block => block.content)
          .join('\n')
          .trim();

        return {
          title: '',
          sections: [
            '# Diagram',
            ...(textBlocks ? [{ type: 'text', content: textBlocks }] : []),
            ...mermaidBlocks.map(block => ({
              type: 'mermaid',
              content: block.content.trim().replace(/^[\r\n]+|[\r\n]+$/g, ''),
              language: 'mermaid'
            }))
          ],
          id: Date.now().toString() + Math.floor(100000 + Math.random() * 900000).toString(),
          timestamp: new Date().toISOString(),
          isComplete: true
        };
      }
    }

    return null;
  }, [parseDocumentSections, timestamp]);

  // Optimize updating right content to avoid repeated checks
  const updateRightContent = useCallback((documentData) => {
    if (!documentData) return;

    setRightContent(prevContent => {
      const existingDocs = prevContent?.data || [];

      // Check for duplicates
      const isDuplicate = existingDocs.some(doc =>
        doc.isComplete &&
        JSON.stringify(doc.sections) === JSON.stringify(documentData.sections)
      );

      if (isDuplicate) {
        return prevContent;
      }

      // Add new document to existing documents
      return {
        type: 'document',
        data: [...existingDocs, documentData]
      };
    });
  }, [setRightContent]);

  // Use a debounced effect to avoid multiple processing
  useEffect(() => {
    if (!markdownString) return;

    // Use a small timeout to batch operations
    const timer = setTimeout(() => {
      const blocks = splitTextAndCode(markdownString);
      setCodeBlocks(blocks);

      // Only process right panel content when needed
      if (user !== "user" && message_end) {
        const documentData = processDocument(markdownString, blocks);
        if (documentData) {
          updateRightContent(documentData);
        }
      }
    }, 50); // Small delay to batch operations

    return () => clearTimeout(timer);
  }, [markdownString, message_end, user, processDocument, updateRightContent]);

  // Memoize the renderBlock function to prevent recreating it on each render
  const renderBlock = useCallback((content, index) => {
    if (content.iscode) {
      return (
        <div key={index} className="w-full max-w-full overflow-x-auto">
          <CodeSnippetViewer
            code={content.content}
            language={content.language || ""}
          />
        </div>
      );
    }

    return (
      <div key={index} className="w-full max-w-full overflow-hidden">
        <div className="project-panel-content break-words">
          <span
            dangerouslySetInnerHTML={{
              __html: renderHTML(content.content),
            }}
          />
        </div>
      </div>
    );
  }, []);

  // Memoize the rendered blocks array
  const renderedBlocks = useMemo(() =>
    codeBlocks.map((content, index) => renderBlock(content, index)),
    [codeBlocks, renderBlock]
  );

  return (
    <div className="w-full max-w-full overflow-x-auto">
      {renderedBlocks}
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison for memo
  return prevProps.markdownString === nextProps.markdownString &&
         prevProps.message_end === nextProps.message_end;
});

// Add displayName for debugging
CodeBlock.displayName = 'CodeBlock';

export default CodeBlock;