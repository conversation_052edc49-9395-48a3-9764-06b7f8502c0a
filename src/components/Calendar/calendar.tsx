import {useState} from 'react';
import dayjs from 'dayjs';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DateCalendar } from '@mui/x-date-pickers/DateCalendar';

interface YearMonthCalendarProps {
    date: Date,
    startDate?: Date ,
    onChange? : (newValue: Date) => void
}

export default function YearMonthCalendar({date, startDate ,onChange}: YearMonthCalendarProps) {
    const [view, setView] = useState("month");  
    const [defaultDate, setDefaultDate] = useState<string | null>(`${date.getFullYear()}-${date.getMonth() + 1}-01`);
    const startDateString: string = startDate? `${startDate.getFullYear()}-${startDate.getMonth() + 1}-01` : '2020-01-01'

    return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DateCalendar  
                defaultValue={dayjs(defaultDate)}
                minDate={dayjs(startDateString)}
                maxDate={dayjs('2040-12-31')}
                views={['year', 'month']}
                openTo='month'
                onViewChange={(view) => setView(view)}
                onChange={(value, state) => {onChange && onChange(value.toDate())}}
                className={view==='month'? 'max-h-[280px]' : ''}
            />
        </LocalizationProvider>
    );
}