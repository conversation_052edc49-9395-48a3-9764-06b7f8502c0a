// src/components/UserOnboarding/OrganizationDetails/OrganizationDetails.tsx
import { FC } from 'react';
import { Building2 } from 'lucide-react';

interface OrganizationDetailsProps {
  name: string;
  industryType: string;
  companySize: string;
  businessEmail: string;
}

const OrganizationDetails: FC<OrganizationDetailsProps> = ({
  name,
  industryType,
  companySize,
  businessEmail
}) => {
  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
      <div className="flex items-center gap-2 mb-6">
        <div className="flex items-center justify-center bg-primary-50 rounded-md p-1.5">
          <Building2 className="h-5 w-5 text-primary" />
        </div>
        <h2 className="text-primary font-weight-semibold">Organization Details</h2>
      </div>

      <div className="flex flex-col gap-6">
        <div className="grid grid-cols-2 gap-x-28">
          <div className="flex items-start gap-20">
            <span className="text-gray-500 w-36">Organization Name</span>
            <span className="text-gray-900 font-weight-medium">{name}</span>
          </div>
          <div className="flex items-start gap-20">
            <span className="text-gray-500 w-36">Company Size</span>
            <span className="text-gray-900 font-weight-medium">{companySize}</span>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-x-28">
          <div className="flex items-start gap-20">
            <span className="text-gray-500 w-36">Industry Type</span>
            <span className="text-gray-900 font-weight-medium">{industryType}</span>
          </div>
          <div className="flex items-start gap-20">
            <span className="text-gray-500 w-36">Business Email</span>
            <span className="text-gray-900 font-weight-medium">{businessEmail}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrganizationDetails;