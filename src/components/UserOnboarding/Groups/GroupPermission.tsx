// components/UserOnboarding/Groups/GroupPermission.tsx
import React, { useState } from 'react';
import { Toggle } from '@/components/UserOnboarding/ui/Toggle';

interface Permission {
  create: boolean;
  read: boolean;
  update: boolean;
  delete: boolean;
  merge: boolean;
}

interface FeaturePermissions {
  name: string;
  permissions: Permission;
}

interface GroupPermissionProps {
  initialFeatures: FeaturePermissions[];
  onPermissionChange?: (features: FeaturePermissions[]) => void;
}

const GroupPermission: React.FC<GroupPermissionProps> = ({
  initialFeatures,
  onPermissionChange
}) => {
  const [features, setFeatures] = useState<FeaturePermissions[]>(initialFeatures);
  const headers = ['Features (Phases)', 'Create', 'Read', 'Update', 'Delete', 'Merge/Approval'];

  // Ensure features exists before mapping
  if (!features || features.length === 0) {
    return <div>No features available</div>;
  }

  const handleToggle = (featureIndex: number, permission: keyof Permission) => {
    const updatedFeatures = features.map((feature, index) => {
      if (index === featureIndex) {
        return {
          ...feature,
          permissions: {
            ...feature.permissions,
            [permission]: !feature.permissions[permission]
          }
        };
      }
      return feature;
    });

    setFeatures(updatedFeatures);
    onPermissionChange?.(updatedFeatures);
  };

  return (
    <div className="w-full border border-gray-200 rounded-lg overflow-hidden bg-white">
      <table className="w-full border-collapse">
        <thead>
          <tr>
            {headers.map((header, index) => (
              <th
                key={index}
                className={`
                  px-4 py-3 text-left typography-body-sm font-weight-medium text-gray-700 bg-gray-50 border-b
                  ${index !== 0 ? 'border-l border-gray-200' : ''}
                `}
              >
                {header}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {features && features.map((feature, featureIndex) => (
            <tr key={featureIndex} className="border-b last:border-b-0">
              <td className="px-4 py-3 typography-body-sm text-gray-700">
                {feature.name}
              </td>
              {Object.entries(feature.permissions).map(([permission, value], index) => (
                <td 
                  key={permission} 
                  className="px-4 py-3 border-l border-gray-200"
                >
                  <Toggle
                    enabled={value}
                    onChange={() => handleToggle(featureIndex, permission as keyof Permission)}
                    size="small"
                  />
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default GroupPermission;