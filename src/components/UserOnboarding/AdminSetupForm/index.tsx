'use client';

import React, { useState, useEffect } from "react";
import { FormField } from "../ui/FormField";
import { ContactNumberFieldUI } from "../ui/ContactNumberField";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import Cookies from 'js-cookie';
import Link from "next/link";

const COOKIE_NAME = 'org_details_form';

interface AdminSetupFormProps {
    onBack: () => void
    onNext: () => void
}

interface AdminFormData {
    admin_name: string;
    work_email: string;
    contact_number: string;
    department: string;
}

interface PhoneNumberData {
    countryCode: string;
    plainNumber: string;
}

interface CompleteFormData {
    // Previous form data
    name: string;
    business_email: string;
    industrial_type: string;
    company_size: string;
    domain: string;
    image: string;
    // Admin form data
    admin_name: string;
    work_email: string;
    countryCode: string;
    plainNumber: string;
    contact_number: string;
    department: string;
}

export const AdminSetupForm: React.FC<AdminSetupFormProps> = ({ onNext, onBack }) => {
    const [adminFormData, setAdminFormData] = useState<AdminFormData>({
        admin_name: "",
        work_email: "",
        contact_number: "",
        department: ""
    });

    const [isDataValid, setIsDataValid] = useState(false);
    const [errors, setErrors] = useState<Partial<Record<keyof AdminFormData, string>>>({});
    const [phoneNumberData, setPhoneNumberData] = useState<PhoneNumberData>({
        countryCode: "+91",
        plainNumber: ""
    })

    // Load existing data from cookie when component mounts
    useEffect(() => {
        const savedData = Cookies.get(COOKIE_NAME);
        if (savedData) {
            try {
                const parsedData = JSON.parse(savedData);
                setAdminFormData({
                    admin_name: parsedData.admin_name || "",
                    work_email: parsedData.work_email || "",
                    contact_number: parsedData.contact_number || "",
                    department: parsedData.department || ""
                });

                setPhoneNumberData({
                    countryCode: parsedData.countryCode || "+91",
                    plainNumber: parsedData.plainNumber || ""
                });

            } catch (error) {
                
            }
        }
    }, []);

    const isValidName = (name: string): boolean => name.trim().length > 0;
    const isValidEmail = (email: string): boolean => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    const isValidContactNumber = (number: string): boolean => {
        // Check basic format first
        if (!/^\+[0-9]{2,3}[0-9]{10}$/.test(number)) {
            return false;
        }
        // Extract the actual number part after country code
        const numberPart = number.slice(3); // Skip the '+' and country code
        // Check if it's all zeros
        return !/^0+$/.test(numberPart);
    };

    useEffect(() => {
        const validateData = () => {
            //Destructure form data
            const { admin_name, work_email, contact_number, department } = adminFormData;

            //check validity of each field
            const isAdminNameValid = isValidName(admin_name);
            const isWorkEmailValid = isValidEmail(work_email);
            const isContactNumberValid = isValidContactNumber(contact_number);
            const isDepartmetValid = isValidName(department);

            return (
                isAdminNameValid &&
                isWorkEmailValid &&
                isContactNumberValid &&
                isDepartmetValid
            )
        }

        setIsDataValid(validateData());

    }, [adminFormData]);

    const checkFieldError = (field: keyof AdminFormData) => {
        setErrors((prevErrors) => {
            const updatedErrors = { ...prevErrors };

            if (field == 'admin_name') {
                if (!isValidName(adminFormData['admin_name'])) {
                    updatedErrors.admin_name = "Admin Name is required.";
                }
            }

            if (field == 'work_email') {
                if (!isValidEmail(adminFormData['work_email'])) {
                    updatedErrors.work_email = "Invalid or empty email.";
                }
            }

            if (field == 'contact_number') {
                if (!isValidContactNumber(phoneNumberData.countryCode + phoneNumberData.plainNumber)) {
                    updatedErrors.contact_number = "Invalid or empty contact number.";
                }
            }

            if (field == 'department') {
                if (!isValidName(adminFormData['department'])) {
                    updatedErrors.department = "Department Name is required.";
                }
            }

            return updatedErrors;
        });
    }


    const removeError = (field: keyof AdminFormData) => {
        setErrors((prevErrors) => {
            const updatedErrors = { ...prevErrors };
            delete updatedErrors[field]; // Remove the error for the specific field
            return updatedErrors;
        });
    }

    const handleInputChange = (field: keyof AdminFormData, value: string) => {
        setAdminFormData(prevData => {
            const newAdminData = {
                ...prevData,
                [field]: value
            };

            // Get existing cookie data
            const existingData = Cookies.get(COOKIE_NAME);
            let completeData: CompleteFormData;

            if (existingData) {
                completeData = {
                    ...JSON.parse(existingData),
                    ...newAdminData
                };
            } else {
                completeData = {
                    name: "",
                    business_email: "",
                    industrial_type: "",
                    company_size: "",
                    domain: "",
                    image: "",
                    ...phoneNumberData,
                    ...newAdminData
                };
            }
            Cookies.set(COOKIE_NAME, JSON.stringify(completeData));
            return newAdminData;
        });
    };

    const handleContactNumberChange = (numberField: keyof PhoneNumberData, value: string) => {
        setPhoneNumberData(prevData => {
            const newPhoneNumberData = {
                ...prevData,
                [numberField]: value
            };

            const fullPhoneNumber = newPhoneNumberData.countryCode + newPhoneNumberData.plainNumber;

            //Fetching old data from cookies
            const existingData = Cookies.get(COOKIE_NAME);
            let completeData: CompleteFormData;

            if (existingData) {
                completeData = {
                    ...JSON.parse(existingData),
                    ...newPhoneNumberData
                };
            } else {
                completeData = {
                    name: "",
                    business_email: "",
                    industrial_type: "",
                    company_size: "",
                    domain: "",
                    image: "",
                    ...adminFormData,
                    ...newPhoneNumberData
                };
            }

            //Setting newData in cookies
            Cookies.set(COOKIE_NAME, JSON.stringify(completeData));

            //passing to actual input manager
            handleInputChange("contact_number", fullPhoneNumber);

            return newPhoneNumberData;
        })
    }

    const handleNext = () => {

        // Save current state before moving to next tab
        const existingData = Cookies.get(COOKIE_NAME);
        if (existingData) {
            const completeData = {
                ...JSON.parse(existingData),
                ...adminFormData
            };
            Cookies.set(COOKIE_NAME, JSON.stringify(completeData));
        }
        onNext()
    };

    const handleBack = () => {
        // Save current state before going back
        const existingData = Cookies.get(COOKIE_NAME);
        if (existingData) {
            const completeData = {
                ...JSON.parse(existingData),
                ...adminFormData
            };
            Cookies.set(COOKIE_NAME, JSON.stringify(completeData));
        }
        onBack()
    };

    const handleSaveAsDraft = () => {
        // Save current state
        const existingData = Cookies.get(COOKIE_NAME);
        if (existingData) {
            const completeData = {
                ...JSON.parse(existingData),
                ...adminFormData
            };
            Cookies.set(COOKIE_NAME, JSON.stringify(completeData));
        }
        // You might want to show a success message here
    };

    return (
        <div className="w-full">
            <h2 className="project-panel-heading mb-2">Enter your organization information</h2>
            <form className="flex flex-wrap gap-5 w-full mb-5" onSubmit={(e) => e.preventDefault()}>
                <FormField
                    label="Admin Name"
                    placeholder="Name"
                    value={adminFormData.admin_name}
                    onChange={(e) => handleInputChange("admin_name", e.target.value)}
                    error={errors.admin_name}
                    onFocus={() => removeError("admin_name")}
                    onBlur={() => checkFieldError("admin_name")}
                />
                <FormField
                    label="Work Email"
                    placeholder="<EMAIL>"
                    value={adminFormData.work_email}
                    onChange={(e) => handleInputChange("work_email", e.target.value)}
                    error={errors.work_email}
                    onFocus={() => removeError("work_email")}
                    onBlur={() => checkFieldError("work_email")}
                />
                <div className="w-[49%]">
                    <ContactNumberFieldUI
                        label="Contact Number"
                        placeholder="XXXXXXXXXX"
                        countryCode={phoneNumberData.countryCode}
                        plainNumber={phoneNumberData.plainNumber}
                        onCountryCodeChange={(e) => handleContactNumberChange("countryCode", e.target.value)}
                        onPlainNumberChange={(e) => handleContactNumberChange("plainNumber", e.target.value)}
                        error={errors.contact_number}
                        onFocus={() => removeError("contact_number")}
                        onBlur={() => checkFieldError("contact_number")}
                    />
                </div>
                <FormField
                    label="Department"
                    placeholder="e.g., IT, Operations"
                    value={adminFormData.department}
                    onChange={(e) => handleInputChange("department", e.target.value)}
                    error={errors.department}
                    onFocus={() => removeError("department")}
                    onBlur={() => checkFieldError("department")}
                />
            </form>

            <div className="flex justify-between py-1 mt-3">
                <DynamicButton
                    variant="ghost"
                    text="Back"
                    onClick={handleBack}
                />
                <div className="flex gap-4">
                    <DynamicButton
                        variant="ghostPrimary"
                        text="Save as Draft"
                        onClick={handleSaveAsDraft}
                    />
                    <Link href="/dashboard/organizations">
                        <DynamicButton
                            variant="primary"
                            text="Cancel"
                        />
                    </Link>
                    <DynamicButton
                        variant="primary"
                        text="Next: Plan Selection"
                        disabled={!isDataValid}
                        onClick={() => {
                            if (isDataValid) {
                                handleNext();
                            }
                        }}
                    />
                </div>
            </div>
        </div>
    );
};