// components/layout/Sidebar/SidebarItem.tsx
import Link from 'next/link';

interface SidebarItemProps {
  icon?: React.ReactNode;
  label: string;
  href: string;
  active?: boolean;
  className?: string;
  onClick?: () => void;
  target?: string;
  title?: string
}

export const SidebarItem = ({ 
  icon, 
  label, 
  href, 
  active, 
  className = '',
  onClick,
  target,
  title
}: SidebarItemProps) => {
  const baseClasses = "flex items-center gap-3 px-4 py-2 typography-body-sm transition-colors duration-200";
  const stateClasses = active 
    ? "bg-gray-100 text-gray-900 font-weight-medium" 
    : "text-gray-600 hover:bg-gray-50";
  
  return (
    <Link
      href={href}
      onClick={onClick}
      className={`${baseClasses} ${stateClasses} ${className}`}
      target={target}
    >
      {icon && (
        <span className="w-5 h-5 flex items-center justify-center">
          {icon}
        </span>
      )}
      <span className="flex-1 truncate" title={title}>{label}</span>
    </Link>
  );
};