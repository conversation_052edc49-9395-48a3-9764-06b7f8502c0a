'use client';

import React, { useState, useEffect, useCallback } from "react";
import { FormField } from "../ui/FormField";
import { FileUploader } from "./FileUploader";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { DropDownMenuPropsUI } from "../ui/DropdownMenu";
import Cookies from 'js-cookie';
import { debounce } from 'lodash';
import { checkOrganizationExists } from "@/utils/api";
import Link from "next/link";
const COOKIE_NAME = 'org_details_form';

const IndustryDropDownItems = [
    { label: "Technology" },
    { label: "Media" },
    { label: "Health" },
    { label: "Finance" },
    { label: "Education" },
    { label: "Retail" },
    { label: "Manufacturing" },
    { label: "Transportation" },
    { label: "Construction" },
    { label: "Hospitality" },
    { label: "Real Estate" },
    { label: "Telecommunications" },
    { label: "Energy" },
    { label: "Agriculture" },
    { label: "Pharmaceuticals" },
    { label: "Automotive" },
    { label: "Non-Profit" },
    { label: "Entertainment" },
    { label: "Gaming" },
    { label: "Consulting" },
    { label: "Insurance" },
    { label: "Legal" },
    { label: "Government" },
    { label: "Cybersecurity" },
    { label: "Artificial Intelligence" },
    { label: "Blockchain" },
    { label: "E-commerce" },
    { label: "Logistics" },
    { label: "Media & Advertising" },
    { label: "Fashion" },
    { label: "Food & Beverage" },
    { label: "Sports" },
    { label: "Travel" }
];

const CompanySizeDropDownItems = [
    { label: "20+ employees" },
    { label: "50+ employees" },
    { label: "100+ employees" },
    { label: "200+ employees" },
    { label: "500+ employees" },
    { label: "1000+ employees" }
];

interface AddOrganizationFormProps {
    handleTabClick: (tabId: string) => void;
}

interface FormDataType {
    name: string;
    industrial_type: string;
    company_size: string;
    domain: string;
    image: string;
    username: string;
}

const generateUniqueUsername = (name: string): string => {
    const baseUsername = name.toLowerCase().replace(/[^a-z0-9]/g, '');
    const randomNum = Math.floor(1000 + Math.random() * 9000);
    return `${baseUsername}${randomNum}`;
};

export const AddOragnizationForm: React.FC<AddOrganizationFormProps> = ({
    handleTabClick
}) => {
    const [formData, setFormData] = useState<FormDataType>({
        name: "",
        industrial_type: "Technology",
        company_size: "500+ employees",
        domain: "",
        image: "",
        username: ""
    });

    const [dataValidated, setDataValidted] = useState(false);
    const [errors, setErrors] = useState<{ [key: string]: string }>({});
    const [isCheckingUsername, setIsCheckingUsername] = useState(false);

    useEffect(() => {
        const savedData = Cookies.get(COOKIE_NAME);
        if (savedData) {
            try {
                const parsedData = JSON.parse(savedData);
                setFormData(prevData => ({
                    ...prevData,
                    ...parsedData
                }));
            } catch (error) {
                
            }
        }
    }, []);

    const isValidName = (name: string): boolean => name.trim().length > 0;
    const isValidDomain = (domain: string): boolean => /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(domain);
    const isValidUsername = (username: string): boolean => /^[a-z0-9]+$/.test(username) && username.trim().length > 0;

    useEffect(() => {
        const validateData = () => {
            // Destructure form data
            const { name, domain, username } = formData;

            // Validate each field
            const isNameValid = isValidName(name);
            const isDomainValid = isValidDomain(domain);
            const isUsernameValid = isValidUsername(username);

            // Add check for username error
            const hasUsernameError = errors.username !== undefined;

            // Return overall validity
            return (
                isNameValid &&
                isDomainValid &&
                isUsernameValid &&
                !hasUsernameError
            );
        };

        setDataValidted(validateData());
    }, [formData, errors]);

    const checkUsernameAvailability = async (username: string) => {
        if (!isValidUsername(username)) return;

        setIsCheckingUsername(true);
        try {
            const response = await checkOrganizationExists(username);
            
            if (response.exists) {
                setErrors(prev => {
                    const newErrors = {
                        ...prev,
                        username: "This username is already taken"
                    };
                    
                    return newErrors;
                });
                setDataValidted(false);
            } else {
                setErrors(prev => {
                    const newErrors = { ...prev };
                    delete newErrors.username;
                    
                    return newErrors;
                });
            }
        } catch (error) {
            
            setErrors(prev => ({
                ...prev,
                username: "Error checking username availability"
            }));
        } finally {
            setIsCheckingUsername(false);
        }
    };

    // Debounce the username check to avoid too many API calls
    const debouncedUsernameCheck = useCallback(
        debounce((username: string) => checkUsernameAvailability(username), 500),
        []
    );

    const checkFieldError = (field: keyof FormDataType) => {
        setErrors((prevErrors) => {
            const updatedErrors = { ...prevErrors };

            if (field == 'name') {
                if (!isValidName(formData['name'])) {
                    updatedErrors.name = "Display Name is required.";
                }
            }

            if (field == 'domain') {
                if (!isValidDomain(formData['domain'])) {
                    updatedErrors.domain = "Invalid or empty domain.";
                }
            }

            if (field == 'username') {
                if (!isValidUsername(formData['username'])) {
                    updatedErrors.username = "Username can only contain lowercase letters and numbers.";
                } else {
                    // Trigger immediate availability check on blur
                    checkUsernameAvailability(formData['username']);
                }
            }

            return updatedErrors;
        });
    }

    const removeError = (field: keyof FormDataType) => {
        setErrors((prevErrors) => {
            const updatedErrors = { ...prevErrors };
            delete updatedErrors[field];
            return updatedErrors;
        });
    }

    const handleInputChange = (field: keyof FormDataType, value: string) => {
        const newValue = field === 'username' ? value.toLowerCase() : value;

        setFormData(prevData => {
            const newData = {
                ...prevData,
                [field]: newValue
            };

            // Auto-generate username when display name changes
            if (field === 'name' && value.trim() !== '') {
                const generatedUsername = generateUniqueUsername(value);
                newData.username = generatedUsername;
                // Check availability for the generated username
                debouncedUsernameCheck(generatedUsername);
            }

            // If username field is being changed manually, check availability
            if (field === 'username' && newValue.length > 0) {
                debouncedUsernameCheck(newValue);
            }

            // Get existing cookie data
            const existingData = Cookies.get(COOKIE_NAME);
            const parsedExistingData = existingData ? JSON.parse(existingData) : {};

            // Merge with existing cookie data
            const updatedData = {
                ...parsedExistingData,
                ...newData
            };

            // Save to cookies
            Cookies.set(COOKIE_NAME, JSON.stringify(updatedData));

            return newData;
        });
    };

    const handleFileUpload = (imageUrl: string) => {
        handleInputChange('image', imageUrl);
    };

    const handleNext = () => {
        // Get existing cookie data
        const existingData = Cookies.get(COOKIE_NAME);
        const parsedExistingData = existingData ? JSON.parse(existingData) : {};

        // Merge current form data with existing data
        const updatedData = {
            ...parsedExistingData,
            ...formData
        };

        // Save to cookie
        Cookies.set(COOKIE_NAME, JSON.stringify(updatedData));

        // Navigate to next step
        handleTabClick("adminsetup");
    };

    const handleSaveAsDraft = () => {
        // Get existing cookie data
        const existingData = Cookies.get(COOKIE_NAME);
        const parsedExistingData = existingData ? JSON.parse(existingData) : {};

        // Merge current form data with existing data
        const updatedData = {
            ...parsedExistingData,
            ...formData
        };

        // Save to cookie
        Cookies.set(COOKIE_NAME, JSON.stringify(updatedData));
        alert('Progress saved successfully!');
    };

    return (
        <div className="w-full">
            <h2 className="project-panel-heading mb-2">Enter your organization information</h2>
            <form className="flex flex-wrap gap-5 w-full mb-5" onSubmit={(e) => e.preventDefault()}>
                <FormField
                    label="Display Name"
                    placeholder="Company Name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    error={errors.name}
                    onFocus={() => removeError("name")}
                    onBlur={() => checkFieldError("name")}
                />
                <FormField
                    label="Organization Id"
                    placeholder="Organization ID"
                    value={formData.username}
                    onChange={(e) => handleInputChange("username", e.target.value)}
                    error={errors.username}
                    onFocus={() => removeError("username")}
                    onBlur={() => checkFieldError("username")}
                    helperText="Auto-generated from Display Name. You can modify it if needed."
                />
                <DropDownMenuPropsUI
                    headLabel="Industry Type"
                    data={IndustryDropDownItems}
                    value={formData.industrial_type}
                    onChange={(value) => handleInputChange("industrial_type", value)}
                />
                <DropDownMenuPropsUI
                    headLabel="Company Size"
                    data={CompanySizeDropDownItems}
                    value={formData.company_size}
                    onChange={(value) => handleInputChange("company_size", value)}
                />
                <FormField
                    label="Business Email Domain"
                    placeholder="company.com"
                    value={formData.domain}
                    onChange={(e) => handleInputChange("domain", e.target.value)}
                    error={errors.domain}
                    onFocus={() => removeError("domain")}
                    onBlur={() => checkFieldError("domain")}
                />
            </form>

            <FileUploader
                onFileUpload={handleFileUpload}
                initialImage={formData.image}
            />

            <div className="flex mt-3 py-1 w-full">
                <div className="flex gap-4 ml-auto">
                    <DynamicButton
                        variant="ghostPrimary"
                        text="Save as Draft"
                        onClick={handleSaveAsDraft}
                    />
                    <Link href="/dashboard/organizations">
                        <DynamicButton
                            variant="primary"
                            text="Cancel"
                        />
                    </Link>
                    <DynamicButton
                        variant="primary"
                        text="Next: Admin Setup"
                        disabled={!dataValidated}
                        onClick={() => {
                            if (dataValidated) {
                                handleNext();
                            }
                        }}
                    />
                </div>
            </div>
        </div>
    );
};