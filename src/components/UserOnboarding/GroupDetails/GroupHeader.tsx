import { FC, useState } from 'react';
import { DynamicButton } from '@/components/UIComponents/Buttons/DynamicButton';
import { Pencil } from 'lucide-react';
import EditOrganization from "../Modal/EditOrganization";

interface GroupHeaderProps {
  orgId: string;
  groupId: string;
  groupDetails: GroupDetails | null;
  setGroupDetails: (details: GroupDetails | null) => void;
}

interface GroupDetails {
  name: string;
  group_type: string;
  description: string;
  status: string;
}

const GroupHeader: FC<GroupHeaderProps> = ({groupDetails, setGroupDetails }) => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  

  const handleSave = async (data: {
    name: string;
    email: string;
    isSuspended: boolean;
  }) => {
    try {
      setIsEditModalOpen(false);
    } catch (error) {
      
    }
  };

  if (!groupDetails) {
    return null;
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-1 mt-1">
        <div className="flex flex-col gap-2">
          <h1 className="typography-heading-2 font-weight-semibold">{groupDetails.name}</h1>
          <div className="flex items-center gap-4">
            <span className={`px-3 py-1 typography-body-sm rounded-full ${
              groupDetails.group_type === 'Department' ? 'bg-yellow-100 text-yellow-800' : 
              groupDetails.group_type === 'Administrative' ? 'bg-pink-100 text-pink-800' :
              'bg-purple-100 text-purple-800'
            }`}>
              {groupDetails.group_type}
            </span>
          </div>
        </div>
        <DynamicButton
          variant="secondary"
          icon={Pencil}
          text="Edit Group"
          disable={true}
        />
      </div>

      <EditOrganization
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        initialData={{
          name: groupDetails.name,
          email: '<EMAIL>',
          isSuspended: groupDetails.status !== 'active'
        }}
        onSave={handleSave}
      />
    </div>
  );
};

export default GroupHeader;