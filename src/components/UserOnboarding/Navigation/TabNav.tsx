// src/components/UserOnboarding/Navigation/TabNav.tsx
import { FC } from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';

interface TabNavProps {
  organizationId: string;
}

const TabNav: FC<TabNavProps> = ({ organizationId }) => {
  const pathname = usePathname();
  
  const tabs = [
    { 
      id: 'overview', 
      label: 'Overview', 
      path: `/dashboard/organizations/${organizationId}/overview` 
    },
    { 
      id: 'users', 
      label: 'Users', 
      path: `/dashboard/organizations/${organizationId}/users` 
    },
    { 
      id: 'features', 
      label: 'Features', 
      path: `/dashboard/organizations/${organizationId}/features` 
    },
    { 
      id: 'settings', 
      label: 'Settings', 
      path: `/dashboard/organizations/${organizationId}/settings` 
    },
    { 
      id: 'llmcost', 
      label: 'Cost Dashboard', 
      path: `/dashboard/organizations/${organizationId}/costs` 
    },
    // { 
    //   id: 'analytics', 
    //   label: 'Usage & Analytics', 
    //   path: `/dashboard/organizations/${organizationId}/analytics` 
    // }
  ];

  return (
    <div className="flex items-center ">
      {tabs.map((tab, index) => (
        <div key={tab.id} className="flex items-center">
          <Link
            href={tab.path}
            className={`
              py-1.5 px-1.5 typography-body-sm font-weight-medium transition-all
              ${pathname === tab.path || (pathname.split('/')[6] == 'costs' && tab.label=='Users')  //highlighting users tab if cost dashboard is open
                ? 'bg-white text-primary-600 shadow-md rounded-md border border-gray-200'
                : 'text-gray-600 hover:text-gray-800'}
            `}
          >
            {tab.label}
          </Link>
          {index < tabs.length - 1 && (
            <span className="mx-2 text-gray-300">|</span>
          )}
        </div>
      ))}
    </div>
  );
};

export default TabNav;