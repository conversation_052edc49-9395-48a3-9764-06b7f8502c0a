import React from "react";

interface ActionButton{
    label: string;
    onclick: () => void;
};

interface TableActionButtonProps{
    items: ActionButton[];
    onClose: () => void;
}

const TableActionButtons : React.FC<TableActionButtonProps> = ({items, onClose}) => {
    const handleClickOutside = (e: MouseEvent) => {
        if (!(e.target as Element).closest(".tableActionButtons")) {
          onClose();
        }
    };

    React.useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    return(
        <div className="fixed right-10 p-1 z-50 flex tableActionButtons flex-col rounded-md bg-white shadow-lg">
            {items.map ((item, index) => (
                <button key = {index} className="text-left px-3 py-2 font-weight-medium w-full text-[14px] hover:bg-gray-100" onClick={item.onclick}>
                    {item.label}
                </button>
            ))}
        </div>
    )
}

export default TableActionButtons;