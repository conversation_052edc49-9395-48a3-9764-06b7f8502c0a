// src/components/ui/Card.tsx
interface CardProps {
    title?: string;
    children: React.ReactNode;
    className?: string;
  }

  export const Card = ({ title, children, className = '' }: CardProps) => {
    return (
      <div className={`bg-white rounded-lg shadow-sm ${className}`}>
        {title && (
          <div className="px-4 py-3 border-b">
            <h3 className="typography-body-lg font-weight-medium text-gray-900">{title}</h3>
          </div>
        )}
        <div className="p-4">
          {children}
        </div>
      </div>
    );
  };