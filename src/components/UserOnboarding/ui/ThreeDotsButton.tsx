"use client";

import React, { useState } from "react";
import TableActionButtons from "./TableActionButtons";

export const ThreeDotsButton: React.FC<{
  onClick?: () => void;
  actionButtonItems: {label: string, onclick: () => void}[];
}> = ({ onClick, actionButtonItems }) => {

    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const openMenu = false; //remove this line after threedots menu functionalities are available

    const toggleOpen = () => {
        setIsMenuOpen(!isMenuOpen);
    }

    const toggleOpenThroughButtonClick = (event: React.MouseEvent) => {
        event.stopPropagation();
        setIsMenuOpen(!isMenuOpen);
    }

  return (
    <div className="relative">
      <button
        onClick={(toggleOpenThroughButtonClick)}
        className="p-1 hover:bg-gray-100 rounded-full"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="lucide lucide-ellipsis"
        >
          <circle cx="12" cy="12" r="1" />
          <circle cx="19" cy="12" r="1" />
          <circle cx="5" cy="12" r="1" />
        </svg>
      </button>


      {/*Remove the openMenu condition check after menu functionalities are available*/}
      {
        isMenuOpen && openMenu &&
        <TableActionButtons 
            items={actionButtonItems}
            onClose={toggleOpen}
        />
      }
    </div>
  );
};