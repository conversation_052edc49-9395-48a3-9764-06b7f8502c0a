'use client';

import React, { useState, useEffect, useContext } from "react";
import { Info } from "lucide-react";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { useRouter } from 'next/navigation';
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { Toggle } from "@/components/UserOnboarding/ui/Toggle";
import Cookies from 'js-cookie';
import { fetchOrganizationPlans } from '@/utils/api';
import FeatureConfigurationSkeleton from "@/components/Loaders/FeatureConfigurationSkeleton";
import Link from "next/link";
const COOKIE_NAME = 'org_details_form';
interface FeatureConfigurationProps {
  selectedPlan?: number;
  onBack: () => void;
  onNext: () => void;
  orgName?: string;
  initialFeatures?: APIFeatures;
}

interface APIFeatures {
  max_users: number;
  role_customization: boolean;
  api_access: boolean;
  github_integration: boolean;
  jira_integration: boolean;
  custom_reports: boolean;
  export_capabilities: boolean;
}

interface APIPlan {
  id: string;
  features: {
    workflow_templates: string;
    workflow_management: string;
    integration: string | boolean;
    workflow_analysis: boolean;
  };
}

interface OrganizationData {
  name: string;
  business_email: string;
  industrial_type: string;
  company_size: string;
  domain: string;
  image: string;
  plan_id: string;
  admin_name: string;
  admin_email: string;
  admin_contact_number: string;
  admin_department: string;
  configurations: APIFeatures;
}

interface FeatureSectionProps {
  title: string;
  description: string;
  features: APIFeatures;
  setFeatures: React.Dispatch<React.SetStateAction<APIFeatures>>;
  options: {
    left: { key: keyof APIFeatures; label: string; type: 'toggle' | 'number' }[];
    right: { key: keyof APIFeatures; label: string; type: 'toggle' | 'number' }[];
  };
}

const FeatureConfiguration: React.FC<FeatureConfigurationProps> = ({
  orgName,
  selectedPlan,
  initialFeatures,
  onBack,
  onNext,
}) => {
  const router = useRouter();
  const { showAlert } = useContext(AlertContext);
  const [enableAll, setEnableAll] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [validationMessage, setValidationMessage] = useState<string | null>(null);
  const [features, setFeatures] = useState<APIFeatures>({
    max_users: 2,
    role_customization: false,
    api_access: false,
    github_integration: false,
    jira_integration: false,
    custom_reports: false,
    export_capabilities: false
  });

  const validateOrganizationData = (data: OrganizationData): boolean => {
    const requiredFields = [
      'name',
      'business_email',
      'industrial_type',
      'company_size',
      'domain',
      'plan_id',
      'admin_name',
      'admin_email',
      'admin_contact_number',
      'admin_department'
    ];

    for (const field of requiredFields) {
      if (!data[field as keyof OrganizationData]) {
        setValidationMessage(`Please fill in all required fields`);
        return false;
      }
    }

    setValidationMessage(null);
    return true;
  };

  useEffect(() => {
    const loadFeatures = async () => {
      try {
        const cookieData = Cookies.get(COOKIE_NAME);
        if (!cookieData) {
          setValidationMessage('Please complete previous steps first');
          return;
        }
        // Use initialFeatures if provided
        if (initialFeatures) {
          setFeatures(initialFeatures);
          return;
        }


        const parsedCookieData = JSON.parse(cookieData);
        const planId = parsedCookieData?.plan_id;

        if (planId) {
          const plans = await fetchOrganizationPlans();
          const selectedPlan = plans.find((plan: APIPlan) => plan.id === planId);

          if (selectedPlan) {
            setFeatures({
              max_users: selectedPlan.features.workflow_templates === "unlimited" ? 999 : 2,
              role_customization: selectedPlan.features.workflow_management === "advanced",
              api_access: !!selectedPlan.features.integration,
              github_integration: selectedPlan.features.integration === "full",
              jira_integration: selectedPlan.features.integration === "full",
              custom_reports: selectedPlan.features.workflow_analysis,
              export_capabilities: selectedPlan.features.workflow_analysis
            });
          }
        }
      } catch (error) {
        
        setError('Failed to load features. Please try again.');
      } finally {
        setIsLoading(false);
      }
    };

    loadFeatures();
  }, []);

  const handleEnableAll = (enabled: boolean) => {
    setEnableAll(enabled);
    setFeatures(prev => ({
      ...prev,
      role_customization: enabled,
      api_access: enabled,
      github_integration: enabled,
      jira_integration: enabled,
      custom_reports: enabled,
      export_capabilities: enabled
    }));
  };

  const handleDisableOptional = (disabled: boolean) => {
    setEnableAll(!disabled);
    handleEnableAll(!disabled);
  };

  const handleSaveFeatures = () => {
    const cookieData = Cookies.get(COOKIE_NAME);
    if (cookieData) {
      const parsedData = JSON.parse(cookieData);
      const updatedData = {
        ...parsedData,
        configurations: features
      };
      Cookies.set(COOKIE_NAME, JSON.stringify(updatedData));
      showAlert('Progress saved successfully!', 'success');
    }
  };

  const handleReview = () => {
    onNext(); // Navigate to the next step, which should be the review step
  };

  if (isLoading) {
    return <FeatureConfigurationSkeleton />;
  }

  return (
    <div className="w-full max-h-[74vh] mx-1">
      <div className="flex flex-col ">
        {" "}
        {/* Adjusted height */}
        {/* Header */}
        <div className="mb-1 mx-1">
          <h2 className="project-panel-heading mb-2">Configure Features</h2>
          <div className="flex gap-8 mb-4">
            <div className="flex flex-col space-y-2">
              <span className="project-panel-content">
                Enable all available features
              </span>
              <div className="flex justify-start">
                <Toggle
                  enabled={enableAll}
                  onChange={handleEnableAll}
                  size="small"
                />
              </div>
            </div>
            <div className="flex flex-col space-y-2">
              <span className="project-panel-content">
                Disable optional features
              </span>
              <div className="flex justify-start">
                <Toggle
                  enabled={!enableAll}
                  onChange={handleDisableOptional}
                  size="small"
                />
              </div>
            </div>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto space-y-4">
          <FeatureSection
            title="User Management"
            description="Configure user-related features and permissions"
            features={features}
            setFeatures={setFeatures}
            options={{
              left: [{ key: 'max_users', label: 'Maximum users', type: 'number' }],
              right: [{ key: 'role_customization', label: 'Role customization', type: 'toggle' }]
            }}
          />

          <FeatureSection
            title="Integration Hub"
            description="Manage external service integrations"
            features={features}
            setFeatures={setFeatures}
            options={{
              left: [
                { key: 'api_access', label: 'API access', type: 'toggle' },
                { key: 'github_integration', label: 'Github', type: 'toggle' }
              ],
              right: [
                { key: 'jira_integration', label: 'Jira', type: 'toggle' }
              ]
            }}
          />

          <FeatureSection
            title="Analytics Dashboard"
            description="Configure analytics and reporting features"
            features={features}
            setFeatures={setFeatures}
            options={{
              left: [{ key: 'custom_reports', label: 'Custom reports', type: 'toggle' }],
              right: [{ key: 'export_capabilities', label: 'Export capabilities', type: 'toggle' }]
            }}
          />
        </div>

        <div className="flex justify-between py-4 mt-2">
          <DynamicButton
            variant="ghost"
            text="Back to Plan Selection"
            onClick={onBack}
            disabled={isLoading}
          />
          <div className="flex gap-4">
            <DynamicButton
              variant="ghostPrimary"
              text="Save as Draft"
              onClick={handleSaveFeatures}
              disabled={isLoading}
            />
            <Link href="/dashboard/organizations">
              <DynamicButton
                variant="primary"
                text="Cancel"
              />
            </Link>
            <DynamicButton
              variant="primary"
              text="Review and Confirm"
              onClick={handleReview}
              disabled={isLoading || !features.max_users || features.max_users < 1}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

const FeatureSection: React.FC<FeatureSectionProps> = ({
  title,
  description,
  features,
  setFeatures,
  options
}) => {
  const handleChange = (key: keyof APIFeatures, value: boolean | number) => {
    setFeatures(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-3 mx-1">
      <div className="flex items-center justify-between mb-1 bg-primary-50 rounded-md p-1.5">
        <div className="flex items-center gap-2">
          <h3 className="text-[#1C64F2] font-weight-semibold">{title}</h3>
          <div className="relative group">
            <Info
              className="w-4 h-4 text-gray-400 cursor-help"
              aria-label={description}
            />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-8">
        <div className="space-y-3">
          {options.left.map(option => (
            <div key={option.key} className="flex flex-col space-y-1">
              <span className="project-panel-content">{option.label}</span>
              <div className="flex justify-start">
                {option.type === 'number' ? (
                  <input
                    type="number"
                    className="w-full h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    value={features[option.key] as number}
                    onChange={(e) => handleChange(option.key, Number(e.target.value))}
                    min={1}
                  />
                ) : (
                  <Toggle
                    enabled={features[option.key] as boolean}
                    onChange={(enabled) => handleChange(option.key, enabled)}
                    size="small"
                  />
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="space-y-3">
          {options.right.map(option => (
            <div key={option.key} className="flex flex-col space-y-1">
              <span className="project-panel-content">{option.label}</span>
              <div className="flex justify-start">
                {option.type === 'number' ? (
                  <input
                    type="number"
                    className="w-full h-9 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    value={features[option.key] as number}
                    onChange={(e) => handleChange(option.key, Number(e.target.value))}
                    min={1}
                  />
                ) : (
                  <Toggle
                    enabled={features[option.key] as boolean}
                    onChange={(enabled) => handleChange(option.key, enabled)}
                    size="small"
                  />
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

const style = document.createElement('style');
style.textContent = `
  @keyframes loading-bar {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  .animate-loading-bar {
    animation: loading-bar 1.5s ease-in-out infinite;
  }
`;
document.head.appendChild(style);

export default FeatureConfiguration;