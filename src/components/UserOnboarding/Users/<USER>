"use client";

import React, { useMemo, useState, use<PERSON><PERSON>back, useEffect, useContext } from "react";
import TableComponent from "@/components/SimpleTable/table";
import Search from "@/components/BrowsePanel/TableComponents/Search";
import { Plus, Filter, Download, Trash2, MoreVertical, X, MinusCircle } from "lucide-react";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import AddNewUser from "@/components/UserOnboarding/Modal/AddNewUser";
import { fetchOrganizationUsers, deleteUser, promoteUserToAdmin, demoteUserFromAdmin } from "@/utils/api";
import Loader from "@/components/UserOnboarding/ui/Loader"
import { useUser } from "@/components/Context/UserContext";
import { UserCredModal } from "@/components/UIComponents/Modals/UserCredModal";
import { InfoIcon } from "lucide-react";
import DeleteConfirmationModal from "@/components/Modal/DeleteConfirmationModal";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";

interface User {
  id: string;
  name: string;
  email: string;
  contact_number: string;
  department: string;
  is_admin: boolean;
  status: string;
}

interface SuccessModalState {
  show: boolean;
  inviteUrl: string;
  loginUrl: string;
}

interface FilterState {
  status: string;
  department: string;
}

interface GroupFormState {
  firstName: string;
  lastName: string;
  workEmail: string;
  contactNumber: string;
  employeeId: string;
  designation: string;
  photo?: File;
}

const UserManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [users, setUsers] = useState<User[]>([]);
  const [rowsPerPage] = useState(10);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const { tenant_id } = useUser();
  const {showAlert} = useContext(AlertContext);
  const [filters, setFilters] = useState<FilterState>({
    status: "",
    department: ""
  });

  const [successModal, setSuccessModal] = useState<SuccessModalState>({
    show: false,
    inviteUrl: "",
    loginUrl: ""
  });

  const [deleteModalState, setDeleteModalState] = useState<{
    isOpen: boolean;
    userId: string | null;
    userName: string | null;
    isLoading: boolean;
  }>({
    isOpen: false,
    userId: null,
    userName: null,
    isLoading: false
  });

  const [actionModalState, setActionModalState] = useState<{
    isOpen: boolean;
    userId: string | null;
    userEmail: string | null;
    userName: string | null;
    isAdmin: boolean;
    position?: { top: number; left: number };
  }>({
    isOpen: false,
    userId: null,
    userEmail: null,
    userName: null,
    isAdmin: false
  });

  const [promoteModalState, setPromoteModalState] = useState<{
    isOpen: boolean;
    userId: string | null;
    userName: string | null;
    isLoading: boolean;
  }>({
    isOpen: false,
    userId: null,
    userName: null,
    isLoading: false
  });

  const [demoteModalState, setDemoteModalState] = useState<{
    isOpen: boolean;
    userId: string | null;
    userName: string | null;
    isLoading: boolean;
  }>({
    isOpen: false,
    userId: null,
    userName: null,
    isLoading: false
  });

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        const orgId = tenant_id;
        const response = await fetchOrganizationUsers(orgId);
        setUsers(response);
      } catch (error) {
        
      } finally {
        setIsLoading(false);
      }
    };

    fetchUsers();
  }, []);

  const handleCreateUser = async (formData: GroupFormState) => {
    try {
      const orgId = tenant_id;
      await fetchOrganizationUsers(orgId).then(response => {
        setUsers(response);
      });
    } catch (error) {
      
    }
    setIsModalOpen(false);
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  const handleFilterChange = (newFilters: FilterState) => {
    setFilters(newFilters);
  };

  const handleSuspend = useCallback(async (id: string) => {
    try {
      
    } catch (error) {
      
    }
  }, []);

  const handleDelete = useCallback(async (id: string) => {
    try {
      setDeleteModalState(prev => ({ ...prev, isLoading: true }));
      
      const result = await deleteUser(tenant_id, id);
      
      if (result.success) {
        showAlert(result.message || "User deleted successfully", "success");
        setUsers(prevUsers => prevUsers.filter(user => user.id !== id));
      } else {
        showAlert(result.message || "Failed to delete user", "danger");
      }
      
    } catch (error: any) {
      showAlert(error.message || "An error occurred while deleting the user", "danger");
    } finally {
      setDeleteModalState({ isOpen: false, userId: null, userName: null, isLoading: false });
    }
  }, [tenant_id]);

  const handlePromoteToAdmin = useCallback(async (userId: string) => {
    try {
      setPromoteModalState(prev => ({ ...prev, isLoading: true }));
      
      const result = await promoteUserToAdmin(tenant_id, userId);
      if (result) {
        showAlert("User promoted to admin successfully", "success");
        setUsers(prevUsers => prevUsers.map(user => 
          user.id === userId ? { ...user, is_admin: true } : user
        ));
      }
    } catch (error: any) {
      showAlert(error.message || "Failed to promote user to admin", "danger");
    } finally {
      setPromoteModalState({ isOpen: false, userId: null, userName: null, isLoading: false });
      setActionModalState({ isOpen: false, userId: null, userEmail: null, userName: null, isAdmin: false });
    }
  }, [tenant_id, showAlert]);

  const handleDemoteFromAdmin = useCallback(async (userId: string) => {
    try {
      setDemoteModalState(prev => ({ ...prev, isLoading: true }));
      
      const result = await demoteUserFromAdmin(tenant_id, userId);
      if (result) {
        showAlert("User demoted from admin successfully", "success");
        setUsers(prevUsers => prevUsers.map(user => 
          user.id === userId ? { ...user, is_admin: false } : user
        ));
      }
    } catch (error: any) {
      showAlert(error.message || "Failed to demote user from admin", "danger");
    } finally {
      setDemoteModalState({ isOpen: false, userId: null, userName: null, isLoading: false });
      setActionModalState({ isOpen: false, userId: null, userEmail: null, userName: null, isAdmin: false });
    }
  }, [tenant_id, showAlert]);

  const handleFilterClick = () => {
    setIsFilterOpen(!isFilterOpen);
  };

  const handleRowClick = (id: string) => {
    
  };

  const FilterDropdown: React.FC<{
    filters: FilterState;
    setFilters: (filters: FilterState) => void;
    onClose: () => void;
  }> = ({ filters, setFilters, onClose }) => {
    const handleClickOutside = (e: MouseEvent) => {
      if (!(e.target as Element).closest(".filter-dropdown")) {
        onClose();
      }
    };

    React.useEffect(() => {
      document.addEventListener("mousedown", handleClickOutside);
      return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    return (
      <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg z-50 py-2 filter-dropdown">
        <div className="px-4 py-2">
          <h3 className="typography-body-sm font-weight-medium text-gray-900">Status</h3>
          <div className="mt-2 space-y-2">
            {["active", "inactive"].map((status) => (
              <label key={status} className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.status === status}
                  onChange={() =>
                    setFilters({
                      ...filters,
                      status: status === filters.status ? "" : status,
                    })
                  }
                  className="rounded border-gray-300"
                />
                <span className="ml-2 typography-body-sm text-gray-700">{status}</span>
              </label>
            ))}
          </div>
        </div>

        <div className="px-4 py-2 border-t">
          <h3 className="typography-body-sm font-weight-medium text-gray-900">Department</h3>
          <div className="mt-2 space-y-2">
            {["IT", "Development"].map((dept) => (
              <label key={dept} className="flex items-center">
                <input
                  type="checkbox"
                  checked={filters.department === dept}
                  onChange={() =>
                    setFilters({
                      ...filters,
                      department: dept === filters.department ? "" : dept,
                    })
                  }
                  className="rounded border-gray-300"
                />
                <span className="ml-2 typography-body-sm text-gray-700">{dept}</span>
              </label>
            ))}
          </div>
        </div>
      </div>
    );
  };

  const filteredData = useMemo(() => {
    return users
      .filter(user => {
        const searchableValues = {
          name: user.name,
          email: user.email,
          contact_number: user.contact_number,
          department: user.department,
          status: user.status
        };

        const matchesSearch = Object.values(searchableValues)
          .join(' ')
          .toLowerCase()
          .includes(searchTerm.toLowerCase());

        const matchesStatus = !filters.status || user.status === filters.status;
        const matchesDepartment = !filters.department || user.department === filters.department;

        return matchesSearch && matchesStatus && matchesDepartment;
      })
      .map(user => ({
        id: user.id,
        name: user.name,
        email: user.email,
        contact: user.contact_number,
        department: user.department,
        role: (
          <span className={` py-1 rounded-md ${user.is_admin ? 'py-1 px-3 rounded-xl bg-primary-100 text-primary' : 'py-1 px-3 rounded-xl bg-gray-100 text-gray-600'}`}>
              {user.is_admin ? 'Admin' : 'Member'}
          </span>
        ),
        status: user.status,
        menuActions: (
          <div className="flex gap-2 relative p-1">
            <DynamicButton
              variant="secondary"
              icon={Download}
              text=""
              onClick={(e) => {
                e.stopPropagation();
                const setPasswordUrl = `${window.location.protocol}//${window.location.host}/users/set_password?tenant_id=${encodeURIComponent(tenant_id)}&email=${encodeURIComponent(user.email)}`;
                const loginUrl = `${window.location.protocol}//${window.location.host}/users/login?tenant_id=${encodeURIComponent(tenant_id)}&email=${encodeURIComponent(user.email)}`;
                setSuccessModal({ show: true, inviteUrl: setPasswordUrl, loginUrl: loginUrl });
              }}
            />
            <button
              className="p-2 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              onClick={(e) => {
                e.stopPropagation();
                if (actionModalState.isOpen && actionModalState.userId === user.id) {
                  setActionModalState({ isOpen: false, userId: null, userEmail: null, userName: null, isAdmin: false });
                } else {
                  const buttonRect = e.currentTarget.getBoundingClientRect();
                  setActionModalState({
                    isOpen: true,
                    userId: user.id,
                    userEmail: user.email,
                    userName: user.name,
                    isAdmin: user.is_admin,
                    position: {
                      top: buttonRect.top + (buttonRect.height / 2) + window.scrollY - 20,
                      left: buttonRect.left + window.scrollX - 192
                    }
                  });
                }
              }}
            >
              {actionModalState.isOpen && actionModalState.userId === user.id ? (
                <X className="w-5 h-5 text-gray-500" />
              ) : (
                <MoreVertical className="w-5 h-5 text-gray-500" />
              )}
            </button>
            {actionModalState.isOpen && actionModalState.userId === user.id && (
              <div className="fixed z-50 bg-white rounded-lg shadow-lg w-48"
                style={{
                  top: actionModalState.position?.top,
                  left: actionModalState.position?.left
                }}
              >
                <button
                  className="absolute -top-2 -right-2 p-1 bg-white rounded-full shadow-md hover:bg-gray-100"
                  onClick={() => setActionModalState({ isOpen: false, userId: null, userEmail: null, userName: null, isAdmin: false })}
                >
                  <X className="w-3 h-3 text-gray-500" />
                </button>
                <div className="p-2 space-y-1">
                  <button
                    className="w-full px-3 py-2 typography-body-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                    onClick={(e) => {
                      e.stopPropagation();
                      const setPasswordUrl = `${window.location.protocol}//${window.location.host}/users/set_password?tenant_id=${encodeURIComponent(tenant_id)}&email=${encodeURIComponent(actionModalState.userEmail!)}`;
                      const loginUrl = `${window.location.protocol}//${window.location.host}/users/login?tenant_id=${encodeURIComponent(tenant_id)}&email=${encodeURIComponent(actionModalState.userEmail!)}`;
                      setSuccessModal({ show: true, inviteUrl: setPasswordUrl, loginUrl: loginUrl });
                      setActionModalState({ isOpen: false, userId: null, userEmail: null, userName: null, isAdmin: false });
                    }}
                  >
                    <Download className="w-4 h-4 text-gray-500" />
                    <span>Download</span>
                  </button>
                  {!actionModalState.isAdmin && (
                    <button
                      className="w-full px-3 py-2 typography-body-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                      onClick={() => {
                        setPromoteModalState({ 
                          isOpen: true, 
                          userId: actionModalState.userId,
                          userName: actionModalState.userName,
                          isLoading: false
                        });
                        setActionModalState({ isOpen: false, userId: null, userEmail: null, userName: null, isAdmin: false });
                      }}
                    >
                      <svg 
                        className="w-4 h-4 text-primary" 
                        fill="none" 
                        stroke="currentColor" 
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>Promote to Admin</span>
                    </button>
                  )}
                  <button
                    className={`w-full px-3 py-2 typography-body-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2 ${
                      actionModalState.isAdmin ? 'opacity-50 cursor-not-allowed' : ''
                    }`}
                    onClick={() => {
                      if (!actionModalState.isAdmin) {
                        setDeleteModalState({ 
                          isOpen: true, 
                          userId: actionModalState.userId,
                          userName: actionModalState.userName,
                          isLoading: false
                        });
                        setActionModalState({ isOpen: false, userId: null, userEmail: null, userName: null, isAdmin: false });
                      }
                    }}
                    disabled={actionModalState.isAdmin}
                  >
                    <Trash2 className="w-4 h-4 text-red-500" />
                    <span>Delete User</span>
                  </button>
                  {actionModalState.isAdmin && (
                    <button
                      className="w-full px-3 py-2 typography-body-sm text-gray-700 hover:bg-gray-100 flex items-center gap-2"
                      onClick={() => {
                        setDemoteModalState({ 
                          isOpen: true, 
                          userId: actionModalState.userId,
                          userName: actionModalState.userName,
                          isLoading: false
                        });
                        setActionModalState({ isOpen: false, userId: null, userEmail: null, userName: null, isAdmin: false });
                      }}
                    >
                      <MinusCircle className="w-4 h-4 text-orange-500" />
                      <span>Demote Admin</span>
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>
        )
      }));
  }, [users, searchTerm, filters, handleSuspend, handleDelete, tenant_id, actionModalState]);

  const renderContent = () => {
    if (isLoading) {
      return (
        <Loader type="table" />
      );
    }

    if (filteredData.length === 0) {
      if (searchTerm) {
        return (
          <EmptyStateView
            type="noSearchResults"
            onClick={() => setSearchTerm("")}
          />
        );
      }
      if (filters.status || filters.department) {
        return (
          <EmptyStateView
            type="noFilterResults"
            onClick={() => setFilters({ status: "", department: "" })}
          />
        );
      }
      return (
        <EmptyStateView
          type="noData"
          onClick={() => setIsModalOpen(true)}
        />
      );
    }

    return (
      <TableComponent
        headers={[
          { key: "name", label: "Name" },
          { key: "email", label: "Email" },
          { key: "contact", label: "Contact" },
          { key: "department", label: "Department" },
          { key: "role", label: "Role" },
          { key: "status", label: "Status"},
          { key: "menuActions", label: "" },
        ]}
        data={filteredData}
        onRowClick={handleRowClick}
        sortableColumns={{
          name: true,
          email: true,
          contact: true,
          department: true,
          role: true,
        }}
        itemsPerPage={rowsPerPage}
        title="All Users"
      />
    );
  };

  return (
    <div className="bg-white rounded-lg shadow flex flex-col h-[81vh]">
      <div className="p-4 border-b flex-shrink-0">
        <h1 className="project-panel-heading mb-5">All Users</h1>
        <div className="flex justify-between items-center">
          <div>
            <Search searchTerm={searchTerm} setSearchTerm={handleSearch} />
          </div>
          <div className="flex gap-3">
            <DynamicButton
              variant="primary"
              icon={Plus}
              text="Add User"
              onClick={() => setIsModalOpen(true)}
            />
            <DynamicButton
              variant="secondary"
              icon={Filter}
              text="Filter"
              onClick={handleFilterClick}
            />
            <AddNewUser
              isOpen={isModalOpen}
              onClose={() => setIsModalOpen(false)}
              onSubmit={handleCreateUser}
            />
            {isFilterOpen && (
              <FilterDropdown
                filters={filters}
                setFilters={handleFilterChange}
                onClose={() => setIsFilterOpen(false)}
              />
            )}
          </div>
        </div>
      </div>

      <UserCredModal
        isOpen={successModal.show}
        title="User Credentials"
        message=""
        icon={InfoIcon}
        inviteUrl={successModal.inviteUrl}
        loginUrl={successModal.loginUrl}
        onClose={() => setSuccessModal({ show: false, 
          inviteUrl: `${window.location.protocol}//${window.location.host}/users/set_password?tenant_id=${encodeURIComponent(tenant_id)}` ,
          loginUrl: `${window.location.protocol}//${window.location.host}/users/login?tenant_id=${encodeURIComponent(tenant_id)}` })}
      />
      

      {renderContent()}

      <DeleteConfirmationModal
        isOpen={deleteModalState.isOpen}
        onClose={() => setDeleteModalState({ isOpen: false, userId: null, userName: null, isLoading: false })}
        onConfirm={() => deleteModalState.userId && handleDelete(deleteModalState.userId)}
        title="Delete User"
        message={`Are you sure you want to delete ${deleteModalState.userName}? This person will be removed from all groups. This action cannot be undone.`}
        isLoading={deleteModalState.isLoading}
      />

      <DeleteConfirmationModal
        isOpen={promoteModalState.isOpen}
        onClose={() => setPromoteModalState({ isOpen: false, userId: null, userName: null, isLoading: false })}
        onConfirm={() => promoteModalState.userId && handlePromoteToAdmin(promoteModalState.userId)}
        title="Promote to Admin"
        message={`Are you sure you want to promote ${promoteModalState.userName || 'this user'} to admin? This will give them full administrative privileges.`}
        isLoading={promoteModalState.isLoading}
        confirmText="Promote"
        confirmButtonClass="bg-primary-600 hover:bg-primary-700"
      />

      <DeleteConfirmationModal
        isOpen={demoteModalState.isOpen}
        onClose={() => setDemoteModalState({ isOpen: false, userId: null, userName: null, isLoading: false })}
        onConfirm={() => demoteModalState.userId && handleDemoteFromAdmin(demoteModalState.userId)}
        title="Demote from Admin"
        message={`Are you sure you want to demote ${demoteModalState.userName || 'this user'} from admin? This will remove their administrative privileges.`}
        isLoading={demoteModalState.isLoading}
        confirmText="Demote"
        confirmButtonClass="bg-orange-600 hover:bg-orange-700"
      />
    </div>
  );
};

export default UserManagement;