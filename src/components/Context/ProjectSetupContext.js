"use client"
import React, { createContext, useState } from "react";

const ProjectSetupContext = createContext();

const ProjectSetupProvider = ({ children }) => {
    const [projectSetupOpen, setProjectSetupOpen] = useState(false);
    const [projectId, setProjectId] = useState(null);
    const [openFlowModel, setOpenFlowModel] = useState(false);
    const [projectData, setProjectData] = useState([]);
    const [hasProcessingFiles, setHasProcessingFiles] = useState(false);
    const [containerId, setContainerId] = useState(null);
    const [ContainerData, setContainerData] = useState(null)
    const [ComponentData, setComponentData] = useState(null)
    const [functionalRequirements, setFunctionalRequirements] = useState(null)
    const [testCasesLoading, setTestCasesLoading] = useState(false);
    const [testCases, setTestCases] = useState([]);
    const [componentId, setComponentId] = useState(null);
    const [showConfigModel,setShowConfigModel] = useState(false)
    const [taskStatus,setTaskStatus] = useState([])
    const [selectedContainerIdVal,setSelectedContainerIdVal] = useState(null)
     const [isChecked,setIsChecked] = useState(false)

    // Design related states
    const [designId, setDesignId] = useState(null);
    const [designData, setDesignData] = useState(null);
    return (
        <ProjectSetupContext.Provider value={{
            ComponentData,
            setComponentData,
            functionalRequirements,
            setFunctionalRequirements,
            ContainerData,
            setContainerData,
            projectSetupOpen,
            setProjectSetupOpen,
            projectId,
            setProjectId,
            openFlowModel,
            setOpenFlowModel,
            projectData,
            setProjectData,
            hasProcessingFiles,
            setHasProcessingFiles,
            containerId,
            setContainerId,
            testCasesLoading,
            setTestCasesLoading,
            testCases,
            setTestCases,
            componentId,
            setComponentId,
            designId,
            setDesignId,
            designData,
            setDesignData,
            taskStatus,
            setTaskStatus,
            showConfigModel,
            setShowConfigModel,
            setSelectedContainerIdVal,
            selectedContainerIdVal,
            setIsChecked,
            isChecked
        }}>
            {children}
        </ProjectSetupContext.Provider>
    );
}

export { ProjectSetupProvider, ProjectSetupContext };