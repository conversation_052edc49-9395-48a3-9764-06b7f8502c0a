// RightContentContext.js
'use client'
import React, { createContext, useContext, useState } from 'react';

const RightContentContext = createContext();

export const RightContentProvider = ({ children }) => {
  const [rightContent, setRightContent] = useState({
    type: 'empty',
    data: []
  });

  return (
    <RightContentContext.Provider value={{ rightContent, setRightContent }}>
      {children}
    </RightContentContext.Provider>
  );
};

export const useRightContent = () => useContext(RightContentContext);