"use client"
import React, { createContext, useState, useContext, useRef } from "react";
import { ExecutionContext } from "./ExecutionContext";
import { AlertContext } from "../NotificationAlertService/AlertList";
import Cookies from "js-cookie";

const CodeGenerationExecutionContext = createContext();

const CodeGenerationExecutionProvider = ({ children }) => {
    const idToken = Cookies.get("idToken");
    const { currentTaskDetailsId, setActiveTask } = useContext(ExecutionContext);
    const [isConnected, setIsConnected] = useState(false);
    const { showAlert } = useContext(AlertContext);
    const abortControllerRef = useRef(null);
    return (
        <CodeGenerationExecutionContext.Provider value={{isConnected}}>
            {children}
        </CodeGenerationExecutionContext.Provider>
    );
}

const useCodeGenerationExecution = () => useContext(CodeGenerationExecutionContext);

export { CodeGenerationExecutionProvider, useCodeGenerationExecution };