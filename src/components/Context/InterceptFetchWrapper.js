'use client';

import { usePlanRestriction } from './PlanRestrictionContext';
import PremiumOverlay from '../PremiumOverlay';
import { useEffect, useState } from 'react';

const InterceptFetchWrapper = ({ children }) => {
    const { showPlanRestriction, setShowPlanRestriction, setCreditLimitCrossed } = usePlanRestriction();
    const [isUnauthorized, setIsUnauthorized] = useState(false);
    const [isCreditLimitExceeded, setIsCreditLimitExceeded] = useState(false);

    useEffect(() => {
        const originalFetch = window.fetch;

        window.fetch = async (url, options) => {
            try {
                const response = await originalFetch(url, options);

                if (response.status === 402) {
                    // Clone the response to read the body
                    const clonedResponse = response.clone();
                    try {
                        const data = await clonedResponse.json();
                        
                        if (data.detail === "You are not authorized to access this resource.") {
                            setIsUnauthorized(true);
                            setIsCreditLimitExceeded(false);
                        } else if (data.detail === "Free Credits Used Up.") {
                            setIsUnauthorized(false);
                            setIsCreditLimitExceeded(true);
                            setCreditLimitCrossed(true);
                        } else {
                            // Default case
                            setIsCreditLimitExceeded(true);
                            setCreditLimitCrossed(true);
                        }
                        
                        setShowPlanRestriction(true);
                    } catch (parseError) {
                        
                        // Default behavior for unparseable responses
                        setIsCreditLimitExceeded(true);
                        setCreditLimitCrossed(true);
                        setShowPlanRestriction(true);
                    }
                }

                return response;
            } catch (error) {
                

                if (url.includes('/auth/') || url.includes('/login')) {
                    return new Response(JSON.stringify({
                        error: true,
                        message: error.message || 'Network error occurred'
                    }), {
                        status: 500,
                        headers: { 'Content-Type': 'application/json' }
                    });
                }
                throw error;
            }
        };

        return () => {
            window.fetch = originalFetch;
        };
    }, [setShowPlanRestriction, setCreditLimitCrossed]);

    const handleClose = () => {
        setShowPlanRestriction(false);
        setIsUnauthorized(false);
        setIsCreditLimitExceeded(false);
    };

    return (
        <>
            {children}
            {showPlanRestriction && (
                <PremiumOverlay 
                    isCreditLimitExceeded={isCreditLimitExceeded} 
                    isUnauthorized={isUnauthorized} 
                    onClose={handleClose} 
                    allowClose={true} 
                />
            )}
        </>
    );
};

export default InterceptFetchWrapper;
