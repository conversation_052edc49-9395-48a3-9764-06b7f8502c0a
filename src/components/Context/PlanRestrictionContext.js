'use client';

import { createContext, useContext, useState } from 'react';

const PlanRestrictionContext = createContext();

export const PlanRestrictionProvider = ({ children }) => {
    const [creditLimitCrossed, setCreditLimitCrossed] = useState(false); 
    const [showPlanRestriction, setShowPlanRestriction] = useState(false);

    return (
        <PlanRestrictionContext.Provider value={{ showPlanRestriction, setShowPlanRestriction, creditLimitCrossed, setCreditLimitCrossed }}>
            {children}
        </PlanRestrictionContext.Provider>
    );
};

export const usePlanRestriction = () => useContext(PlanRestrictionContext);
