
"use client";

import React, { createContext, useState, useEffect } from 'react';
import { usePathname, useParams } from 'next/navigation';
import { fetchChildNodes } from '@/utils/api';

export const ArchitectureDetailContext = createContext();

export const ArchitectureProvider = ({ children }) => {
  const [architectureNodes, setArchitectureNodes] = useState([]);
  const pathname = usePathname();
  const params = useParams();
  useEffect(() => {
    const fetchNodesByUrl = async () => {
      const parts = pathname.split("/");
      const projectId = params.projectId;
      const pathSegment = parts[3];

      // Only fetch nodes if the URL contains a project ID and the 'architecture' path segment
      if (projectId && pathSegment === 'architecture') {
        try {
          const nodes = await fetchChildNodes(projectId, 'Project', 'Architecture');
          setArchitectureNodes(nodes);
        } catch (error) {
          
        }
      }
    };

    fetchNodesByUrl();
  }, [pathname]);

  return (
    <ArchitectureDetailContext.Provider value={{ architectureNodes, setArchitectureNodes }}>
      {children}
    </ArchitectureDetailContext.Provider>
  );
};
