// components/Context/SplitPanelContext.js
"use client";
import React, { createContext, useContext, useState } from "react";

const SplitPanelContext = createContext();

export const SplitPanelProvider = ({ children }) => {
  const [isPane1Collapsed, setIsPane1Collapsed] = useState(true);
  const [splitInstance, setSplitInstance] = useState(null);

  const togglePane1 = () => {
    if (splitInstance) {
      if (isPane1Collapsed) {
        splitInstance.setSizes([30, 70]);
      } else {
        splitInstance.setSizes([0, 100]);
      }
      setIsPane1Collapsed(!isPane1Collapsed);
    }
  };

  return (
    <SplitPanelContext.Provider value={{ togglePane1, isPane1Collapsed, setSplitInstance }}>
      {children}
    </SplitPanelContext.Provider>
  );
};

export const useSplitPanel = () => useContext(SplitPanelContext);
