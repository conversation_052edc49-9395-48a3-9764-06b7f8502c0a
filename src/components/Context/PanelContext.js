"use client";
import React, { createContext, useState } from "react";
import { fetchNodeById } from "../../utils/api";
const PanelContext = createContext();

const PanelProvider = ({ children }) => {
    const [projectDetails, setProjectDetails] = useState({});

    const getProject = async (projectId) => {
        if (!projectDetails[projectId]) {
            return await setProject(projectId);
        }
        return projectDetails[projectId];
    };

    const setProject = async (projectId) => {
        let details = await fetchNodeById(projectId, "Project");
                setProjectDetails((prevDetails) => ({
            ...prevDetails,
            [projectId]: details,
        }));
        return details;
    };

    const updateProject = (projectId, newDetails) => {
        setProjectDetails((prevDetails) => ({
            ...prevDetails,
            [projectId]: newDetails,
        }));
    };

    const updateProjectTitle = async (projectId, newTitle) => {
        const project = await getProject(projectId);
        const updatedProject = {
            ...project,
            properties: {
                ...project.properties,
                Title: newTitle,
            },
        };
        updateProject(projectId, updatedProject);
    };

    return (
        <PanelContext.Provider value={{ getProject, setProject, updateProject, setProjectDetails, updateProjectTitle}}>
            {children}
        </PanelContext.Provider>
    );
};

export { PanelProvider, PanelContext };
