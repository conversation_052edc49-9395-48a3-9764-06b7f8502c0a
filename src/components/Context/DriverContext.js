"use client";

import React, { createContext, useState, useRef } from 'react';
import { driver } from 'driver.js';
import Cookies from 'js-cookie';
import { usePathname } from 'next/navigation';
import { getRequiredSteps } from '@/constants/driver/driversteps';
import "driver.js/dist/driver.css";
import "@/styles/tourdriver.css";


const DriverContext = createContext();
/*This context is used to manage the state of the driver.js library, which is used for creating tours around the application. */

const DriverProvider = ({children}) => {
    const pathname = usePathname();
    const [currentStep, setCurrentStep] = useState(Number(Cookies.get('currentTourStep')) || 3);
    const [driverLoaded, setDriverLoaded] = useState(false);
    const tourDriver = useRef(null);
    //const [isDriverActive, setIsDriverActive] = useState(false);
    const redirected= useRef(false); //used to reset steps on the next driver iteration if it is being redirecred
    const isDriverActive = useRef(false);
    if (typeof window !== 'undefined' && window.sessionStorage) { //confirming its a client component
      isDriverActive.current = sessionStorage.getItem('isDriverActive') === 'true';
      sessionStorage.setItem('isDriverActive',isDriverActive.current);
    }
    

    const setRedirected = (value) => {
      redirected.current = value;
    }

    // Create driver directly instead of using useTour
    const createDriver = (steps) => {
      /*
        Creates an instance of the driver object provided by the driver.js library
      */
      const showButtons = steps.length == 1? ['next', 'close'] : ['previous', 'next', 'close'];
      return driver({
        steps,
        animate: true,
        allowClose: true,
        showButtons: showButtons,
        showProgress: false,
        smoothScroll: true,
        overlayClickBehavior: undefined,
        onNextClick: handleNextTourStep,
        onPrevClick: handlePrevTourStep,
        onCloseClick: handleClose,
        popoverClass: "driverjs-theme",
      });
    };

    const setIsDriverActive = (value) => {
      /* 
        An internal function to set the state of the driver. 
        Since the state of the driver is a react ref and not a state, this is a custom set fucntion that behaves as a setter function for react state 
      */
      isDriverActive.current = value;
      if (typeof window !== 'undefined' && window.sessionStorage) { //confirming its a client component
        sessionStorage.setItem('isDriverActive', value? 'true' : 'false');
      }
    };

    const handleDriveTour = (step = 0) => {
      /*
        This function is used to start the tour by calling the drive method of the driver instance.
        It also sets the current step in the state and in cookies.
        The step parameter is used to set the current step of the tour. This can be used to start the driver from a custom step count.
      */
      Cookies.set("currentTourStep", step.toString());
      setCurrentStep(step);
      if (tourDriver.current) {
        tourDriver.current.drive(step);
        setIsDriverActive(true);
      }
    };

    const handlePrevTourStep = () => {
      if (tourDriver.current) {
          if (tourDriver.current.isFirstStep()) {
              Cookies.set("currentTourStep", '0');
              setCurrentStep(0);
          } else {
              setCurrentStep((prevStep) => {
                  const newStep = Math.max(prevStep - 1, 0);
                  Cookies.set("currentTourStep", newStep.toString());
                  return newStep;
              });
              tourDriver.current.movePrevious();
          }
      }
  };
  
  const handleNextTourStep = () => {
      if (tourDriver.current) {
          if (tourDriver.current.isLastStep()) {
              Cookies.set("currentTourStep", '0');
              setCurrentStep(0);
              destroyDriver();
          } else {
              setCurrentStep((prevStep) => {
                  const newStep = prevStep + 1;
                  Cookies.set("currentTourStep", newStep.toString());
                  return newStep;
              });
              tourDriver.current.moveNext();
          }
      }
  };

  const handleClose = () => {
    if(tourDriver.current){
      destroyDriver();
      setIsDriverActive(false);
    }
  }
  
  const destroyDriver = () => {
    if (tourDriver.current) {
      tourDriver.current.destroy();
    }
    tourDriver.current = null;
    setIsDriverActive(false);
    setDriverLoaded(false);
  };

  const createCustomScreen = (fetchedData) => {
    const customScreen = (
      <div className='customScreenClass' id = 'customScreen'>
        {fetchedData}
      </div>
    );

    return customScreen;
  }

  const prepareDriver = (pagename, instantRedirect = false) => {
    const steps = getRequiredSteps(pagename);
    Cookies.set("currentTourStep", '0')
    setCurrentStep(0);
    tourDriver.current = createDriver(steps);
    if(isDriverActive.current === true){
      if(redirected.current){
        handleDriveTour(0);
        setRedirected(false);
      }
      else{
        if(!instantRedirect){
          handleDriveTour(currentStep);
        }
      }
    }
    setDriverLoaded(true);

    if(!redirected.current && isDriverActive.current && instantRedirect){
      handleDriveTour(currentStep);
    }

    return(() => {
      clearTimeout(timeout);
    })
  }

  const prepareTempDriver = (pagename, isModal=true) => {
    if(tourDriver.current){
      tourDriver.current.destroy();
    }
    const tempSteps = getRequiredSteps(pagename);

    let showButtons = tempSteps.length == 1? ['next', 'close'] : ['previous', 'next', 'close'];

    const tempDriver = driver({
      steps: tempSteps,
      showButtons: showButtons,
      overlayClickBehavior: undefined,
      popoverClass: "driverjs-theme",
      overlayOpacity: isModal? 0.5 : 0
    });

    if(isDriverActive.current === true){
      tempDriver.drive(0);
    }
  }

  /*const asyncDriver = (fetchfunction, title, message) => {
    const newDriver = driver({
      steps: [
      {
        popover: {
          title: 'Loading Data',
          description: 'The next step presents you with loaded data.',
          
          onNextClick: async () => {
            const fetchedData = await fetchfunction();
            const customScreen = createCustomScreen(fetchedData);
            ReactDOM.createPortal(customScreen, document.body);
            newDriver.moveNext();
          }
        }
      },
      {
        element: "#custom-screen",
        popover: {
          title: title,
          description: message,
          
          OnDeselected: () => {
            if (typeof window !== undefined){
              document.querySelector('#custom-screen')?.remove(); 
            }
          }
        }
      }
    ]
    })
  }*/

  const highlightElement = ({element, title, message}) => {
    if(!tourDriver.current){
      tourDriver.current = driver();
    }
    tourDriver.current.highlight({
      element: element,
      popover: {
        title: title,
        description: message
      }
    });
  }

    return (
        <DriverContext.Provider value={{
            destroyDriver,
            currentStep,
            setCurrentStep,
            handleDriveTour,
            driverLoaded,
            setDriverLoaded,
            prepareDriver,
            isDriverActive: isDriverActive.current,
            prepareTempDriver,
            highlightElement,
            setRedirected
        }}>
            {children}
        </DriverContext.Provider>
    );
};

export { DriverProvider, DriverContext };