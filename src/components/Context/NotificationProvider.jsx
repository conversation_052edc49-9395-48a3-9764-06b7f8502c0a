"use client"

import { useState, useEffect, useContext, createContext } from 'react';
import { getToken } from 'firebase/messaging';
import { messaging } from '@/lib/firbase-config';
import { registerDevice } from '@/utils/notificationAPI';
import { setCookie } from '@/utils/auth';
import { AlertContext } from '@/components/NotificationAlertService/AlertList';
import Cookies from 'js-cookie';

const VAPID_KEY = process.env.NEXT_PUBLIC_VAPID_KEY;
const NotificationContext = createContext();

export const NotificationProvider = ({ children }) => {
    const [isLoading, setIsLoading] = useState(true);
    const [notifications, setNotifications] = useState([]);
    const [permissionStatus, setPermissionStatus] = useState('default');
    const [fcmToken, setFcmToken] = useState(null);
    const { showAlert } = useContext(AlertContext);

    const registerServiceWorker = async () => {
        try {
            if (!('serviceWorker' in navigator)) {
                throw new Error('Service Worker not supported');
            }

 

            const registration = await navigator.serviceWorker.register(
                `/firebase-messaging-sw.js?v=${Date.now()}`,
                {
                    scope: '/',
                    updateViaCache: 'none'
                }
            );

            await navigator.serviceWorker.ready;
            
            return registration;
        } catch (error) {
            
            throw error;
        }
    };

    const getFCMToken = async () => {
        try {
            if (!messaging) {
                throw new Error('Firebase messaging not initialized');
            }

       
            // Generate new token
            const token = await getToken(messaging, {
                vapidKey: VAPID_KEY,
                serviceWorkerRegistration: await navigator.serviceWorker.getRegistration()
            });

            if (!token) {
                throw new Error('Failed to generate FCM token');
            }

            
            return token;
        } catch (error) {
            
            throw error;
        }
    };

    const handleNotificationRegistration = async (token) => {
        try {
            const deviceInfo = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                screenResolution: `${window.screen.width}x${window.screen.height}`
            };

            await registerDevice(
                token,
                'web',
                process.env.NEXT_PUBLIC_APP_VERSION,
                deviceInfo
            );

            
            return true;
        } catch (error) {
            
            return false;
        }
    };

    const handleNotification = async (event) => {
        try {
            const payload = event.data?.payload || event.data;
            if (!payload) return;

            if (event.data?.messageType === 'push-received') {
                new Notification(payload.data["pinpoint.notification.title"], {
                    body: payload.data["pinpoint.notification.body"],
                    icon: '/logo/kavia_light_logo.svg',
                    badge: '/logo/kavia_light_logo.svg',
                    tag: 'notification-' + Date.now(),
                    data: payload.data
                });
            }

            setNotifications(prev => [payload, ...prev]);
            // showAlert(`${payload.data["pinpoint.notification.title"]} - ${payload.data["pinpoint.notification.body"]}`, "info");

            if (payload.data.link) {
                setCookie('notification_redirect', payload.data.link);
            }
        } catch (error) {
            
        }
    };

    const checkPermission = async () => {
        try {
            // Check for idToken first
            const idToken = Cookies.get('idToken');
            if (!idToken) {
                
                setIsLoading(false);
                return;
            }

            if (typeof window === 'undefined' || !('Notification' in window)) {
                showAlert('Notifications are not supported in this browser', 'error');
                return;
            }

            await registerServiceWorker();

            const currentPermission = Notification.permission;
            setPermissionStatus(currentPermission);

            let status = currentPermission;
            if (currentPermission === 'default') {
                status = await Notification.requestPermission();
                setPermissionStatus(status);
            }

            if (currentPermission === 'granted' || status === 'granted') {
                const token = await getFCMToken();
                if (token) {
                    if (token !== Cookies.get('fcm_token')) {
                        setFcmToken(token);
                        await setCookie('fcm_token', token);
                        const registered = await handleNotificationRegistration(token);
                        
                        if (registered) {
                            // showAlert('Notifications enabled successfully', 'success');
                        } else {
                            // showAlert('Error registering device for notifications', 'error');
                        }
                    } else {
                        setFcmToken(token);
                    }
                }
            } 
        } catch (error) {
            
            // showAlert('Error setting up notifications', 'error');
        } finally {
            setIsLoading(false);
        }
    };

    // Initial setup
    useEffect(() => {
        checkPermission();
    }, []);

    // Service worker message listener
    useEffect(() => {
        if (navigator.serviceWorker) {
            navigator.serviceWorker.addEventListener('message', handleNotification);
            return () => {
                navigator.serviceWorker.removeEventListener('message', handleNotification);
            };
        }
    }, []);

    // Token refresh handler
    useEffect(() => {
        if (messaging) {
            // Set up token refresh monitoring
            const tokenRefreshInterval = setInterval(async () => {
                try {
                    // Force token refresh
                    const currentToken = await getToken(messaging, { 
                        vapidKey: VAPID_KEY,
                        forceRefresh: true
                    });
                    
                    if (currentToken && currentToken !== fcmToken) {
                        
                        setFcmToken(currentToken);
                        await setCookie('fcm_token', currentToken);
                        await handleNotificationRegistration(currentToken);
                    }
                } catch (error) {
                    
                }
            }, 60 * 60 * 1000); // Check every hour
    
            return () => {
                clearInterval(tokenRefreshInterval);
            };
        }
    }, [fcmToken]);

    return (
        <NotificationContext.Provider
            value={{
                isLoading,
                permissionStatus,
                fcmToken,
                requestPermission: checkPermission,
                handleNotification,
                notifications,
                setNotifications
            }}
        >
            {children}
        </NotificationContext.Provider>
    );
};

export const useNotifications = () => {
    return useContext(NotificationContext);
};