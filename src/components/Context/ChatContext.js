'use client'
import React, { createContext, useState, useEffect } from "react";
import { fetchChatHistory } from '../../utils/api';
import { useSearchParams } from 'next/navigation';

const ChatContext = createContext();
// const InitialMessage = "Welcome to Ka<PERSON>'s AI-powered chat interface. Type your message here to begin a conversation about your projects, source code, or any other development-related questions. Our AI is ready to assist you with information, explanations, and insights to help streamline your workflow";
const InitialMessage = "Hello! How can I assist you with your projects or source code today?"
const ChatProvider = ({ children }) => {
  const [messages, setMessages] = useState([
    { id: 1, text: InitialMessage, sender: 'AI' }
  ]);
  const [isLoading, setIsLoading] = useState(true);
  const [discussionId, setDiscussionId] = useState(null);
  const searchParams = useSearchParams();

  useEffect(() => {
    const urlDiscussionId = searchParams.get('discussion_id');
    setDiscussionId(urlDiscussionId);

    if (!urlDiscussionId) {
      // Reset messages if no discussion ID is found
      setMessages([{ id: 1, text: InitialMessage, sender: 'AI' }]);
      setIsLoading(false);
    } else {
      // Fetch messages for the given discussion ID
      fetchMessages(urlDiscussionId);
    }
  }, [searchParams]);

  const fetchMessages = async (id) => {
    setIsLoading(true);
    try {
      const fetchedMessages = await fetchChatHistory(parseInt(id, 10));
      // setMessages(fetchedMessages);
      setMessages([
        { id: 1, text: InitialMessage, sender: 'AI' },
        ...fetchedMessages,
      ]);
    } catch (error) {
      
      // setMessages([{ id: 1, text: 'Failed to load messages. Please try again.', sender: 'AI' }]);
      setMessages([
        { id: 1, text: 'Failed to load messages. Please try again.', sender: 'AI' },
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const addMessage = (message) => {
    setMessages(prevMessages => [...prevMessages, message]);
    
  };

  const removeLastMessage = () => {
    setMessages(prevMessages => prevMessages.slice(0, -1));
  };

  return (
    <ChatContext.Provider value={{ 
      messages, 
      addMessage, 
      removeLastMessage, 
      discussionId,
      isLoading
    }}>
      {children}
    </ChatContext.Provider>
  );
}

export { ChatProvider, ChatContext };
