
import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import { useBuildProgress } from './BuildProgressContext';
import { AlertContext } from '../NotificationAlertService/AlertList';

const WebSocketContext = createContext(null);

export const WebSocketProvider = ({ children }) => {
  const [connections, setConnections] = useState(new Map());
  const [documentProgressMap, setDocumentProgressMap] = useState({});
  const progressMapRef = useRef({});
  const { updateBuildProgress, updateBuildStatus, initiateBuildStatus } = useBuildProgress();
  const [refreshRepos, setRefreshRepos] = useState(false);
  const { showAlert } = useContext(AlertContext);
  const [files, setFiles] = useState(null);
  const [isCompleted, setIsCompleted] = useState(true);
  const [fileContent, setFileContent] = useState({});  // Store file content by name
  const [streamingFiles, setStreamingFiles] = useState({}); // Track which files are currently streaming
  const connectToSession = (sessionId) => {
    if (!sessionId || connections.has(sessionId)) return;


    const ws = new WebSocket(`${process.env.NEXT_PUBLIC_WS_URL}/${sessionId}`);

    ws.onopen = () => {

      ws.send(JSON.stringify({
        type: 'client',
        task_id: sessionId,
      }));
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        if (data.type === 'code_ingestion' && data.data?.info  == "Build_Status_Update"){
          updateBuildStatus(data.data.buildId, data.data.message, data.data.status);
        }

        if (data.type === 'code_ingestion' && data.data?.progress_by_codebase) {
          Object.entries(data.data.progress_by_codebase).forEach(([buildId, buildData]) => {
            updateBuildProgress(data.task_id, buildId, buildData, data.data.estimated_time_remaining);
          });
        }

        if (data.type === 'code_ingestion' && data.data?.info == "Manifest_Repository_Update") {
          showAlert(data.data.message, "info");
          setRefreshRepos(true);
        }

        if (data.type === 'code_ingestion' && data.data?.info == "Repo_Data_Update") {
          setRefreshRepos(true);
        }

        if (data.type === 'document_processing') {


          // Extract file_uuid from the nested data object
          const fileUuid = data.data?.file_uuid;

          if (!fileUuid) {
            
            return;
          }



          // Create progress data object using the nested structure
          const progressData = {
            fileId: fileUuid,
            progress: data.data?.progress || 0,
            status: data.data?.status || "processing",
            metrics: data.data?.metrics ? {
              progressPercent: data.data.metrics.progress_percent,
              elapsedTime: data.data.metrics.elapsed_time,
              estimatedRemainingTime: data.data.metrics.estimated_remaining_time,
              criteriaProcessed: data.data.metrics.criteria_processed,
              totalCriteria: data.data.metrics.total_criteria,
              avgTimePerCriteria: data.data.metrics.avg_time_per_criteria
            } : null,
            message: data.data?.message || ""
          };

          // Update ref for immediate access
          progressMapRef.current = {
            ...progressMapRef.current,
            [fileUuid]: progressData
          };

          // Force a new object creation to ensure React state update
          setDocumentProgressMap(prev => {
            const newMap = {...prev};
            newMap[fileUuid] = progressData;

            return newMap;
          });
        }
      } catch (error) {
        
      }
    };

    ws.onerror = (error) => {
      
    };

    ws.onclose = () => {

      connections.delete(sessionId);
      setConnections(new Map(connections));
    };

    setConnections(new Map(connections.set(sessionId, ws)));
  };

  const disconnectFromSession = (sessionId) => {
    const ws = connections.get(sessionId);
    if (ws) {
      ws.close();
      connections.delete(sessionId);
      setConnections(new Map(connections));
    }
  };

  const getConnection = (sessionId) => {
    return connections.get(sessionId);
  };

  // Get document progress for a specific file UUID
  const getDocumentProgress = (fileUuid) => {
    // Use ref for immediate access, fallback to state
    return progressMapRef.current[fileUuid] || documentProgressMap[fileUuid];
  };

  // Get all document progress data
  const getAllDocumentProgress = () => {
    return documentProgressMap;
  };

  // Update file content directly from tool stream
  const updateFileContent = (fileName, content, isComplete = false) => {
    // Mark file as streaming
    if (!isComplete) {
      setStreamingFiles(prev => ({
        ...prev,
        [fileName]: true
      }));
    }

    setFileContent(prev => {
      const newContent = { ...prev, [fileName]: content };

      // Also update the files object for Sandpack if it exists
      if (files) {
        setFiles(prevFiles => ({
          ...prevFiles,
          [fileName]: content
        }));
      }

      return newContent;
    });

    // If the file is complete, mark it as not streaming
    if (isComplete) {
      setTimeout(() => {
        setStreamingFiles(prev => {
          const newState = { ...prev };
          delete newState[fileName];
          return newState;
        });
      }, 500); // Small delay to ensure the UI updates
    }
  };

  useEffect(() => {
    return () => {
      connections.forEach((ws) => ws.close());
    };
  }, []);

  return (
    <WebSocketContext.Provider value={{
      connectToSession,
      disconnectFromSession,
      getConnection,
      getDocumentProgress,
      getAllDocumentProgress,
      refreshRepos,
      setRefreshRepos,
      documentProgressMap, // Expose the state directly
      setFiles,
      files,
      isCompleted,
      setIsCompleted,
      fileContent,
      updateFileContent,
      streamingFiles
    }}>
      {children}
    </WebSocketContext.Provider>
  );
};

export const useWebSocket = () => {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};