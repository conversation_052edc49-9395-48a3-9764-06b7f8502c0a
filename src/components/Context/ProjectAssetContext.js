// ProjectAssetContext.js

"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";

import { useBuildProgress } from "./BuildProgressContext";

export const ProjectAssetContext = createContext();

export const ProjectAssetProvider = ({ children }) => {
  const [wsConnection, setWsConnection] = useState(null);
  const [wsStatus, setWsStatus] = useState("disconnected");
  const [reconnectAttempt, setReconnectAttempt] = useState(0);
  const [currentSessionId, setCurrentSessionId] = useState(null);
  const [activeTab ,setActiveTab] = useState("code")
  const [progressByRepo, setProgressByRepo] = useState({});
  const [rawWebSocketMessage, setRawWebSocketMessage] = useState(null);
   const [repositoryDetails,setRepositoryDetails] = useState([])
  const [scanningRepoId, setScanningRepoId] = useState(null); 
  const maxReconnectAttempts = 5;
  const { updateBuildProgress, updateBuildStatus } = useBuildProgress();
  
  const updateSessionId = useCallback((sessionId) => {
    
    setCurrentSessionId(sessionId);
    if (sessionId) {
      // alert("ws-connection")
      connectWebSocket(sessionId);
    }
  }, []);

  const connectWebSocket = useCallback(
    (sessionId) => {
      if (!sessionId) {
        
        return;
      }

      
      setWsStatus("connecting");

      // Create WebSocket connection
      const ws = new WebSocket(
        `${process.env.NEXT_PUBLIC_WS_URL}/${sessionId}`
      );

      ws.onopen = () => {
        
        setWsStatus("connected");
        setReconnectAttempt(0);

        // Send initial message
        const message = {
          type: "client",
          task_id: sessionId,
        };
        
        ws.send(JSON.stringify(message));
      };

      ws.onmessage = (event) => {
        
        try {
          const data = JSON.parse(event.data);
          setRawWebSocketMessage(data)
          

          if (data.type === "code_ingestion") {
            updateWebSocketMessage(data);
          }

          if (data.type === 'code_ingestion' && data.data?.info  == "Build_Status_Update"){
            updateBuildStatus(data.data.buildId, data.data.message, data.data.status);
          }

          if (data.type === 'code_ingestion' && data.data?.progress_by_codebase) {
            // Extract build IDs and their progress from progress_by_codebase
            Object.entries(data.data.progress_by_codebase).forEach(([buildId, buildData]) => {          
              updateBuildProgress(data.task_id, buildId, buildData, data.data.estimated_time_remaining);
            });
          }
        } catch (error) {
          
        }
      };

      ws.onerror = (error) => {
        
        setWsStatus("error");
      };

      ws.onclose = (event) => {
        
        setWsStatus("disconnected");

        // Implement reconnection logic
        if (reconnectAttempt < maxReconnectAttempts) {
          const delay = 1000 * Math.min(reconnectAttempt + 1, 30);
          
          setTimeout(() => {
            setReconnectAttempt((prev) => prev + 1);
            connectWebSocket(sessionId);
          }, delay);
        }
      };

      setWsConnection(ws);

      // Cleanup function
      return () => {
        if (ws) {
          
          ws.close();
          setWsConnection(null);
        }
      };
    },
    [reconnectAttempt, maxReconnectAttempts]
  );

  const updateWebSocketMessage = useCallback((data) => {
    if (data.data) {
      const {
        repo_id,
        files_processed,
        total_files,
        percentage_complete,
        estimated_time_remaining,
      } = data.data;
      if (repo_id) {
        setProgressByRepo((prev) => ({
          ...prev,
          [repo_id]: {
            files_processed: files_processed || 0,
            total_files: total_files || 0,
            percentage_complete: percentage_complete || 0,
            estimated_time_remaining: estimated_time_remaining || null,
          },
        }));
      }
    }
  }, []);

  // Add monitoring effect
  useEffect(() => {
    
  }, [wsStatus]);

  // Add monitoring effect for progress
  useEffect(() => {
    
  }, [progressByRepo]);

  return (
    <ProjectAssetContext.Provider
      value={{
        wsStatus,
        progressByRepo,
        connectWebSocket,
        updateSessionId,
        currentSessionId,
        rawWebSocketMessage,
        setActiveTab,
        setRepositoryDetails,
        repositoryDetails,
        setScanningRepoId,
        scanningRepoId,
        activeTab
      }}
    >
      {children}
    </ProjectAssetContext.Provider>
  );
};

export const useProjectAsset = () => {
  const context = useContext(ProjectAssetContext);
  if (!context) {
    throw new Error(
      "useProjectAsset must be used within a ProjectAssetProvider"
    );
  }
  return context;
};
