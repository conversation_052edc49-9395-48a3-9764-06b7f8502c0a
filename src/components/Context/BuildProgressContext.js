import React, { createContext, useState, useContext, useRef } from 'react';

// Create the context
const BuildProgressContext = createContext();

// Create the provider component
export const BuildProgressProvider = ({ children }) => {
  const [buildProgress, setBuildProgress] = useState({});
  const buildStatusRef = useRef({});
  const [buildStatus, setBuildStatus] = useState({});

  //Update build Status for a spefic build
  const updateBuildStatus = (buildId, statusMessage, status) => {
    let newStatus = buildStatusRef.current[buildId];
    if(newStatus && newStatus.some((item) => item.message === statusMessage)){
      if(status == "Success"){
        let statusUpdated = false;
        newStatus = newStatus.map((item) => {
          if(item.message == statusMessage){
            statusUpdated = true;
            return {...item, status};
          }
          else {
            return statusUpdated ? item : {...item, status}
          }
        })
      }
      else{
        newStatus = newStatus.map((item) => 
          item.message === statusMessage? {...item, status} : item
        )
      }
    }
    else {
      if(!newStatus) {
        newStatus = [{"message": statusMessage, "status": status}];
      }
      else{
        newStatus = [...newStatus, {"message": statusMessage, "status": status}];
      }
    }

    setBuildStatus(prev => ({
      ...prev,
      [buildId]: newStatus
    }));

    buildStatusRef.current = {
      ...buildStatusRef,
      [buildId]: newStatus
    }
  };

  const initiateBuildStatus = (buildIds, type="clone") => {
    const newStatus = {};
    buildIds.forEach(buildId => {
      newStatus[buildId] = [
        { message: type == "ingest"? "Copying the files" : type == "clone"? "Cloning the repository" : "Setting up the repository", status: "Processing" },
        { message: `Starting ${type == "upstream"  ? "Re-build": "Build"} Process`, status: "Processing" },
        { message: "Fetching Progress Data", status: "Processing" }
      ];
    });

    setBuildStatus(prev => ({
      ...prev,
      ...newStatus
    }));

    buildStatusRef.current = {
      ...buildStatus,
      ...newStatus
    }
  }

  

  // Update progress for a specific build
  const updateBuildProgress = ( buildSessionId, buildId, progressData, estimated_time_remaining) => {
    setBuildProgress(prev => ({
      ...prev,
      [buildId]: {
        totalFiles: progressData.total_files,
        filesReady: progressData.files_ready,
        filesProcessed: progressData.files_processed,
        percentageComplete: progressData.percentage_complete,
        status: progressData.status || 'in_progress',
        estimatedTime: estimated_time_remaining,
        buildSessionId: buildSessionId
      }
    }));
  };

  // Clear progress for a specific build
  const clearBuildProgress = (buildId) => {
    setBuildProgress(prev => {
      const newProgress = { ...prev };
      delete newProgress[buildId];
      return newProgress;
    });

    setBuildStatus(prev => {
      const newStatuses = { ...prev };
      delete newStatuses[buildId];
      return newStatuses;
    });
  };

  // Get progress for a specific build
  const getBuildProgress = (buildId) => {
    return buildProgress[buildId];
  };

  return (
    <BuildProgressContext.Provider value={{
      buildProgress,
      updateBuildProgress,
      buildStatus,
      setBuildStatus,
      updateBuildStatus,
      clearBuildProgress,
      getBuildProgress,
      initiateBuildStatus
    }}>
      {children}
    </BuildProgressContext.Provider>
  );
};

// Custom hook for using the context
export const useBuildProgress = () => {
  const context = useContext(BuildProgressContext);
  if (!context) {
    throw new Error('useBuildProgress must be used within a BuildProgressProvider');
  }
  return context;
};