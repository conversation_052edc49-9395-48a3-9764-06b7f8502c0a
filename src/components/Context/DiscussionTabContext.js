// contexts/DiscussionTabContext.js
'use client'
import { createContext, useState } from 'react';
import { getComponentDeployments } from '@/utils/api';

export const DiscussionTabContext = createContext();




export function DiscussionTabProvider({ children }) {
  const [overviewData, setOverviewData] = useState(null);
  const [deploymentId, setDeploymentId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchOverviewData = async (projectId, containerId) => {
    setIsLoading(true);
    try {
      const data = await getComponentDeployments(projectId, containerId);
      setOverviewData(data);
      // Set the deploymentId from the first deployment in the array
      if (data.deployments && data.deployments.length > 0) {
        setDeploymentId(data.deployments[0].id);
      }
    } catch (err) {
      setError(err.message);
    }
    setIsLoading(false);
  };

  return (
    <DiscussionTabContext.Provider value={{
      overviewData,
      deploymentId,
      isLoading,
      error,
      fetchOverviewData,
      setDeploymentId // Also expose the setter in case you need to update it elsewhere
    }}>
      {children}
    </DiscussionTabContext.Provider>
  );
}