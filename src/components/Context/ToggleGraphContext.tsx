'use client';
import React, { createContext, useState, useContext, ReactNode, useCallback } from 'react';

interface ToggleGraphContextType {
  showToggleButton: boolean;
  isGraphView: boolean;
  toggleGraph: () => void;
  setShowToggleButton: (show: boolean) => void;
  setIsGraphView: (isGraph: boolean) => void;
  setToggleGraphFunction: (fn: () => void) => void;
}

const defaultContext: ToggleGraphContextType = {
  showToggleButton: false,
  isGraphView: false,
  toggleGraph: () => {},
  setShowToggleButton: () => {},
  setIsGraphView: () => {},
  setToggleGraphFunction: () => {},
};

export const ToggleGraphContext = createContext<ToggleGraphContextType>(defaultContext);

interface ToggleGraphProviderProps {
  children: ReactNode;
}

export const ToggleGraphProvider: React.FC<ToggleGraphProviderProps> = ({ children }) => {
  const [showToggleButton, _setShowToggleButton] = useState(false);
  const [isGraphView, _setIsGraphView] = useState(false);
  const [toggleGraphFn, setToggleGraphFn] = useState<() => void>(() => {});

  // Use useCallback to ensure these functions don't change on every render
  const setShowToggleButton = useCallback((show: boolean) => {
    _setShowToggleButton(show);
  }, []);

  const setIsGraphView = useCallback((isGraph: boolean) => {
    _setIsGraphView(isGraph);
  }, []);

  const toggleGraph = useCallback(() => {
    // Toggle the isGraphView state immediately for visual feedback
    _setIsGraphView(prev => !prev);
    // Then call the actual toggle function
    toggleGraphFn();
  }, [toggleGraphFn]);

  const setToggleGraphFunction = useCallback((fn: () => void) => {
    setToggleGraphFn(() => fn);
  }, []);

  return (
    <ToggleGraphContext.Provider
      value={{
        showToggleButton,
        isGraphView,
        toggleGraph,
        setShowToggleButton,
        setIsGraphView,
        setToggleGraphFunction,
      }}
    >
      {children}
    </ToggleGraphContext.Provider>
  );
};

export const useToggleGraph = () => useContext(ToggleGraphContext);
