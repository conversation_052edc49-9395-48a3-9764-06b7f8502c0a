"use client"
import React, { createContext, useState, useEffect } from "react";
import { fetchNodeById, fetchChildArchitecture, getHeadersRaw } from "@/utils/api";
import axios from "axios";


const ArchitectureContext = createContext();

const ArchitectureProvider = ({ children }) => {
    const [architectureRootId, setArchitectureRootId] = useState(null);
    const [architectures, setArchitectures] = useState({});
    const [selectedArchitecture, setSelectedArchitecture] = useState(null);
    const [lowLevelDesignId, setLowLevelDesignId] = useState(null);
    const [lowLevelDesignVal, setLowLevelDesignVal] = useState(null)
    const [lowLevelDesignList, setLowLevelDesignList] = useState([]);
    const [lowLevelDesignDetailsId, setLowLevelDesignDetailsId] = useState(null);
    const [lowLevelDesignDetails, setLowLevelDesignDetails] = useState({});
    const [selectedArchitecturalRequirement, setSelectedArchitecturalRequirement] = useState(null);
    const [lldLoading, setLldLoading] = useState(false);
    const [dataType, setDataType] = useState(null);
    const [containerIdVal,setContainerIdVal] = useState(null)
    const [containerdata,setContainerData] = useState([])
    const [componentData,setComponentData] = useState([])
    const [componentIdVal,setComponentIdVal] = useState(null)
    const [designIdVal,setDesignIdVal] = useState(null)
    
   

    

    const selectArchitecture = (architecture) => {
        setSelectedArchitecture(architecture);
    }

    const updateSelectedArchitecturalRequirement = (requirement) => {
        setSelectedArchitecturalRequirement(requirement);
    };


    const updateArchitectures = (architectures) => {
        let updatedArchitectures = { ...architectures };
        architectures.forEach(architecture => {
            if (architecture.properties?.Title) {
                updatedArchitectures[architecture.properties?.Title] = architecture.properties;
                updatedArchitectures[architecture.properties?.Title].id = architecture.id;
            }

        }
        );
        setArchitectures(updatedArchitectures);
    }

    const getArchitecture = (title) => {
        // 
         
        if (architectures[title]) {
            return architectures[title];
        }
        return null;
    }

    const getArchitectureVal = async (id,projectId)=>{
        const response = await getArchitectureById(id,projectId);
        
        if(response){
            return response
        }
        return null
    }

    const getArchitectureById = async (id) => {
        // API call to get architecture by id
        try {
            let data = await fetchNodeById(id, "Architecture");

            let architecture = data.properties;
            architecture.ui_metadata = data.ui_metadata;
            architecture.id = id;


            return architecture;

        } catch (e) {
            
        } finally {
            const childArchitectures = await fetchChildArchitecture(id)
            updateArchitectures(childArchitectures)
        }
    }

    const fetchLowLevelDesignList = async (projectId) => {
        try {
            const response = await axios.get(
                `${process.env.NEXT_PUBLIC_API_URL}/architecture/architecture_leaf_nodes/${projectId}`,
                {
                    headers: getHeadersRaw()
                }
            );

            if (response.status === 200) {
                setLowLevelDesignList(response.data)
            }
        } catch (error) {
            
        }
    }

    const fetchLowLevelDesignDetails = async (archId) => {
        try {
            setLldLoading(true);
            const response = await axios.get(
                `${process.env.NEXT_PUBLIC_API_URL}/architecture/design_nodes/${archId}`,
                {
                    headers: getHeadersRaw()
                }
            );

            if (response.status === 200) {

                if (Array.isArray(response.data)) {
                    setLowLevelDesignDetails(response.data[0]);
                } else {
                    setLowLevelDesignDetails(response.data);
                }
            }
        } catch (error) {
            
        }
        finally {
            setLldLoading(false);
        }
    }

    useEffect(() => {
        if (lowLevelDesignId) {
            fetchLowLevelDesignList(lowLevelDesignId);
        }

    }, [lowLevelDesignId]);

    useEffect(() => {
        if (lowLevelDesignDetailsId) {
            fetchLowLevelDesignDetails(lowLevelDesignDetailsId);
        }

    }, [lowLevelDesignDetailsId]);


    const handleCancelLowLevelDesignList = async () => {
        try {
            if (!lowLevelDesignId) {
                // 
                return;
            }
            setLowLevelDesignId(null);

        } catch (error) {
            
        }

    }

    const handleCancelLowLevelDesignDetails = async () => {
        try {
            if (!lowLevelDesignDetailsId) {
                // 
                return;
            }
            setLowLevelDesignDetailsId(null);

        } catch (error) {
            
        }

    }

    lowLevelDesignDetailsId


    return (
        <ArchitectureContext.Provider value={{ selectedArchitecture, selectArchitecture, updateArchitectures, getArchitecture, getArchitectureById, architectureRootId, setArchitectureRootId, lowLevelDesignList, lowLevelDesignId, setLowLevelDesignId, handleCancelLowLevelDesignList, setLowLevelDesignDetailsId, lowLevelDesignDetails, lldLoading, handleCancelLowLevelDesignDetails,setLowLevelDesignVal,lowLevelDesignVal,getArchitectureVal ,selectedArchitecturalRequirement, updateSelectedArchitecturalRequirement,dataType , setDataType,setContainerIdVal,containerIdVal,setComponentData,componentData,setContainerData,containerdata,setComponentIdVal,componentIdVal,setDesignIdVal,designIdVal}}>
            {children}
        </ArchitectureContext.Provider>
    );
}

export { ArchitectureProvider, ArchitectureContext };