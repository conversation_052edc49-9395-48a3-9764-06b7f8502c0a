"use client";

import { createContext, useState, useEffect, useContext } from "react";
import { startDiscussion, getFigmaMessagesHistory } from "@/utils/FigmaAPI";
import { getFigmaExtImage, getFigmaFileData } from "@/api/figma";
import { useSearchParams, useRouter, useParams } from "next/navigation";

export const FigmaExtractionContext = createContext();

export const FigmaExtractionProvider = ({ children }) => {
    const [selectedDesignData, setSelectedDesignData] = useState(null)
    const [selectedFrame, setSelectedFrame] = useState(null)
    const [figmaDiscussionId, setFigmaDiscussionId] = useState(null);
    const [showFigmaExtactionModal, setShowFigmaExtactionModal] = useState(false);
    const [messagesHistory, setMessagesHistory] = useState([]);
    const searchParams = useSearchParams();
    const router = useRouter();
    const params = useParams();
    const designType = searchParams.get("type"); // Default to "figma" if not provided
    const projectId = params.projectId;

    useEffect(() => {
        const handleDiscussionState = async () => {
            const dicussionState = searchParams.get("figmaDiscussion");
            const discussionId = searchParams.get("figmaDiscussionId");
            const selectedDesignId = searchParams.get("selectedDesignId")

            

            if (dicussionState) {
                setShowFigmaExtactionModal(true);
                if (dicussionState === "existing" && discussionId) {
                    
                    setFigmaDiscussionId(discussionId);
                    const history = await getFigmaMessagesHistory(discussionId);
                    if (history?.messages?.length > 0) {
                        setMessagesHistory(history.messages);
                    }
                }
            }
            else {
                setShowFigmaExtactionModal(false);
                return;
            }

            if (dicussionState === "new") {
                try {
                    const response = await startDiscussion(projectId, selectedDesignId, designType);
                    const discussionId = response.discussion_id; // Make sure this matches your API response structure
                    setFigmaDiscussionId(discussionId);

                    // Create new URLSearchParams instance
                    const newSearchParams = new URLSearchParams(searchParams);
                    newSearchParams.set('figmaDiscussion', 'existing');
                    newSearchParams.set('figmaDiscussionId', discussionId);

                    // Use router.replace with the new search params
                    router.replace(`${window.location.pathname}?${newSearchParams.toString()}`,
                        { scroll: false }
                    );
                } catch (error) {
                    
                    setShowFigmaExtactionModal(false);
                }
            }
            
            if (selectedDesignId && designType != null) {
                let data = null;
                
                if (designType === 'figma') {
                    data = await getFigmaFileData(selectedDesignId);
                    // Ensure frames property exists for Figma data
                    if (!data || !data.frames) {
                        throw new Error('Invalid Figma data structure received');
                    }
                } else if (designType === 'image') {
                    const response = await getFigmaExtImage(projectId, selectedDesignId);
                    // Check if response and image property exist
                    if (!response || !response.image || !response.image.images) {
                        throw new Error('Invalid image data structure received');
                    }
                    // Transform image data to match expected frames structure
                    data = {
                        frames: response.image.images.map(img => ({
                            id: img.file_id,
                            name: img.filename,
                            imageUrl: img.base64url,
                            absoluteBoundingBox: {
                                width: 800,
                                height: 600
                            },
                            dimensions: {
                                height: 800,
                                width: 600
                            }
                        })) || []
                    };
                }

                setSelectedDesignData(data)
            }

        };

        handleDiscussionState();
    }, [searchParams, projectId, designType, params.projectId]);

    return (
        <FigmaExtractionContext.Provider
            value={{
                showFigmaExtactionModal,
                figmaDiscussionId,
                setShowFigmaExtactionModal,
                setFigmaDiscussionId,
                messagesHistory,
                setMessagesHistory,
                selectedFrame,
                setSelectedFrame,
                selectedDesignData,
                setSelectedDesignData,
                designType
            }}
        >
            {children}
        </FigmaExtractionContext.Provider>
    );
};

export const useFigmaExtraction = () => useContext(FigmaExtractionContext);