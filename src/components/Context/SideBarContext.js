"use client"
import React, { createContext, useState } from "react";

const SideBarContext = createContext(); 

const SideBarProvider = ({ children }) => {
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);
    const [prevProjects, setPrevProjects] = useState([]);
    const [currentProject, setCurrentProject] = useState(null);

    const toggleSidebar = () => {
        setIsSidebarOpen((prevState) => !prevState);
    };

    const closeSidebar = () => {
        setIsSidebarOpen(false);
    };

    const updateCurrentProject = (project) => {
        setCurrentProject(project);
        setPrevProjects((prevProjects) => [...prevProjects, project]);
    };

    return (
        <SideBarContext.Provider value={{ isSidebarOpen, toggleSidebar, closeSidebar, prevProjects, setPrevProjects, currentProject, updateCurrentProject }}>
            {children}
        </SideBarContext.Provider>
    );
}

export { SideBarProvider, SideBarContext };
