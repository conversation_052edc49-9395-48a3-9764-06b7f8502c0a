import React, { useState,useEffect } from 'react';

interface BaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  isDarkMode?: boolean;
}

const BaseModal: React.FC<BaseModalProps> = ({ isOpen, onClose, title, children, isDarkMode = false }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="fixed inset-0 bg-black opacity-50" onClick={onClose}></div>
      <div className="rounded-lg w-full max-w-md mx-auto z-10 overflow-hidden bg-custom-bg-primary">
        <div className="flex justify-between items-center p-4 border-b border-custom-border">
          <h2 className="typography-body-lg font-weight-medium text-custom-text-primary">
            {title}
          </h2>
          <button
            onClick={onClose}
            className="hover:opacity-70 text-custom-text-secondary"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
        <div className="p-4">{children}</div>
      </div>
    </div>
  );
};

// Edit Title Modal
interface EditTitleModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: any;
  onSave: (projectId: string, newTitle: string) => void;
  isLoading?: boolean;
  isDarkMode?: boolean;
}

export const EditTitleModal: React.FC<EditTitleModalProps> = ({ 
  isOpen, 
  onClose, 
  project, 
  onSave, 
  isLoading = false,
  isDarkMode = false 
}) => {
  const [title, setTitle] = useState('');
  
  useEffect(() => {
    if (isOpen && project) {
      setTitle(project.Title || '');
    }
  }, [isOpen, project]);

  const handleSave = () => {
    if (title.trim() !== '') {
      onSave(project.id, title);
    }
  };
  
  return (
    <BaseModal isOpen={isOpen} onClose={onClose} title="Edit Project Title" isDarkMode={isDarkMode}>
      <div className="space-y-4">
        <div>
          <label
            htmlFor="project-title"
            className="block typography-body-sm font-weight-medium mb-1 text-custom-text-primary"
          >
            Project Title
          </label>
          <input
            type="text"
            id="project-title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="w-full px-3 py-2 border border-custom-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-custom-bg-primary text-custom-text-primary placeholder-custom-text-secondary"
            autoFocus
            disabled={isLoading}
          />
        </div>
        <div className="flex justify-end space-x-2">
          <button
            onClick={onClose}
            className={`px-4 py-2 border border-custom-border rounded-md hover:opacity-80 text-custom-text-primary hover:bg-custom-bg-secondary ${
              isLoading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            onClick={handleSave}
            className={`px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary ${
              isLoading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            disabled={isLoading}
          >
            {isLoading ? 'Saving...' : 'Save'}
          </button>
        </div>
      </div>
    </BaseModal>
  );
};

// Delete Project Modal
interface DeleteProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: any;
  onConfirm: (projectId: string) => void;
  isLoading?: boolean;
  isDarkMode?: boolean;
}

export const DeleteProjectModal: React.FC<DeleteProjectModalProps> = ({ 
  isOpen, 
  onClose, 
  project, 
  onConfirm,
  isLoading = false,
  isDarkMode = false 
}) => {
  const handleConfirm = () => {
    onConfirm(project.id);
  };

  return (
    <BaseModal isOpen={isOpen} onClose={onClose} title="Delete Project" isDarkMode={isDarkMode}>
      <div className="space-y-4">
        <p className="text-custom-text-primary">
          Are you sure you want to delete the project "
          <span className="font-weight-medium">{project?.Title}</span>"?
        </p>
        <p className="text-destructive font-weight-medium">
          By deleting this project, other related data will also be permanently removed
        </p>
        <div className="flex justify-end space-x-2">
          <button
            onClick={onClose}
            className={`px-4 py-2 border border-custom-border rounded-md hover:opacity-80 text-custom-text-primary hover:bg-custom-bg-secondary ${
              isLoading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            onClick={handleConfirm}
            className={`px-4 py-2 bg-destructive text-destructive-foreground rounded-md hover:bg-destructive/90 focus:outline-none focus:ring-2 focus:ring-destructive ${
              isLoading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            disabled={isLoading}
          >
            {isLoading ? 'Deleting...' : 'Delete'}
          </button>
        </div>
      </div>
    </BaseModal>
  );
};

// Clone Project Modal
interface CloneProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: any;
  onConfirm: (projectId: string, newTitle: string) => void;
  isLoading?: boolean;
  isDarkMode?: boolean;
}

export const CloneProjectModal: React.FC<CloneProjectModalProps> = ({ 
  isOpen, 
  onClose, 
  project, 
  onConfirm,
  isLoading = false,
  isDarkMode = false 
}) => {
  const [title, setTitle] = useState(`Copy of ${project?.Title || ''}`);

  const handleConfirm = () => {
    if (title.trim() !== '') {
      onConfirm(project.id, title);
    }
  };

  return (
    <BaseModal isOpen={isOpen} onClose={onClose} title="Clone Project" isDarkMode={isDarkMode}>
      <div className="space-y-4">
        <p>
          Create a copy of the project - 
          <i className="font-weight-medium">{project?.Title}</i>
        </p>
        <div>
          <label
            htmlFor="clone-title"
            className="block typography-body-sm font-weight-medium mb-1 text-custom-text-primary"
          >
            New Project Title
          </label>
          <input
            type="text"
            id="clone-title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="w-full px-3 py-2 border border-custom-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-custom-bg-primary text-custom-text-primary placeholder-custom-text-secondary"
            autoFocus
            disabled={isLoading}
          />
        </div>
        <div className="flex justify-end space-x-2">
          <button
            onClick={onClose}
            className={`px-4 py-2 border border-custom-border rounded-md hover:opacity-80 text-custom-text-primary hover:bg-custom-bg-secondary ${
              isLoading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            onClick={handleConfirm}
            className={`px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary ${
              isLoading ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            disabled={isLoading}
          >
            {isLoading ? 'Cloning...' : 'Clone'}
          </button>
        </div>
      </div>
    </BaseModal>
  );
};