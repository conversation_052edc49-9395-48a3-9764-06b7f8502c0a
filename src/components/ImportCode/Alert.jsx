// components/Alert.js
import React, { useEffect, useState } from 'react';
import { IoMdClose } from 'react-icons/io';

const Alert = ({ message, type = 'success', duration = 5000, onClose }) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      onClose();
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  if (!isVisible) return null;

  return (
    <div className="fixed top-4 right-4 z-50 max-w-md animate-slide-in-right">
      <div className={`bg-green-100 border-l-4 border-green-500 p-4 rounded shadow-lg`}>
        <div className="flex items-center">
          <div className="text-green-500 mr-3">
            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="flex-grow">
            <p className="font-weight-bold">Alert heading</p>
            <p className="typography-body-sm">{message}</p>
          </div>
          <button onClick={() => setIsVisible(false)} className="text-green-500 hover:text-green-700">
            <IoMdClose size={20} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default Alert;