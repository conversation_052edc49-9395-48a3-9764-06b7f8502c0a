import React from 'react';
import CardList from './CardList';
interface AppcardProps {
    cardLists: any[];
    onCardClick: (cardData: any) => void;
}

const AppCard: React.FC<AppcardProps> = ({ cardLists, onCardClick }) => {
    return (
        <div className="max-h-[530px] overflow-y-auto">
            {cardLists.map((cardData, index) => (
                <CardList
                    key={index}
                    title={cardData.title}
                    isMenu={cardData.isMenu}
                    isSearch={cardData.isSearch}
                    listArray={cardData.listArray}
                    onCardClick={onCardClick}
                />
            ))}
        </div>
    );
};

export default AppCard;
