import React from 'react';
import { Menu } from '@headlessui/react';
import { FaCheck, FaEllipsisH } from 'react-icons/fa';

interface IntegrationCardProps {
    title?: any;
    description?: any;
    imgSrc?: any;
    isConnected: boolean;
    isMenu?: boolean;
    connectedSettingsArray?: any[];
    handleMenuClick?(item?: any): any;
    onCardClick?: () => void;
}

const IntegrationCard: React.FC<IntegrationCardProps> = ({
    title,
    description,
    imgSrc,
    connectedSettingsArray,
    isConnected,
    isMenu,
    handleMenuClick,
    onCardClick }) => {
    const _handleMenuClick = (item?: any) => {
        handleMenuClick && handleMenuClick(item)
    }
    return (
        <div
            onClick={onCardClick}
            className="max-w-sm mx-auto bg-white shadow-md rounded-lg p-4 mb-4 hover:shadow-lg hover:bg-gray-50 hover:scale-105 transform transition duration-200">
            <div className="flex items-center">
                <div className="w-10 h-10 flex-shrink-0">
                    <img
                        src={imgSrc}
                        alt={title}
                        className="object-contain w-full h-full"
                    />
                </div>
                <div className="ml-auto relative flex items-center">
                    {isConnected ? (
                        <div className="flex items-center space-x-2">
                            <div className="bg-green-500 w-5 h-5 rounded-full flex items-center justify-center">
                                <FaCheck size={10} />
                            </div>
                            {isMenu && <Menu as="div" className="relative">
                                <Menu.Button className="p-2 rounded-lg border border-gray-300 shadow-sm hover:shadow-md bg-white transition duration-200"
                                    onClick={(e) => e.stopPropagation()}>
                                    <FaEllipsisH size={10} />
                                </Menu.Button>
                                <Menu.Items className="absolute right-0 mt-2 w-48 bg-white border rounded-lg shadow-lg">
                                    {connectedSettingsArray && connectedSettingsArray.map((item, index) => (
                                        <Menu.Item key={index}>
                                            {({ active }) => (
                                                <button
                                                    className={`block w-full text-left px-4 py-2 ${active ? 'bg-gray-100' : ''}`}
                                                    onClick={(e) => {
                                                        e.stopPropagation();
                                                        _handleMenuClick(item)
                                                    }}
                                                >
                                                    {item}
                                                </button>
                                            )}
                                        </Menu.Item>
                                    ))}
                                </Menu.Items>
                            </Menu>}
                        </div>

                    ) : (
                        <button className="px-4 py-2 bg-gray-100 text-black-600 font-weight-medium typography-body-sm rounded-md hover:bg-gray-200 transition duration-200">
                            Connect
                        </button>
                    )}
                </div>

            </div>
            <div className="mt-4 flex-grow">
                {title && <h2 className="typography-body-lg font-weight-semibold text-gray-800">{title}</h2>}
                {description && <p className="typography-body-sm text-gray-600">
                    {description}
                </p>}
            </div>
        </div>
    );
};

export default IntegrationCard;
