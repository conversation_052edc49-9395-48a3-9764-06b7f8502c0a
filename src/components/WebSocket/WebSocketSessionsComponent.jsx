import { useEffect } from 'react';
import { useWebSocket } from '@/components/Context/WebsocketContext';

const WebSocketSessionsComponent = ({ repositories }) => {
  const { connectToSession } = useWebSocket();

  useEffect(() => {
    // Get unique session IDs
    try{
    const sessionIds = new Set();
    repositories?.forEach(repo => {
      if(repo.service == "github"){
        repo?.branches?.forEach(branch => {
          if (branch?.builds?.kg_creation_status === 1 && branch?.builds?.build_session_id) {
            sessionIds.add(branch.builds.build_session_id);
          }
        });
      } else {
        if(repo.builds.kg_creation_status == 1 && repo.builds.build_session_id){
          sessionIds.add(repo.builds.build_session_id);
        }
      }
    });

    // Connect to each session
    Array.from(sessionIds).forEach(sessionId => {
        connectToSession(sessionId);
      });
    }catch(err){
      }
  }, [repositories, connectToSession]);

  return null;
};

export default WebSocketSessionsComponent; 