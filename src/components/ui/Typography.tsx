import React from 'react';
import { cn } from '@/lib/utils';

type TypographyVariant =
  | 'h1'
  | 'h2'
  | 'h3'
  | 'h4'
  | 'h5'
  | 'h6'
  | 'body-lg'
  | 'body'
  | 'body-sm'
  | 'caption';

type TypographyWeight = 'light' | 'normal' | 'medium' | 'semibold' | 'bold';

interface TypographyProps {
  variant?: TypographyVariant;
  weight?: TypographyWeight;
  className?: string;
  children: React.ReactNode;
  as?: React.ElementType;
  color?: string;
}

const variantClassMap: Record<TypographyVariant, string> = {
  h1: 'typography-heading-1',
  h2: 'typography-heading-2',
  h3: 'typography-heading-3',
  h4: 'typography-heading-4',
  h5: 'typography-heading-5',
  h6: 'typography-heading-6',
  'body-lg': 'typography-body-lg',
  body: 'typography-body',
  'body-sm': 'typography-body-sm',
  caption: 'typography-caption',
};

const weightClassMap: Record<TypographyWeight, string> = {
  light: 'font-weight-light',
  normal: 'font-weight-normal',
  medium: 'font-weight-medium',
  semibold: 'font-weight-semibold',
  bold: 'font-weight-bold',
};

const elementMap: Record<TypographyVariant, React.ElementType> = {
  h1: 'h1',
  h2: 'h2',
  h3: 'h3',
  h4: 'h4',
  h5: 'h5',
  h6: 'h6',
  'body-lg': 'p',
  body: 'p',
  'body-sm': 'p',
  caption: 'span',
};

export const Typography: React.FC<TypographyProps> = ({
  variant = 'body',
  weight = 'normal',
  className = '',
  children,
  as,
  color,
}) => {
  const Component = as || elementMap[variant];

  const variantClasses = variantClassMap[variant];
  const weightClasses = weightClassMap[weight];
  const colorClasses = color ? `text-${color}` : '';

  return (
    <Component
      className={cn(
        variantClasses,
        weightClasses,
        colorClasses,
        className
      )}
    >
      {children}
    </Component>
  );
};

// Heading components for convenience
export const Heading1: React.FC<Omit<TypographyProps, 'variant' | 'as'>> = (props) => (
  <Typography variant="h1" weight="bold" {...props} />
);

export const Heading2: React.FC<Omit<TypographyProps, 'variant' | 'as'>> = (props) => (
  <Typography variant="h2" weight="bold" {...props} />
);

export const Heading3: React.FC<Omit<TypographyProps, 'variant' | 'as'>> = (props) => (
  <Typography variant="h3" weight="semibold" {...props} />
);

export const Heading4: React.FC<Omit<TypographyProps, 'variant' | 'as'>> = (props) => (
  <Typography variant="h4" weight="semibold" {...props} />
);

export const Heading5: React.FC<Omit<TypographyProps, 'variant' | 'as'>> = (props) => (
  <Typography variant="h5" weight="medium" {...props} />
);

export const Heading6: React.FC<Omit<TypographyProps, 'variant' | 'as'>> = (props) => (
  <Typography variant="h6" weight="medium" {...props} />
);

// Text components for convenience
export const TextLarge: React.FC<Omit<TypographyProps, 'variant' | 'as'>> = (props) => (
  <Typography variant="body-lg" {...props} />
);

export const Text: React.FC<Omit<TypographyProps, 'variant' | 'as'>> = (props) => (
  <Typography variant="body" {...props} />
);

export const TextSmall: React.FC<Omit<TypographyProps, 'variant' | 'as'>> = (props) => (
  <Typography variant="body-sm" {...props} />
);

export const Caption: React.FC<Omit<TypographyProps, 'variant' | 'as'>> = (props) => (
  <Typography variant="caption" {...props} />
);
