// components/ui/Button.jsx
import { cn } from '../../lib/utils';

const Button = ({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  className = '',
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-weight-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none';

  const variants = {
    primary: 'bg-semantic-gray-600 text-semantic-gray-50 hover:bg-semantic-gray-700 focus:ring-semantic-gray-500',
    secondary: 'bg-semantic-gray-100 text-semantic-gray-900 hover:bg-semantic-gray-200 focus:ring-semantic-gray-500',
    outline: 'border border-semantic-gray-300 bg-custom-bg-primary text-semantic-gray-700 hover:bg-semantic-gray-50 focus:ring-semantic-gray-500',
    ghost: 'text-semantic-gray-600 hover:text-semantic-gray-800 hover:bg-semantic-gray-50 focus:ring-semantic-gray-500',
    danger: 'bg-destructive text-destructive-foreground hover:bg-destructive/90 focus:ring-destructive',
    orange: 'bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-primary'
  };

  const sizes = {
    sm: 'px-3 py-1.5 typography-body-sm',
    md: 'px-4 py-2 typography-body-sm',
    lg: 'px-6 py-3 typography-body'
  };

  return (
    <button
      className={cn(
        baseClasses,
        variants[variant],
        sizes[size],
        className
      )}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      )}
      {children}
    </button>
  );
};

export default Button;