// components/ui/Card.jsx
import { cn } from '../../lib/utils';

export const Card = ({ children, className = '', ...props }) => (
  <div className={cn('bg-custom-bg-primary rounded-lg shadow', className)} {...props}>
    {children}
  </div>
);

export const CardHeader = ({ children, className = '', ...props }) => (
  <div className={cn('px-4 py-3 border-b border-custom-border', className)} {...props}>
    {children}
  </div>
);

export const CardContent = ({ children, className = '', ...props }) => (
  <div className={cn('px-4 py-3', className)} {...props}>
    {children}
  </div>
);

export const CardTitle = ({ children, className = '', ...props }) => (
  <h3 className={cn('typography-body-lg font-weight-semibold text-custom-text-primary', className)} {...props}>
    {children}
  </h3>
);