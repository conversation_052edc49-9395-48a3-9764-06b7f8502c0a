"use client";
import React, { useState, useEffect, useContext } from 'react';
import ChatList from './ChatList';
import { fetchChatHistory } from '../../utils/api';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import {ChatListSkeleton} from "@/components/UIComponents/Loaders/LoaderGroup"
import { TopBarContext } from '../Context/TopBarContext';
import ErrorView from '../Modal/ErrorViewModal';
import en from "../../en.json";
import Cookies from 'js-cookie';

import '@/styles/chatlist.css';
import { useUser } from '../Context/UserContext';

const defaultChats = [
  { id: '1', properties: { Title: 'General Chat', created_at: '2024-01-01' } },
  { id: '2', properties: { Title: 'Project Discussion', created_at: '2024-01-05' } },
];

interface PreloadChatListProps {
  handleDrawerToggle: () => void;
}

const PreloadChatList: React.FC<PreloadChatListProps> = ({ handleDrawerToggle }) => {
  const [chats, setChats] = useState(defaultChats);
  const [isLoading, setIsLoading] = useState(true);
  const { tabs, addTab } = useContext(TopBarContext); //to set the tab
  const [error, setError] = useState(null);
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const id = pathname.split("/")[3];
  const router = useRouter();
  const [currentChatId, setCurrentChatId] = useState(null);
  const [isChatModalOpen, setIsChatModalOpen] = useState(false);
  const { is_public_project_selected } = useUser();


  const fetchChats = async () => {
    setError(null);
    setIsLoading(true);
    try {
      const data = await fetchChatHistory();
      setChats(data);
    } catch (err: any) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchChats();
  }, []);

  const handleChatClick = (chat: any) => {
    const { buildProjectUrl } = require('@/utils/navigationHelpers');
    const chatUrl = `${buildProjectUrl(chat.project_id, 'overview')}?discussion_id=${chat.discussion_id}`;
    router.push(chatUrl);
    const tab = tabs.find((tab: any) => tab.href === chatUrl);

    // Create project info for the chat's project
    const projectInfo = {
      selected_project_id: chat.project_id,
      selected_project_creator_email: chat.creator_email || '',
      is_public_selected: is_public_project_selected(chat.creator_email), //TODO:PUBLICSELECTED
      selected_tenant_id: chat.tenant_id || Cookies.get('tenant_id') || ''
    };

    // Store in cookies for backward compatibility
    Cookies.set('selected_project_id', projectInfo.selected_project_id);
    Cookies.set('selected_project_creator_email', projectInfo.selected_project_creator_email);
    Cookies.set('is_public_selected', projectInfo.is_public_selected);
    Cookies.set('selected_tenant_id', projectInfo.selected_tenant_id);

    if (!tab) {
      addTab(chat.project_name, chatUrl, projectInfo);
    }

    // Update any necessary state here
    setCurrentChatId(chat.discussion_id);

    // Close the drawer
    handleDrawerToggle();
  };

  const createNewChatAndCloseSidebar = () => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('discussion', 'new');
    newSearchParams.set('node_id', id);
    newSearchParams.set('node_type', 'RequirementRoot');
    router.push(`${pathname}?${newSearchParams.toString()}`);
  };

  const handleCloseModal = () => {
    setIsChatModalOpen(false);
  };

  const handleUpdateResponse = (response: any) => {
    setChats([...chats, response]);
  };
  if (isLoading) return <ChatListSkeleton />;

  if (error) {
    return (
      <div className="preload-chat-list">
        <ErrorView
          title="Unable to Load Chats"
          message={en.ChatList_ErrorLoadingChats}
          showRetryButton={true}
          onRetry={() => fetchChats}
        />
      </div>
    );
  }

  return (
    <div className="preload-chat-list">
      <ChatList
        chats={chats}
        onChatClick={handleChatClick}
        createNewChatAndCloseSidebar={createNewChatAndCloseSidebar}
      />
    </div>
  );
};

PreloadChatList.displayName = 'PreloadChatList';
export default PreloadChatList;