import React, { useState, useEffect, useContext } from "react";
import { renameChat, deleteChat } from "@/utils/chatAPI";
import { AlertContext } from "../NotificationAlertService/AlertList";
import EmptyStateView from "../Modal/EmptyStateModal";
import ChatListItem from "./ChatItem";
import { SearchInput } from "../UIComponents/Inputs/SearchInput";


export interface ChatListProps {
  chats: any[];
  onChatClick?: (chat: any) => void;
  createNewChatAndCloseSidebar?: () => void;
  openChatModal?: () => void;
  onDeleteChat?: (chatId: string) => void;
  onRenameChat?: (chatId: string, newChatName: string) => void;
}


const ChatList: React.FC<ChatListProps> = ({
  chats,
  onChatClick,
  createNewChatAndCloseSidebar,
  openChatModal,
  onDeleteChat,
  onRenameChat,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState(chats);
  const [selectedChatId, setSelectedChatId] = useState<string>(() => {
    return sessionStorage.getItem("selectedChatId") || "";
  });
  const [menuVisible, setMenuVisible] = useState<string | null>(null);
  const [renamingChatId, setRenamingChatId] = useState<string | null>(null);
  const [newChatName, setNewChatName] = useState("");
  const [isRenaming, setIsRenaming] = useState(false);
  const { showAlert } = useContext(AlertContext);

  useEffect(() => {
    const results = chats.filter((chat) =>
      chat.name?.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setSearchResults(results);
  }, [searchTerm, chats]);

  useEffect(() => {
    const storedChatId = sessionStorage.getItem("selectedChatId");
    if (storedChatId) setSelectedChatId(storedChatId);
  }, []);

  const handleChatClick = (chat: any) => {
    onChatClick?.(chat);
    setSelectedChatId(chat.discussion_id);
    sessionStorage.setItem("selectedChatId", chat.id);
  };

  const handleRename = (chatId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    setRenamingChatId(chatId);
    const chat = chats.find((c) => c.discussion_id === chatId);
    setNewChatName(chat?.name || "");
    setMenuVisible(null);
  };

  const updateLocalChat = (chatId: string, updates: Partial<any>) => {
    setSearchResults(prevResults =>
      prevResults.map(chat =>
        chat.discussion_id === chatId
          ? { ...chat, ...updates }
          : chat
      )
    );
  };

  const handleClearSearch =() => {
    setSearchTerm("");
  }

  const handleRenameSubmit = async (chatId: string, newName: string) => {
    setIsRenaming(true);

    try {
      const response = await renameChat(chatId, newName);

      if (response.message === "updated successfully") {
        // Update parent component state if needed
        onRenameChat?.(chatId, newName);
        showAlert("Chat renamed successfully", "success");
      }
    } catch (error) {

      showAlert("Failed to rename chat", "error");
      // Revert local changes if the API call fails
      const originalChat = chats.find(chat => chat.discussion_id === chatId);
      if (originalChat) {
        updateLocalChat(chatId, { name: originalChat.name });
      }
    } finally {
      setIsRenaming(false);
      setRenamingChatId(null);
    }
  };

  const toggleMenu = (chatId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    setMenuVisible(menuVisible === chatId ? null : chatId);
  };

  const handleDelete = async (chatId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    try {
      const response = await deleteChat(chatId);
      if (response.message === "Deleted successfully") {
        // Update the local state
        const updatedChats = searchResults.filter(chat => chat.discussion_id !== chatId);
        setSearchResults(updatedChats);

        // Notify the parent component
        onDeleteChat?.(chatId);

        // Show success notification
        showAlert("Chat deleted successfully", "success");
      }
    } catch (error) {

      showAlert("Failed to delete chat", "error");
    }
    setMenuVisible(null);
  };

  return (
    <div className="chat-list">
      <SearchInput
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        withContainer={true}
      />
      <div className="chat-list-container custom-scrollbar">
        {searchResults.length > 0 ? (
          searchResults.map((chat, index) => (
            <ChatListItem
              key={chat.discussion_id || chat.id || `chat-${index}`}
              chat={chat}
              selectedChatId={selectedChatId}
              onChatClick={handleChatClick}
              onDeleteSuccess={(chatId) => {
                const updatedChats = searchResults.filter(chat => chat.discussion_id !== chatId);
                setSearchResults(updatedChats);
                onDeleteChat?.(chatId);
              }}
              onRename={handleRenameSubmit}
              isRenaming={isRenaming}
              renamingChatId={renamingChatId}
              onRenameStart={(chatId) => setRenamingChatId(chatId)}
              showAlert={showAlert}
              onUpdateLocalChat={updateLocalChat}
            />
          ))
        ) : (
          <div className="flex justify-center items-center h-96">
          {chats.length === 0 ? (
            <EmptyStateView type="chat" onClick={()=> {}}/>
          ) : (
            <EmptyStateView type="noSearchResult"  onClick={handleClearSearch}/>
          )}
        </div>
        )}
      </div>
    </div >
  );
};

ChatList.displayName = "ChatList";

export default ChatList;
