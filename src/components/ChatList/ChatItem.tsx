import React, { useState } from 'react';
import { formatListDate } from "@/utils/datetime";
import { deleteChat } from "@/utils/chatAPI";
import { Trash2, Edit2 } from 'lucide-react';
import CustomDropdown, { MenuOption } from '@/components/UIComponents/Dropdowns/CustomDropdown';

interface ChatListItemProps {
  chat: any;
  selectedChatId: string;
  onChatClick: (chat: any) => void;
  onDeleteSuccess: (chatId: string) => void;
  onRename: (chatId: string, newName: string) => Promise<void>;
  isRenaming: boolean;
  renamingChatId: string | null;
  onRenameStart: (chatId: string) => void;
  showAlert: (message: string, type: string) => void;
  onUpdateLocalChat: (chatId: string, updates: Partial<any>) => void;
}

const ChatListItem = ({
  chat,
  selectedChatId,
  onChatClick,
  onDeleteSuccess,
  onRename,
  isRenaming,
  renamingChatId,
  onRenameStart,
  showAlert,
  onUpdateLocalChat
}: ChatListItemProps) => {
  const [menuVisible, setMenuVisible] = useState(false);
  const [newChatName, setNewChatName] = useState(chat.name);
  const [isDeleting, setIsDeleting] = useState(false);
  const isRenamingThis = renamingChatId === chat.discussion_id;

  const toggleMenu = (event: React.MouseEvent) => {
    event.stopPropagation();
    setMenuVisible(!menuVisible);
  };

  const handleRenameClick = (event: React.MouseEvent) => {
    event.stopPropagation();
    onRenameStart(chat.discussion_id);
    setNewChatName(chat.name);
    setMenuVisible(false);
  };

  const handleRenameSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    event.stopPropagation();

    if (newChatName.trim() !== chat.name) {
      try {
        await onRename(chat.discussion_id, newChatName.trim());
        // Update local state immediately after successful rename
        onUpdateLocalChat(chat.discussion_id, { name: newChatName.trim() });
      } catch (error) {
        // Reset to original name if rename fails
        setNewChatName(chat.name);
      }
    }
  };

  const handleDelete = async (event: React.MouseEvent) => {
    event.stopPropagation();

    if (isDeleting) return;

    try {
      setIsDeleting(true);
      const response = await deleteChat(chat.discussion_id);

      if (response.message === "Deleted successfully") {
        onDeleteSuccess(chat.discussion_id);
        showAlert("Chat deleted successfully", "success");
      } else {
        throw new Error("Delete operation failed");
      }
    } catch (error) {
      
      showAlert("Failed to delete chat", "error");
    } finally {
      setIsDeleting(false);
      setMenuVisible(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewChatName(e.target.value);
  };

  const handleInputClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  const menuOptions: MenuOption[] = [
    {
      label: isDeleting ? "Deleting..." : "Delete",
      icon: Trash2,
      onClick: handleDelete,
      disabled: isDeleting,
      variant: 'danger'
    },
    {
      label: "Rename",
      icon: Edit2,
      onClick: handleRenameClick,
      disabled: isRenaming
    }
  ];

  return (
    <div
      className={`chat-item ${selectedChatId === chat.discussion_id ? "selected" : ""}`}
      onClick={() => onChatClick(chat)}
    >
      <div className="chat-info">
        <div className="chat-details">
          {isRenamingThis ? (
            <form onSubmit={handleRenameSubmit}>
              <input
                type="text"
                value={newChatName}
                onChange={handleInputChange}
                className="rename-input"
                onClick={handleInputClick}
                autoFocus
                disabled={isRenaming}
              />
            </form>
          ) : (
            <>
              <div className="chat-name">{chat.name}</div>
              <div className="chat-date">
                {formatListDate(chat.created_at)}
              </div>
            </>
          )}
        </div>
      </div>

      <div className="chat-menu" >
        <CustomDropdown
          options={menuOptions}
          isLoading={isDeleting}
          align="right"
          size='sqSmall'
        />
      </div>
    </div>
  );
};

export default ChatListItem;