// closeButton.tsx

import { forwardRef, MouseEvent } from 'react';
import { HiX } from 'react-icons/hi';
import classNames from 'classnames';

// Define and export the CloseButtonProps type
export type CloseButtonProps = {
  absolute?: boolean;
  defaultStyle?: boolean;
  onClick?: (e: MouseEvent<HTMLSpanElement>) => void;
  className?: string;
};

// Define the CloseButton component using forwardRef and CloseButtonProps
const CloseButton = forwardRef<HTMLSpanElement, CloseButtonProps>((props, ref) => {
  const { absolute, className, defaultStyle, onClick, ...rest } = props;

  // Generate class names conditionally
  const closeButtonClass = classNames(
    'close-btn',
    defaultStyle && 'close-btn-default',
    absolute && 'absolute z-10',
    className
  );

  // Return the button with icon
  return (
    <span
      className={closeButtonClass}
      role="button"
      onClick={onClick}
      {...rest}
      ref={ref}
    >
      <HiX />
    </span>
  );
});

// Set displayName for better debugging and testing
CloseButton.displayName = 'CloseButton';

// Export the CloseButton component
export default CloseButton;
