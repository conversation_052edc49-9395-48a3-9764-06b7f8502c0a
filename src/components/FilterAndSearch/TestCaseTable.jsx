"use client";

import React, { useState } from "react";
import TableComponent from "../SimpleTable/table"; // Ensure this path is correct
import { MoreVertical } from "lucide-react";

const TestCaseTable = ({ onActionPerformed }) => {
  const [selectedId, setSelectedId] = useState(null);

  const data = [
    {
      id: "1",
      testCase: "Basic City Search Validation",
      estimate: "4m",
      priority: "Normal",
    },
    {
      id: "2",
      testCase: "Verify accuracy of temperature prediction algorithm",
      estimate: "2M",
      priority: "High",
    },
    {
      id: "3",
      testCase: "GPS Location Accuracy",
      estimate: "4m",
      priority: "High",
    },
    // More data...
  ];

  const headers = [
    { key: "checkbox", label: "", actionLabel: "" },
    { key: "testCase", label: "TEST CASE" },
    { key: "estimate", label: "ESTIMATE" },
    { key: "priority", label: "PRIORITY" },
    { key: "action", label: "", icon: <MoreVertical size={16} /> },
  ];

  const sortableColumns = {
    testCase: true,
    estimate: true,
    priority: true,
  };

  const handleRowClick = (id) => {
    setSelectedId(id); // Set the selected row's id
    
    onActionPerformed(id);
  };

  const handleActionClick = (id) => {
    
    onActionPerformed(id);
  };

  return (
    <div>
      <TableComponent
        title=""
        data={data}
        headers={headers}
        onRowClick={handleRowClick}
        sortableColumns={sortableColumns}
        itemsPerPage={20}
        onActionClick={handleActionClick}
      />
    </div>
  );
};

export default TestCaseTable;
