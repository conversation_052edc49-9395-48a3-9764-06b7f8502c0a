
// Helper function to determine color based on time
export const getTimeColor = (minutes: number): string => {
    if (minutes <= 15) {
      return 'text-green-600 bg-green-50 border-green-200';
    } else if (minutes <= 45) {
      return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    } else {
      return 'text-red-600 bg-red-50 border-red-200';
    }
  };
  
  // Helper function to format time display
  export const formatTimeDisplay = (minutes: number): string => {
    if (minutes < 60) {
      return `${minutes} mins`;
    } else if (minutes < 120) {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return mins > 0 ? `${hours} hour ${mins} mins` : `${hours} hour`;
    } else {
      const hours = Math.ceil(minutes / 60);
      return `${hours} hours`;
    }
  };