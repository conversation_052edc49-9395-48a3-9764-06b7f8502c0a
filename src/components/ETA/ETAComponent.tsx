import React from "react";
import { Clock } from "lucide-react";
import { ETAComponentProps } from "./ETATypes";
import { getTimeColor, formatTimeDisplay } from "./ETAUtils";

const ETAComponent: React.FC<ETAComponentProps> = ({
  estimatedMinutes,
  itemsSelected,
  displayText,
  className = "",
  size = "md",
}) => {
  const colorClasses = getTimeColor(estimatedMinutes);
  const timeDisplay = displayText || formatTimeDisplay(estimatedMinutes);

  // Size variant configurations
  const sizeConfig = {
    sm: {
      container: "px-3 py-1.5 rounded-md",
      icon: "w-3 h-3",
      text: "text-xs font-semibold",
      label: "text-xs font-medium",
      time: "text-xs font-semibold",
    },

    md: {
      container: "p-3",
      icon: "w-4 h-4",
      text: "text-sm",
      label: "text-sm font-medium",
      time: "text-sm font-semibold",
    },
    lg: {
      container: "p-4",
      icon: "w-5 h-5",
      text: "text-base",
      label: "text-base font-medium",
      time: "text-base font-semibold",
    },
  };

  const config = sizeConfig[size];

  return (
    <div
      className={`rounded-lg border ${colorClasses} ${config.container} ${className}`}
    >
      <div className="flex items-center justify-between gap-4">
        {/* Left side - Icon and label */}
        <div className="flex items-center gap-1">
          <Clock className={config.icon} />
          <span className={config.label}>Estimated Time</span>
        </div>

        {/* Middle - Items selected (only show if itemsSelected is provided) */}
        {itemsSelected !== undefined && (
          <div className={`${config.text} mx-2`}>
            {itemsSelected} {itemsSelected === 1 ? "item" : "items"} selected
          </div>
        )}

        {/* Right side - Time display */}
        <div className={`${config.time} ml-auto`}>{timeDisplay}</div>
      </div>
    </div>
  );
};

export default ETAComponent;
