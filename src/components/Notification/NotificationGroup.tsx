import { cn } from "@/lib/utils";
import { CheckCircle, Trash2 } from "lucide-react";
import { formatUTCToLocal } from "@/utils/datetime";
import { IconButton } from "../UIComponents/Buttons/IconButton";

interface NotificationGroupProps {
  date: string;
  items: any[];
  notificationTooltips: { [key: number]: string };
  markRead: (id: any, handler: (value: number) => void) => void;
  handleValueChange: (value: number) => void;
  handleDrawerToggle: (drawerName: string) => void;
  openDeleteModal: (notificationId: string, receiverId: string) => void;
  onNotificationClick: (item: any) => void;
  theme?: 'light' | 'dark';
}

const handleTypeDisplay = (item: any) => {
  if (item.type === "code_generation") {
    if (item.data.message.includes("cg")) {
      return "Code Generation";
    } else if (item.data.message.includes("cm")) {
      return "Code Maintenance";
    } else {
      return item.type;
    }
  }
  return item.type;
};

export const NotificationGroup = ({
  date,
  items,
  notificationTooltips,
  markRead,
  handleValueChange,
  handleDrawerToggle,
  openDeleteModal,
  onNotificationClick,
  theme = 'light',
}: NotificationGroupProps) => {
  const themeClasses = {
    light: {
      group: "",
      dateHeader: "text-custom-text-secondary",
      item: "bg-custom-bg-primary border-custom-border hover:border-primary hover:bg-custom-bg-secondary",
      itemUnread: "bg-custom-bg-secondary border-primary",
      typeTag: "bg-custom-bg-muted text-custom-text-primary",
      unreadIndicator: "bg-primary",
      title: "text-custom-text-primary hover:text-custom-text-secondary",
      timestamp: "text-custom-text-secondary",
      divider: "border-custom-border",
      actionButton: "text-custom-text-secondary",
      markReadButton: "text-custom-text-secondary hover:text-primary",
      deleteButton: "text-custom-text-secondary hover:text-custom-text-destructive",
    },
    dark: {
      group: "",
      dateHeader: "text-custom-text-secondary",
      item: "bg-custom-bg-primary border-custom-border hover:border-primary hover:bg-custom-bg-secondary",
      itemUnread: "bg-custom-bg-secondary border-primary",
      typeTag: "bg-custom-bg-muted text-custom-text-primary",
      unreadIndicator: "bg-primary",
      title: "text-custom-text-primary hover:text-custom-text-secondary",
      timestamp: "text-custom-text-secondary",
      divider: "border-custom-border",
      actionButton: "text-custom-text-secondary",
      markReadButton: "text-custom-text-secondary hover:text-primary",
      deleteButton: "text-custom-text-secondary hover:text-custom-text-destructive",
    }
  };

  return (
    <div className={`notification-group ${themeClasses[theme].group}`}>
      <div className={`notification-date-header ${themeClasses[theme].dateHeader}`}>{date}</div>

      <div className="space-y-2">
        {items.map((item) => (
          <div
            key={item.notification_id}
            className={cn(
              `rounded-lg shadow-sm border p-4 cursor-pointer transition-all duration-200 ${themeClasses[theme].item}`,
              !item.is_read && themeClasses[theme].itemUnread
            )}
          >
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <span className={`px-2 py-1 rounded-md typography-caption ${themeClasses[theme].typeTag}`}>
                  {handleTypeDisplay(item)}
                </span>
                {!item.is_read && (
                  <span className={`w-2 h-2 rounded-full animate-pulse ${themeClasses[theme].unreadIndicator}`} />
                )}
              </div>
            </div>

            <h3
              onClick={() => onNotificationClick(item)}
              className={`typography-body-sm font-weight-medium cursor-pointer hover:underline mb-2 ${themeClasses[theme].title}`}
            >
              {item.data.message}
            </h3>

            <div className={`flex justify-between items-center pt-2 border-t ${themeClasses[theme].divider}`}>
              <span className={`typography-caption ${themeClasses[theme].timestamp}`}>
                {formatUTCToLocal(item.created_at)}
              </span>
              <div className="flex items-center gap-2">
                {!item.is_read && (
                  <IconButton
                    icon={<CheckCircle className="w-4 h-4" />}
                    variant="ghost"
                    onClick={() =>
                      markRead(item.notification_id, handleValueChange)
                    }
                    tooltip="Mark as read"
                    className={themeClasses[theme].markReadButton}
                  />
                )}
                <IconButton
                  icon={<Trash2 className="w-4 h-4" />}
                  variant="ghost"
                  onClick={() =>
                    openDeleteModal(item.notification_id, item.receiver_id)
                  }
                  tooltip="Delete notification"
                  className={themeClasses[theme].deleteButton}
                />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
