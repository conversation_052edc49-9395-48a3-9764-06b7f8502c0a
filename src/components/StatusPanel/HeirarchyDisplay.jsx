import React, { useState, useEffect, useContext, useRef } from 'react';
import Image from 'next/image';
import layers from "../../../public/images/layers.png";
import { FaChevronDown, FaChevronRight, FaEye, FaTimes } from 'react-icons/fa';
import { useRouter, useParams } from 'next/navigation';
import { ExecutionContext } from '../Context/ExecutionContext';
import { getNodeVersions } from '@/utils/api';
import { Dialog, DialogContent } from "@mui/material";
import VersionRender from "@/components/UiMetadata/VersionRender";
import Badge from '../UIComponents/Badge/Badge';
import Cookies from 'js-cookie';
import EmptyStateView from '../Modal/EmptyStateModal';
import { buildProjectUrl } from '@/utils/navigationHelpers';
const HierarchyDisplay = ({ formattedResponse, configurationStatus }) => {
  const [expandedNodes, setExpandedNodes] = useState({});
  const [finalFormat, setFinalFormat] = useState({});
  const [printed, setPrinted] = useState([]);
  const params = useParams();
  const router = useRouter();
  const { configLabel, autoNavigateEnabled } = useContext(ExecutionContext)
  const tenantId = Cookies.get('tenant_id')
  const [content, setContent] = useState(null);
  const [metaData, setMetaData] = useState({})
  const [modalOpen, setModalOpen] = useState(false);
  const [nodeType, setNodeType] = useState(null)
  const [loading, setLoading] = useState(false)
  const projectId = params.projectId;

  const VersionModal = ({ open, onClose, previousVersion, currentVersion }) => {
    const isPreviousVersionEmpty = Object.keys(previousVersion || {}).length === 0;
    const isCurrentVersionEmpty = Object.keys(currentVersion || {}).length === 0;
    return (
      <Dialog open={open} onClose={onClose} maxWidth="2xl" fullWidth>
        <div className="bg-white rounded-lg shadow-lg h-[90vh]  flex flex-col">
          <div className="flex justify-between items-center bg-white text-black p-4 rounded-t-lg border-b">
            <div className="flex items-center gap-2">
              <h2 className="typography-body-lg font-weight-semibold text-gray-900">Version Details</h2>
              <Badge type={nodeType} />
            </div>
            <button className="hover:text-red-600" onClick={onClose} title='Close Modal'>
              <FaTimes />
            </button>
          </div>

          {/* Modal Content */}
          <DialogContent className="p-6 flex-grow overflow-auto">

            <div className="grid grid-cols-2 gap-6 h-full">
              <div className="border border-gray-300 rounded-lg h-full flex flex-col p-4">
                <h3 className="typography-body-lg font-weight-semibold text-gray-900 mb-2">Previous Version</h3>
                <div className=" overflow-auto flex-grow">
                  {loading ? (
                    Array(3)
                      .fill()
                      .map((_, index) => (
                        <div key={index} className="border rounded-md overflow-hidden">
                          <div className="p-4 bg-gray-50 flex justify-between items-center">
                            <div className="h-5 bg-gray-200 rounded w-3/4 animate-pulse"></div>
                            <div className="h-5 w-5 bg-gray-200 rounded-full animate-pulse"></div>
                          </div>
                          <div className="px-4 py-3">
                            <div className="h-4 bg-gray-200 rounded w-full mb-2 animate-pulse"></div>
                            <div className="h-4 bg-gray-200 rounded w-5/6 mb-2 animate-pulse"></div>
                            <div className="h-4 bg-gray-200 rounded w-4/6 animate-pulse"></div>
                          </div>
                        </div>
                      ))
                  ) : isPreviousVersionEmpty ? (
                    <EmptyStateView type="noPreviousVersionFound" />
                  ) :
                    (
                      <VersionRender
                        properties={currentVersion}
                        metadata={metaData}
                        to_skip={["configuration_state", "Type", "Title"]}
                        compareWithProperties={previousVersion}
                        isPreviousVersion={true}

                      />
                    )}
                </div>
              </div>

              {/* Current Version */}
              <div className="border border-gray-300 rounded-lg h-full flex flex-col p-4">
                <h3 className="typography-body-lg font-weight-semibold text-gray-900 mb-2">Current Version</h3>
                <div className="overflow-auto flex-grow">
                  {loading ? (
                    Array(3)
                      .fill()
                      .map((_, index) => (
                        <div key={index} className="border rounded-md overflow-hidden">
                          <div className="p-4 bg-gray-50 flex justify-between items-center">
                            <div className="h-5 bg-gray-200 rounded w-3/4 animate-pulse"></div>
                            <div className="h-5 w-5 bg-gray-200 rounded-full animate-pulse"></div>
                          </div>
                          <div className="px-4 py-3">
                            <div className="h-4 bg-gray-200 rounded w-full mb-2 animate-pulse"></div>
                            <div className="h-4 bg-gray-200 rounded w-5/6 mb-2 animate-pulse"></div>
                            <div className="h-4 bg-gray-200 rounded w-4/6 animate-pulse"></div>
                          </div>
                        </div>
                      ))
                  ) : isCurrentVersionEmpty ? (
                    <EmptyStateView type="noCurentVersionFound" />
                  ) : (
                    <VersionRender
                      properties={currentVersion}
                      metadata={metaData}
                      to_skip={["configuration_state", "Type", "Title"]}
                      compareWithProperties={previousVersion}
                      isPreviousVersion={false}

                    />
                  )}
                </div>
              </div>
            </div>
          </DialogContent>
        </div>
      </Dialog>
    );
  };


  const prevConfigStatusRef = useRef({});

  useEffect(() => {
    if (!autoNavigateEnabled || !configurationStatus) return;
    const prevConfigStatus = prevConfigStatusRef.current;

    Object.keys(configurationStatus).forEach(nodeId => {
      const currentNode = configurationStatus[nodeId];
      const prevNode = prevConfigStatus[nodeId];

      if (!prevNode || !currentNode) return;

      const wasConfiguring =
        prevNode.configuration === 'configuring' ||
        prevNode.architecture_requirement === 'configuring';

      const isNowConfigured =
        currentNode.configuration === 'configured' ||
        currentNode.architecture_requirement === 'configured';

      if (wasConfiguring && isNowConfigured) {
        handleNodeClick(nodeId, currentNode.node_type);
      }
    });

    prevConfigStatusRef.current = JSON.parse(JSON.stringify(configurationStatus));
  }, [configurationStatus, autoNavigateEnabled]);

  useEffect(() => {
    const formatResponse = () => {
      let newPrinted = [];
      let newFinalFormat = {};

      const checkForChildren = (nodeId, level = 1) => {
        if (!formattedResponse[nodeId]) return [];
        const children = formattedResponse[nodeId];

        newFinalFormat[nodeId] = children;
        for (const child of children) {
          if (!newPrinted.includes(child)) {
            newPrinted.push(child);
            checkForChildren(child, level + 1);
          }
        }
      };

      for (const key in formattedResponse) {
        if (newPrinted.includes(key)) continue;

        if (!newFinalFormat[key]) {
          newFinalFormat[key] = formattedResponse[key] || [];
        }

        if (formattedResponse[key]) {
          for (const id of formattedResponse[key]) {
            if (!newPrinted.includes(id)) {
              newPrinted.push(id);
              checkForChildren(id, 1);
            }
          }
        }
      }

      setFinalFormat(newFinalFormat);
      setPrinted(newPrinted);
    };

    formatResponse();
  }, [formattedResponse]);

  const handleOnClick = (node, nodeId) => {
    
    
    let nodeType = node.node_type.toLowerCase();
    let routeNodeType = node.node_type.toLowerCase();

    if (nodeType === 'requirement') { // Check if nodeType is 'requirement'
      // Determine if the selected node has children
      const children = finalFormat[nodeId] || []; // Get children array based on the node's own ID

      if (children.length > 0) {
        routeNodeType = 'Epic'; // If node has children, routeNodeType is 'Epic'
      } else {
        routeNodeType = 'UserStory'; // If node has no children, routeNodeType is 'UserStory'
      }
    } else if (nodeType === 'epic') {
      routeNodeType = 'Epic';
    }

    
    const routes = {
      project: buildProjectUrl(id, 'overview'),
      workitem: buildProjectUrl(id, `workitems/${nodeId}`),
      requirement: buildProjectUrl(id, `requirements/${routeNodeType}/${nodeId}`),
      architecture: buildProjectUrl(id, `architecture/high-level/${nodeId}`),
    };

    if (routes[nodeType]) {
      router.push(routes[nodeType]);
    } else {
      
    }
  };

  const checkIfConfigured = (node) => {
    let values = Object.values(node);
    let statusToCheck = ['configuring', 'failed'];
    return !values.some((value) => statusToCheck.includes(value));
  }

  const handleToggle = (e, nodeId) => {
    e.stopPropagation();
    setExpandedNodes(prev => ({
      ...prev,
      [nodeId]: !prev[nodeId]
    }));
  };

  const fetchVersionData = async (nodeId, nodeType) => {
    setLoading(true)
    setNodeType(nodeType)
    setModalOpen(true);
    try {
      const response = await getNodeVersions(projectId, nodeId, nodeType, tenantId);
      if (response) {
        setContent(response)
        setMetaData(response?.reconfigured_version?.ui_metadata || response?.base_version?.ui_metadata || []);
      }
    } catch (error) {
      
    } finally {
      setLoading(false)
    }
  };

  const updated = content?.reconfigured_version?.properties || {};
  const old = content?.base_version?.properties || {};
  const handleNodeClick = (nodeId, nodeType) => {
    let base = buildProjectUrl(projectId, '').replace('/overview', '');
    let node = configurationStatus[nodeId] || {};
    let parent_id = node.parent_node_id;
    switch (nodeType) {
      case 'Project':
        router.push(buildProjectUrl(projectId, 'overview'));
        break;
      case 'WorkItem':
        if (parseInt(parent_id) === parseInt(projectId)) {
          router.push(buildProjectUrl(projectId, 'workitems'));
        } else {
          router.push(buildProjectUrl(projectId, `workitems/${nodeId}`));
        }
        break;
      case 'Requirement':
        if (parseInt(parent_id) === parseInt(projectId)) {
          router.push(buildProjectUrl(projectId, 'requirements'));
        } else {
          router.push(buildProjectUrl(projectId, `requirements/${nodeType}/${nodeId}`));
        }
        break;
      case 'Epic':
      case 'Task':
      case 'UserStory':
        router.push(buildProjectUrl(projectId, `requirements/${nodeType}/${nodeId}`));
        break;
      case 'SystemContext':
        router.push(buildProjectUrl(projectId, 'architecture/system-context'));
        break;
      case 'Container':
        if (parseInt(parent_id) === parseInt(projectId)) {
          router.push(buildProjectUrl(projectId, 'architecture/container'));
        } else {
          router.push(buildProjectUrl(projectId, `architecture/container/${nodeId}`));
        }
        break;
      case 'Architecture':
        if (parseInt(parent_id) === parseInt(projectId)) {
          router.push(buildProjectUrl(projectId, 'architecture/component'));
        } else {
          router.push(buildProjectUrl(projectId, `architecture/component/${nodeId}`));
        }
        break;
      case 'Component':
        if (parseInt(parent_id) === parseInt(projectId)) {
          router.push(buildProjectUrl(projectId, 'architecture/component'));
        } else {
          router.push(buildProjectUrl(projectId, `architecture/component/${nodeId}`));
        }
        break;
      case 'ArchitecturalRequirement':
        router.push(buildProjectUrl(projectId, 'architecture/architecture-requirement'));
        break;
      case 'Interface':
        router.push(buildProjectUrl(projectId, `architecture/interfaces/${nodeId}`));
        break;
      case 'Design':
        router.push(buildProjectUrl(projectId, `architecture/design/${parent_id}`));
        break;
      default:

    }
  };

  const renderHierarchy = (nodeId, level = 0) => {
    const node = configurationStatus[nodeId] || {};
    const children = finalFormat[nodeId] || [];
    const isConfigured = checkIfConfigured(node);
    const isExpanded = expandedNodes[nodeId] !== false;
    
    return (
      <div key={nodeId} className={`relative ${level > 0 ? 'pl-6 pt-2' : 'pt-1'}`}>
        <div 
          className={`flex items-center rounded-lg hover:bg-gray-50 transition-all duration-200 
          ${level > 0 ? 'border-l-2 border-gray-200' : ''}`}
        >
          {level > 0 && (
            <div className="absolute left-0 top-1/2 w-6 h-px bg-gray-200"></div>
          )}
          
          {/* Expandable Toggle Button */}
          <div
            className={`flex items-center justify-center w-8 h-8 rounded-l-lg
              ${children.length > 0 ? 'cursor-pointer hover:bg-gray-100' : ''}`}
            onClick={(e) => children.length > 0 && handleToggle(e, nodeId)}
            aria-label={isExpanded ? "Collapse" : "Expand"}
            role={children.length > 0 ? "button" : "presentation"}
            tabIndex={children.length > 0 ? 0 : -1}
          >
            {children.length > 0 && (
              isExpanded ? (
                <FaChevronDown 
                  className="text-gray-500 transition-transform duration-200 transform" 
                  aria-hidden="true"
                />
              ) : (
                <FaChevronRight 
                  className="text-gray-500 transition-transform duration-200 transform" 
                  aria-hidden="true"
                />
              )
            )}
          </div>
          
          {/* Node Content */}
          <div
            className="flex items-center flex-grow p-3 cursor-pointer hover:bg-gray-50 rounded-r-lg transition-colors duration-200"
            onClick={() => handleNodeClick(nodeId, node.node_type)}
            role="button"
            aria-expanded={isExpanded}
            tabIndex={0}
          >
            {/* Icon for root nodes */}
            {level === 0 && (
              <div className="flex-shrink-0 mr-3">
                <Image src={layers} className="w-6 h-6" alt="" aria-hidden="true" />
              </div>
            )}
            
            {/* Node details */}
            <div className="flex-grow">
            <h2 className="text-md font-weight-semibold flex items-center">
            {node.title || `Node ${nodeId}`}
            {configLabel === "re-config" && isConfigured && (
             <div className="flex items-center space-x-2">
              <FaEye className="ml-2 text-gray-600"  onClick={(e) => {
                e.stopPropagation();
                fetchVersionData(nodeId, node.node_type);
              }} title='Click to see the version details' />
{/* 
{showBlinkingMessage[nodeId] && (
        <div className="flex items-center space-x-1 bg-primary-100 text-primary-700 typography-caption font-weight-medium px-2.5 py-1 rounded-full border border-primary-400 animate-pulse">
          <FaInfoCircle className="text-primary" />
          <span>Click the eye icon to see details</span>
        </div>
      )} */}
             
              </div>
            )}
          </h2>
              <div className="flex items-center justify-between mt-1">
                <span className="typography-body-sm text-gray-500">{node.node_type || 'Unknown'}</span>
                
                <div className="flex items-center space-x-2">
                  {isConfigured ? (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-green-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-primary" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                  )}
                  
                  <div className="w-16 bg-gray-100 rounded-full h-1.5" aria-hidden="true">
                    <div
                      className={`h-full rounded-full ${isConfigured ? 'bg-green-500' : 'bg-primary'}`}
                      style={{ width: `${isConfigured ? 100 : 50}%` }}
                    ></div>
                  </div>
                  
                  <span className="typography-caption text-gray-500" aria-label={isConfigured ? "Configured" : "In progress"}>
                    {isConfigured ? "100%" : "50%"}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Children nodes */}
        {isExpanded && children.length > 0 && (
          <div className="ml-4 mt-1 space-y-1">
            {children.map(childId => renderHierarchy(childId, level + 1))}
          </div>
        )}
      </div>
    );
  };

  // Check if there's any data to display
  const hasData = Object.keys(finalFormat).length > 0 || Object.keys(configurationStatus || {}).length > 0;
  const nodesToRender = Object.keys(finalFormat).filter(nodeId => !printed.includes(nodeId));

  return (
    <>
      <div className="p-4 h-full flex flex-col">
        {hasData && nodesToRender.length > 0 ? (
          <div className="space-y-2">
            {nodesToRender.map(nodeId => renderHierarchy(nodeId))}
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center min-h-[300px]">
            <EmptyStateView
              type="statusPanel"
              className="max-w-md mx-auto"
            />
          </div>
        )}
      </div>
      <VersionModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        previousVersion={old}
        currentVersion={updated}
        metaData={metaData}
      />
    </>
  );
};

export default HierarchyDisplay;