import React, { useEffect, useState, useContext } from "react";
import PropTypes from "prop-types";
import { getTaskUpdatesNonStreaming } from "@/utils/configureAPI";
import HierarchyDisplay from "./HeirarchyDisplay";
import { LoadingSkeleton } from "../Loaders/Loading";
import { useWebSocket } from "../Context/WebsocketContext";
import { ExecutionContext } from "../Context/ExecutionContext";
import StatusDisplay from "./StatusDisplay";
import EmptyStateView from "../Modal/EmptyStateModal";

// Global WebSocket reference for type checking
const WebSocket = global.WebSocket || window.WebSocket;

const TaskDisplay = ({
  taskId,
  showStatusDisplay = false,
  showStatusDisplayProps = {},
}) => {
  const [taskUpdates, setTaskUpdates] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [statusData, setStatusData] = useState({});
  const { connectToSession, disconnectFromSession, getConnection } =
    useWebSocket();
  const { configStatus, setConfigStatus, setIsConnected } =
    useContext(ExecutionContext);

  const checkStatusCommand = {
    type: "client",
    task_id: taskId,
    command: "status_check",
  };

  useEffect(() => {
    const fetchTaskUpdates = async () => {
      if (!taskId) return;
      setIsLoading(true);
      setTaskUpdates([]);
      setStatusData({});
      try {
        connectToSession(taskId);
        const updates = await getTaskUpdatesNonStreaming(taskId);
        
        setTaskUpdates(updates);
        checkTaskStatus();

        let newConfigStatus = { ...(configStatus[taskId] || {}) };

        // Get the WebSocket connection safely
        try {
          const wsConnection = getConnection ? getConnection(taskId) : null;

          // Only set up event handlers if the connection exists
          if (wsConnection && wsConnection.readyState !== WebSocket.CLOSED) {
            // For connections that are already open, we don't need to set onopen
            if (wsConnection.readyState !== WebSocket.OPEN) {
              wsConnection.onopen = () => {
                checkTaskStatus();
              };
            } else {
              // Connection is already open

              checkTaskStatus();
            }

            wsConnection.onmessage = (event) => {
              const data = JSON.parse(event.data);
              if (data.type === "auto_configuration_update" && data.data) {
                const newTaskUpdates = JSON.parse(data.data);
                setTaskUpdates(newTaskUpdates);
                newConfigStatus = {
                  ...newConfigStatus,
                  ...(newTaskUpdates.status && {
                    task_status: newTaskUpdates.status,
                  }),
                  ...(newTaskUpdates.progress && {
                    progress: newTaskUpdates.progress,
                  }),
                  ...(newTaskUpdates.configuration_status && {
                    configuration_status: newTaskUpdates.configuration_status,
                  }),
                  ...(newTaskUpdates.formatted_response && {
                    formatted_response: newTaskUpdates.formatted_response,
                  }),
                  ...(newTaskUpdates.title && { title: newTaskUpdates.title }),
                  ...(newTaskUpdates.start_time && {
                    start_time: newTaskUpdates.start_time,
                  }),
                  ...(newTaskUpdates.eta_info && {
                    eta_info: newTaskUpdates.eta_info,
                  }),
                };
                setConfigStatus((prev) => ({
                  ...prev,
                  [taskId]: newConfigStatus,
                }));
                setIsConnected(true);
              }
              if (data.type === "auto_configuration_status" && data.data) {
                setStatusData(data.data);
                setIsConnected(true);
              }
            };
          } else {
          }
        } catch (error) {}
      } catch (error) {
      } finally {
        setIsLoading(false);
      }
    };

    fetchTaskUpdates();
    return () => taskId && disconnectFromSession(taskId);
  }, [taskId]);

  useEffect(() => {
    // Extract the current task status from taskUpdates
    const currentStatus = taskUpdates.status?.toLowerCase();
    // Only proceed if we have a valid status and taskId
    if (currentStatus && taskId) {
      // Check if status is complete or failed
      if (currentStatus === "complete" || currentStatus === "failed") {
        // Create a verification function
        const verifyFinalStatus = async () => {
          try {
            // Fetch the latest status directly from the server
            const latestUpdates = await getTaskUpdatesNonStreaming(taskId);
            // If the status doesn't match, trigger another refresh
            if (latestUpdates.status?.toLowerCase() !== currentStatus) {
              checkTaskStatus();
            }
          } catch (error) {}
        };
        // Execute verification after a short delay
        const timeoutId = setTimeout(verifyFinalStatus, 2000);
        // Cleanup timeout if component unmounts
        return () => clearTimeout(timeoutId);
      }
    }
  }, [taskUpdates?.status]); // Only run when status changes

  const checkTaskStatus = () => {
    try {
      if (taskId && getConnection) {
        const connection = getConnection(taskId);
        if (connection && connection.readyState === WebSocket.OPEN) {
          connection.send(JSON.stringify(checkStatusCommand));
        } else {
        }
      }
    } catch (error) {}
  };

  if (!taskId) {
    return (
      <div className="flex-1 overflow-auto h-full flex items-center justify-center">
        <EmptyStateView
          type="status"
          className="max-w-md mx-auto"
        />
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-auto h-full">
      {isLoading ? (
        <LoadingSkeleton />
      ) : (
        <>
          {showStatusDisplay && (
            <StatusDisplay
              taskData={taskUpdates}
              isConnected={!!statusData}
              {...showStatusDisplayProps}
            />
          )}
          <HierarchyDisplay
            configurationStatus={taskUpdates.configuration_status}
            formattedResponse={taskUpdates.formatted_response}
          />
        </>
      )}
    </div>
  );
};

TaskDisplay.propTypes = {
  taskId: PropTypes.string.isRequired,
  showStatusDisplay: PropTypes.bool,
  showStatusDisplayProps: PropTypes.object,
};

export default TaskDisplay;
