import React, { useState, useContext, useEffect } from 'react';
import { Activity, AlertTriangle, ChevronDown, ChevronUp, CodeXml, Flame, LeafyGreen, LucideIcon, Siren, Eye, Trash2 } from 'lucide-react';
import Pagination from '../UIComponents/Paginations/Pagination';
import { DynamicButton } from '../UIComponents/Buttons/DynamicButton';
import DeleteProjectModal from "@/components/Modal/DeleteProjectModal";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { deleteQueryDiscussion } from '@/utils/api';
import {deleteMaintenanceTask} from "@/utils/batchAPI"
import EmptyStateView from "@/components/Modal/EmptyStateModal";
type Status = 'SUCCESS' | 'FAILED' | 'RUNNING' | string;
type Priority = 'Critical' | 'High' | 'Medium' | 'Low';

interface StatusBadgeProps {
  status: Status;
}

interface PriorityBadgeProps {
  priority: string | number | undefined;
  isDropdown?: boolean;
}

interface HeaderProps {
  key: string;
  label: string;
  actionLabel?: string;
  render?: (value: any, row: any) => React.ReactNode;
}

interface SortConfig {
  key: string | null;
  direction: 'ascending' | 'descending';
}

interface PriorityConfig {
  icon: LucideIcon;
  styles: string;
  iconColor: string;
  animation?: string;
  pulse?: boolean;
}

interface TableComponentProps {
  totalCount?: number,
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (size: number) => void;
  headers: HeaderProps[];
  data: Array<{ [key: string]: any }>;
  onRowClick: (
    id: string | { [key: string]: any },
    title?: string,
    description?: string,
    type?: string
  ) => void;
  sortableColumns?: { [key: string]: boolean };
  itemsPerPage?: number;
  onActionClick?: (id: string | number) => void;
  title?: string;
  component?: string;
  isLoading?: boolean;
  customPage?: number;
  emptyStateType?: string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  const getStatusConfig = (status: string) => {
    switch (status.toUpperCase()) {
      case 'SUBMITTED':
        return { text: 'Pending', bgColor: 'bg-yellow-100', textColor: 'text-yellow-800', dotColor: 'bg-yellow-500' };
      case 'FAILED':
        return { text: 'Failed', bgColor: 'bg-red-100', textColor: 'text-red-800', dotColor: 'bg-red-500' };
      case 'RUNNING':
      case 'IN_PROGRESS':
        return { text: 'Running', bgColor: 'bg-primary-100', textColor: 'text-primary-800', dotColor: 'bg-primary-500' };
      case 'STOPPED':
      case 'CANCELLED':
      case 'COMPLETE':
        return { text: 'Completed', bgColor: 'bg-green-100', textColor: 'text-green-800', dotColor: 'bg-green-500' };
      default:
        return { bgColor: 'bg-gray-100', textColor: 'text-gray-800', dotColor: 'bg-gray-500' };
    }
  };

  const config = getStatusConfig(status);

  return (
    <div className={`inline-flex items-center px-1.5 py-0.5 rounded ${config.bgColor} min-w-0 max-w-full`}>
      <div className={`w-1.5 h-1.5 rounded-full mr-1 flex-shrink-0 ${config.dotColor}`} />
      <span className={`text-xs font-medium ${config.textColor} truncate`}>
        {config.text ? config.text :status}
      </span>
    </div>
  );
};

const PriorityBadge: React.FC<PriorityBadgeProps> = ({ priority, isDropdown = false }) => {
  const normalizedPriority = (() => {
    if (typeof priority === "number") return 'Low';
    return priority;
  })();

  const priorityConfig: Record<Priority | 'default', PriorityConfig> = {
    'Critical': {
      icon: Siren,
      styles: 'bg-semantic-red-100 text-semantic-red-900 border-semantic-red-300 shadow-md ring-2 ring-semantic-red-400 ring-opacity-50',
      iconColor: 'text-semantic-red-700',
      animation: 'animate-pulse'
    },
    'High': {
      icon: Flame,
      styles: 'bg-semantic-red-100 text-semantic-red-800 border-semantic-red-200 shadow-sm',
      iconColor: 'text-semantic-red-600',
      pulse: true
    },
    'Medium': {
      icon: Activity,
      styles: 'bg-semantic-yellow-100 text-semantic-yellow-800 border-semantic-yellow-200 shadow-sm',
      iconColor: 'text-semantic-yellow-600',
      animation: 'hover:scale-105 transition-transform duration-200'
    },
    'Low': {
      icon: LeafyGreen,
      styles: 'bg-semantic-green-100 text-semantic-green-800 border-semantic-green-200 shadow-sm',
      iconColor: 'text-semantic-green-600'
    },
    'default': {
      icon: AlertTriangle,
      styles: 'bg-semantic-gray-100 text-semantic-gray-600 border-semantic-gray-200 shadow-sm',
      iconColor: 'text-semantic-gray-500'
    }
  };

  const config = priorityConfig[normalizedPriority as Priority] || priorityConfig.default;
  const Icon = config.icon;

  return (
    <span
      className={`
      inline-flex items-center px-2 py-1 typography-caption font-weight-medium
      rounded-full border whitespace-nowrap ${config.styles} ${config.pulse ? 'animate-pulse' : ''}
      ${config.animation || ''} cursor-default
    `}
    >
      <Icon className={`w-3 h-3 mr-1.5 ${config.iconColor}`} />
      {normalizedPriority || 'Not Set'}
      {isDropdown && <ChevronDown className="w-3 h-3 ml-1" />}
    </span>
  );
};

const TableSkeleton: React.FC<{ headers: HeaderProps[] }> = ({ headers }) => {
  return (
    <div className="w-full">
      <div className="animate-pulse">
        <div className="h-12 bg-gray-100 mb-4 rounded-t-lg" />
        {[...Array(5)].map((_, index) => (
          <div key={index} className="h-16 border-b border-gray-100">
            {headers.map((header) => (
              <div key={header.key} className="flex items-center h-full px-6 space-x-4">
                <div className="h-4 bg-gray-100 rounded w-1/3" />
              </div>
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};



const TableComponent: React.FC<TableComponentProps> = ({
  totalCount,
  onPageChange,
  onPageSizeChange,
  headers,
  data: initialData = [],
  onRowClick,
  sortableColumns = {},
  itemsPerPage,
  onActionClick,
  title,
  component,
  customPage,
  isLoading = false,
  emptyStateType = "discussions"
}) => {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ key: null, direction: 'ascending' });
  const [data, setData] = useState(initialData);

  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(itemsPerPage? itemsPerPage : localStorage.getItem("preferredPageSize")? Number(localStorage.getItem("preferredPageSize")): 20);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [discussionId, setDiscussionId] = useState<any>()
  const { showAlert } = useContext(AlertContext);

  useEffect(() => {
      setData(initialData);
  }, [initialData]);

  // Calculate paginated data
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedData = data.slice(startIndex, endIndex);
  const totalItems = data.length;
  const pageCount = Math.ceil(totalCount? totalCount / pageSize : totalItems / pageSize);

  const handleDelete = async (id: string | number) => {
    try {
      setIsDeleting(true);
      let response;
      if (component === "query") {
        response = await deleteQueryDiscussion(id);
      }
      else{
        response = await deleteMaintenanceTask(id);
      }
      const updatedData = data.filter(item =>
        item.id !== id && item.fullId !== id
      );
      // Success case
      if (response?.message === 'Task deleted successfully' || response) {
        if(updatedData.length === 0 && currentPage > 1) {
          handlePageChange(currentPage - 1);
        } else {
        handlePageChange(currentPage);
        }
        showAlert("Discussion deleted successfully", "success");
        setData(updatedData)
        setIsDeleteModalOpen(false);
      } else {
        // If we have a specific error message in the response
        const errorMessage = response.data?.message || response.statusText || 'Delete operation failed';
        throw new Error(errorMessage);
      }
    } catch (error: any) {
      // Log the full error for debugging


      // Show a user-friendly error message
      const errorMessage = error.response?.data?.message || error.message || 'An unexpected error occurred';
      showAlert(`Failed to delete the Discussion: ${errorMessage}`, "danger");
    } finally {
      setIsDeleting(false);
      setIsDeleteModalOpen(false);
    }
  };
  const confirmAndDelete = (id : number|string) => {
    setIsDeleteModalOpen(true);
    setDiscussionId(id)
  };

  const cancelDelete = () => {
    setIsDeleteModalOpen(false);
  };

  const proceedWithDelete = async () => {
    await handleDelete(discussionId);
  };

  const TableHeader: React.FC<{ header: HeaderProps }> = ({ header }) => (
    <th
      key={header.key}
      onClick={() => sortableColumns[header.key] && requestSort(header.key)}
      className={`px-4 py-2 text-xs font-semibold text-semantic-gray-600 uppercase tracking-wide ${['id', 'action', 'status', 'type'].includes(header.key) ? 'text-center' : ''} ${sortableColumns[header.key] ? 'cursor-pointer hover:bg-semantic-gray-100 transition-colors' : ''
        }`}
      style={{ minWidth: header.key.toLowerCase() === 'title' ? '200px' : 'auto' }}
    >
      <div className={`flex items-center space-x-1 ${['id', 'action', 'status', 'type'].includes(header.key) ? 'justify-center' : ''}`}>
        <span>{header.label}</span>
        {sortableColumns[header.key] && (
          <div className="flex flex-col">
            <ChevronUp
              className={`h-3 w-3 ${sortConfig.key === header.key && sortConfig.direction === 'ascending'
                ? 'text-primary'
                : 'text-[#A1A9B8]'
                }`}
            />
            <ChevronDown
              className={`h-3 w-3 ${sortConfig.key === header.key && sortConfig.direction === 'descending'
                ? 'text-primary'
                : 'text-[#A1A9B8]'
                }`}
            />
          </div>
        )}
      </div>
    </th>
  );

  const requestSort = (key: string) => {
    if (!sortableColumns[key]) return;
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'ascending' ? 'descending' : 'ascending'
    }));
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    onPageChange && onPageChange(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    onPageSizeChange && onPageSizeChange(newPageSize);
    setCurrentPage(1);
  };

  return (
    <div className="w-full bg-white border border-[#E9EDF5] rounded-lg shadow h-full overflow-auto">
      {
        title && (
          <div className="bg-gray-100 px-4 py-2 font-weight-medium text-gray-700">
            {title}
          </div>
        )
      }
      <div className={`${isLoading?"":"hidden"}`} >
        <TableSkeleton headers={headers} />
      </div>
      <div className={`h-full ${isLoading?"hidden":""}`} >
        <div className="overflow-x-auto w-full h-full">
            {isLoading ? (
              <TableSkeleton headers={headers} />
            ) : paginatedData.length === 0 ? (
              <EmptyStateView className='min-h-[400px]' type={emptyStateType} onClick={() => {
                // Reset any filters or search that might be applied
                if (onPageChange) {
                  onPageChange(1);
                }
              }}/>
            ) : (
              <div className='max-h-[100%]'>
                <table className="w-full border-collapse divide-y divide-semantic-gray-200 max-h-[500px] overflow-auto">
                  <thead className="bg-semantic-gray-50 border-b border-semantic-gray-200">
                    <tr>
                      {headers.map((header) => (
                        <TableHeader key={header.key} header={header} />
                      ))}
                      {(component && ["code-maintenance", "code-generation", "query", "deep-analysis"].includes(component)) && (
                        <th className="px-4 py-2 text-xs font-semibold text-semantic-gray-600 uppercase text-center tracking-wide">
                          Actions
                        </th>
                      )}
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-semantic-gray-200">
                    {paginatedData.map((row, index) => (
                      <tr
                        key={row.id || index}
                        // onClick={() => onRowClick?.(row.id)}
                        // onClick={() => {

                        //   if (component === "architecture-requirements") {
                        //     onRowClick?.(row.id, row.title, row.description, row.type);
                        //   } else if (component === "architecture-child-requirements") {
                        //     onRowClick?.(row);
                        //   } else if (row.type === 'Interface' || row.type === 'Design' || component === "Design Details" || component === "Interface") {
                        //     onRowClick?.(row.id, row.type);
                        //   } else if (component == "code-maintenance") {
                        //     // onRowClick(row);
                        //   }
                        //   else {
                        //     // onRowClick?.(row.id);
                        //   }
                        // }}
                        className="transition-colors hover:bg-semantic-gray-50 cursor-pointer"
                      >
                        {headers.map(header => (
                          <td
                            key={header.key}
                            className={`px-4 py-2 ${['id', 'action', 'status', 'type'].includes(header.key) ? 'text-center' : ''} ${header.key === 'Priority' ? 'text-center' : ''}`}
                            style={{
                              maxWidth: header.key.toLowerCase() === 'description' ? '100px' : 'auto'
                            }}
                          >
                            {header.render ? (
                              header.render(row[header.key], row)
                            ) : header.key === 'status' ? (
                              <StatusBadge status={row[header.key]} />
                            ) : header.key === 'action' ? (
                              <DynamicButton
                                type="submit"
                                variant="primaryOutline"
                                icon={CodeXml}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onActionClick?.(row.id);
                                }}
                                text={header.actionLabel || 'Action'}
                              />
                            ) : header.key === 'type' ? (
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-semantic-gray-100 text-semantic-gray-800 border border-semantic-gray-200">
                                {row[header.key]}
                              </span>
                            ) : header.key === 'Priority' ? (
                              <PriorityBadge priority={row[header.key]} />
                            ) : (
                              <div className={`text-sm text-semantic-gray-900 custom-table-text-ellipsis ${header.key.toLowerCase() === "title" ? "hover:text-primary cursor-pointer transition-colors font-medium" : ""}`}
                                title={header.key.toLowerCase() === "description" ? row[header.key] : undefined}
                                onClick={header.key.toLowerCase() === "title" ? () => {
                                  if (component === "architecture-requirements") {
                                    onRowClick(row.id, row.title, row.description, row.type);
                                  } else {
                                    onRowClick(row.id);
                                  }
                                } : () => {}}
                              >
                                {row[header.key]}
                              </div>
                            )}
                          </td>
                        ))}
                        {component && ["code-maintenance", "code-generation", "query", "deep-analysis"].includes(component) && (
                          <td className="px-6 py-2.5 text-center">
                            <div className="flex items-center justify-center space-x-4">
                              <button
                                onClick={() => {
                                  if (component && ["code-maintenance", "query", "deep-analysis"].includes(component)) {
                                    onRowClick?.(row);
                                  } else if (component === "code-generation") {
                                    onRowClick?.(row.id);
                                  }
                                }}
                                className="text-gray-500 hover:text-primary transition-colors"
                                title="View"
                              >

                                <Eye size={20} />
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (component === "code-maintenance" || component === "deep-analysis" ){
                                  confirmAndDelete(row.fullId);
                                }
                                else if (component === "code-generation"|| component === "query") {
                                  confirmAndDelete(row.id);
                                }
                                }}
                                className="text-gray-500 hover:text-red-600 transition-colors"
                                title="Delete"
                              >
                                <Trash2 size={20} />
                              </button>
                            </div>
                          </td>
                        )}
                      </tr>
                    ))}
                  </tbody>
                </table>

              </div>
            )}
        </div>
      </div>

        {paginatedData.length > 0 && !isLoading && (
          <Pagination
            currentPage={customPage? customPage: currentPage}
            pageCount={pageCount}
            pageSize={pageSize}
            totalItems={totalCount? totalCount : totalItems}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
          />
        )}

      {isDeleteModalOpen && (
        <DeleteProjectModal
          isOpen={isDeleteModalOpen}
          onClose={cancelDelete}
          onDelete={proceedWithDelete}
          isDeleting={isDeleting}
          type="discussion"
        />
      )}
    </div>
  );
};

export default TableComponent;