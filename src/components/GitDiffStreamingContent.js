import React, { useState, useEffect, useRef, useCallback } from "react";
import { FileCode, Copy, MoreVertical } from "lucide-react";

export default function GitLineDiffViewer({ diff }) {
  const [currentLines, setCurrentLines] = useState([]);
  const [isComplete, setIsComplete] = useState(false);
  const containerRef = useRef(null);
  const speed = 100;
  const scrollToBottom = useCallback(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, []);

  useEffect(() => {
    if (!diff) return;

    const diffLines = diff
      .split("\n")
      .filter((line) => line !== null && line !== undefined)
      .map((line) => line.trim());

    setCurrentLines([]);
    setIsComplete(false);

    let currentIndex = 0;

    const addNextLine = () => {
      if (currentIndex < diffLines.length) {
        setCurrentLines((prev) => [...prev, diffLines[currentIndex]]);
        currentIndex++;
        timeoutId = setTimeout(addNextLine, speed);
        setTimeout(scrollToBottom, 0);
      } else {
        setIsComplete(true);
      }
    };

    let timeoutId = setTimeout(addNextLine, speed);

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [diff, scrollToBottom]);

  const handleReplay = () => {
    setCurrentLines([]);
    setIsComplete(false);
  };

  function processLines(lines) {
    let oldLineNumber = 1;
    let newLineNumber = 1;
    let processed = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const nextLine = i < lines.length - 1 ? lines[i + 1] : null;

      if (!line) continue;

      if (
        line.startsWith("diff") ||
        line.startsWith("index") ||
        line.startsWith("---") ||
        line.startsWith("+++")
      ) {
        processed.push({
          type: "meta",
          content: line,
          oldNum: null,
          newNum: null,
        });
        continue;
      }

      if (line.startsWith("@@")) {
        const match = line.match(/@@ -(\d+),?\d* \+(\d+),?\d* @@/);
        if (match) {
          oldLineNumber = parseInt(match[1]);
          newLineNumber = parseInt(match[2]);
        }
        processed.push({
          type: "hunk",
          content: line,
          oldNum: null,
          newNum: null,
        });
        continue;
      }

      // Check for modified content (removed followed by added)
      if (line.startsWith("-") && nextLine?.startsWith("+")) {
        processed.push({
          type: "modified",
          oldContent: line.substring(1),
          newContent: nextLine.substring(1),
          oldNum: oldLineNumber++,
          newNum: newLineNumber++,
        });
        i++; // Skip next line since we've processed it
        continue;
      }

      if (line.startsWith("+")) {
        processed.push({
          type: "added",
          content: line.substring(1),
          oldNum: null,
          newNum: newLineNumber++,
        });
      } else if (line.startsWith("-")) {
        processed.push({
          type: "removed",
          content: line.substring(1),
          oldNum: oldLineNumber++,
          newNum: null,
        });
      } else {
        processed.push({
          type: "context",
          content: line,
          oldNum: oldLineNumber++,
          newNum: newLineNumber++,
        });
      }
    }

    return processed;
  }

  if (!diff) return null;

  const processedLines = processLines(currentLines);

  return (
    <div className="w-full  shadow-lg border rounded-lg overflow-x-auto overflow-y-hidden">
      <div className="flex items-center justify-between p-3 bg-gray-50 border-b">
        <div className="flex items-center space-x-3">
          <FileCode className="w-4 h-4 text-gray-500" />
          <span className="typography-caption px-2 py-0.5 bg-yellow-50 text-yellow-700 border border-yellow-200 rounded">
            CHANGED
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <button className="p-1 hover:bg-gray-100 rounded">
            <Copy className="w-4 h-4 text-gray-500" />
          </button>
          <button className="p-1 hover:bg-gray-100 rounded">
            <MoreVertical className="w-4 h-4 text-gray-500" />
          </button>
        </div>
      </div>

      <div className="relative">
        <div
          ref={containerRef}
          className="overflow-x-auto overflow-y-auto typography-body-sm max-h-[65vh] main-content-area"
        >
          <table className="w-full border-collapse">
            <tbody>
              {processedLines.map((line, idx) => {
                const className =
                  line.type === "added"
                    ? "bg-green-50"
                    : line.type === "removed"
                    ? "bg-red-50"
                    : line.type === "modified"
                    ? "bg-yellow-50"
                    : "hover:bg-gray-50";

                const icon =
                  line.type === "added"
                    ? "+"
                    : line.type === "removed"
                    ? "-"
                    : line.type === "modified"
                    ? "~"
                    : " ";

                const iconClass =
                  line.type === "added"
                    ? "text-green-600"
                    : line.type === "removed"
                    ? "text-red-600"
                    : line.type === "modified"
                    ? "text-yellow-600"
                    : "text-gray-400";

                return (
                  <tr
                    key={idx}
                    className={`${className} animate-fadeIn`}
                    style={{
                      animationDelay: `${idx * 0.1}s`,
                      opacity: 0,
                      animation: "fadeIn 0.3s ease-in forwards",
                    }}
                  >
                    <td className="select-none w-8 px-2 py-1 text-right text-gray-400 border-r border-gray-200 bg-gray-50">
                      {line.oldNum || " "}
                    </td>
                    <td className="select-none w-8 px-2 py-1 text-right text-gray-400 border-r border-gray-200 bg-gray-50">
                      {line.newNum || " "}
                    </td>
                    <td className="w-8 text-center border-r border-gray-200">
                      <span className={iconClass}>{icon}</span>
                    </td>
                    <td className="pl-4 pr-4 py-1 whitespace-pre-wrap">
                      {line.type === "modified" ? (
                        <div>
                          <div className="text-red-600 bg-red-50 px-1 rounded">
                            -{line.oldContent}
                          </div>
                          <div className="text-green-600 bg-green-50 px-1 rounded mt-1">
                            +{line.newContent}
                          </div>
                        </div>
                      ) : (
                        line.content
                      )}
                    </td>
                  </tr>
                );
              })}
              {!isComplete && (
                <tr>
                  <td colSpan="4" className="pl-4 py-1">
                    <span className="animate-pulse">▊</span>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        {!isComplete && (
          <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent" />
        )}
      </div>

      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(-10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </div>
  );
}
