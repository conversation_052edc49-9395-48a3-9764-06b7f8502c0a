import React, { useState, useMemo, useEffect, useRef } from 'react';
import { Filter, ArrowDownNarrowWide, ArrowUp, ArrowDown, ExternalLink, GitBranch, ClipboardIcon, CheckIcon, Loader2 } from 'lucide-react';
import GenericCard from './GenericCard';
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import Search from "@/components/BrowsePanel/TableComponents/Search";
import { BootstrapTooltip } from '../ToolTip/Tooltip-material-ui';
import { TOOLTIP_CONTENT } from '@/utils/constant/tooltip';
import { useParams, useRouter } from 'next/navigation';
import { buildProjectUrl } from '@/utils/navigationHelpers';

// Define interfaces for type safety
interface Container {
    title?: string;
}

interface Component {
    title?: string;
    description?: string;
    id?: string;
    type?: string;
    container?: Container;
    [key: string]: any;
}

interface ContainerGroup {
    components: Component[];
    [key: string]: any;
}

interface SortConfig {
    key: string | null;
    direction: 'asc' | 'desc';
}

interface GenericCardGridProps {
    data: ContainerGroup[];
    renderCard?: (item: Component, container?: ContainerGroup) => React.ReactNode;
    onCardClick: (item: Component) => void;
    actionButtons?: Array<{
        icon?: React.ReactNode;
        label: string;
        onClick: (item: Component) => void;
        className?: string;
    }>;
    gridClassName?: string;
    uniquePageIdentifier?: string;
    isModal?: boolean;
    loadingStates?: { [key: string]: boolean }; // New prop for loading states
    // Selection props for modal mode
    selectedContainers?: {
        all_containers: boolean;
        containers: string[];
    };
    onContainerSelect?: (item: Component) => void;
}

interface FilterOptions {
    sortKeys: { label: string; value: string }[];
    containerNames: string[];
}

interface PersistentFilterConfig {
    searchTerm: string;
    containerNameFilters: string[];
    sortConfig: SortConfig;
}

const getRepositoryDisplayName = (item: Component): string => {
    return item.repository_name || 
           item.project_name || 
           item.repositoryUrl || 
           "Unnamed Repository";
};

const GenericCardGrid: React.FC<GenericCardGridProps> = ({
    data,
    renderCard,
    onCardClick,
    actionButtons,
    gridClassName = "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",
    uniquePageIdentifier,
    isModal = false,
    loadingStates = {}, // Default to empty object
    selectedContainers,
    onContainerSelect
}) => {
    const router = useRouter();
    const params = useParams();
    const projectId = params.projectId;
    
    // Load persistent filter state from session storage
    const loadPersistentFilter = (): PersistentFilterConfig | null => {
        if (typeof window === 'undefined') return null;

        const storedFilter = sessionStorage.getItem(`${uniquePageIdentifier}`);
        if (!storedFilter) return null;

        try {
            const parsed = JSON.parse(storedFilter);

            // Type guard to ensure parsed data matches PersistentFilterConfig
            if (
                parsed &&
                typeof parsed.searchTerm === 'string' &&
                Array.isArray(parsed.containerNameFilters) &&
                parsed.sortConfig &&
                ['asc', 'desc'].includes(parsed.sortConfig.direction)
            ) {
                return parsed;
            }
            return null;
        } catch (error) {
            return null;
        }
    };

    // Save filter state to session storage
    const savePersistentFilter = (filterConfig: PersistentFilterConfig) => {
        if (typeof window === 'undefined') return;

        sessionStorage.setItem(
            `${uniquePageIdentifier}`,
            JSON.stringify(filterConfig)
        );
    };

    // Initial state loading from session storage
    const persistedFilter = uniquePageIdentifier ? loadPersistentFilter() : null;

    // State for search, filters, and sorting
    const [searchTerm, setSearchTerm] = useState<string>(persistedFilter?.searchTerm || '');
    const [containerNameFilters, setContainerNameFilters] = useState<string[]>(
        persistedFilter?.containerNameFilters || []
    );
    const [isContainerFilterOpen, setIsContainerFilterOpen] = useState<boolean>(false);
    const filterRef = useRef<HTMLDivElement>(null);
    const [sortConfig, setSortConfig] = useState<SortConfig>(
        persistedFilter?.sortConfig || {
            key: 'containerName',
            direction: 'asc'
        }
    );

    // Helper function to get key ignoring case
    const getKeyIgnoreCase = (obj: Record<string, any>, key: string): string => {
        return Object.keys(obj).find(k => k.toLowerCase() === key.toLowerCase()) || '';
    };

    
    // Extract unique container types and card types
    const filterOptions: FilterOptions = useMemo(() => {
        // Define specific sort keys based on the card rendering
        const sortKeys = [
            { label: "Group Name", value: "containerName" },
            { label: "Title", value: "title" },
            { label: "Description", value: "description" },
            { label: "Id", value: "id" },
            { label: "Type", value: "type" },
        ];

        // Extract unique container names
        const containerNames = [...new Set(
            data.flatMap(containerGroup =>
                containerGroup.components.map(comp =>
                    comp.container?.title || "N/A"
                )
            )
        )];

        return {
            sortKeys,
            containerNames
        };
    }, [data]);

    // Filtering and Sorting Logic
    const processedData = useMemo(() => {
        try {
            let result = data.filter(containerGroup => {
                // Filter components based on container names and search term
                const filteredComponents = containerGroup.components.filter(comp => {
                    // Container name filter
                    const matchesContainerName =
                        containerNameFilters.length === 0 ||
                        containerNameFilters.includes(comp.container?.title || "N/A");

                    // Search term filter
                    const searchMatch = !searchTerm ||
                        (comp[getKeyIgnoreCase(comp, 'title')] &&
                            String(comp[getKeyIgnoreCase(comp, 'title')] || '')
                                .toLowerCase()
                                .includes(searchTerm.toLowerCase())) ||
                        (comp[getKeyIgnoreCase(comp, 'description')] &&
                            String(comp[getKeyIgnoreCase(comp, 'description')] || '')
                                .toLowerCase()
                                .includes(searchTerm.toLowerCase())) ||
                        (comp[getKeyIgnoreCase(comp, 'id')] &&
                            String(comp[getKeyIgnoreCase(comp, 'id')] || '')
                                .toLowerCase()
                                .includes(searchTerm.toLowerCase())) ||
                        (comp[getKeyIgnoreCase(comp, 'type')] &&
                            String(comp[getKeyIgnoreCase(comp, 'type')] || '')
                                .toLowerCase()
                                .includes(searchTerm.toLowerCase()));

                    return matchesContainerName && searchMatch;
                });

                // Only keep containers that have matching components
                return filteredComponents.length > 0;
            }).map(containerGroup => ({
                ...containerGroup,
                components: containerGroup.components.filter(comp => {
                    const matchesContainerName =
                        containerNameFilters.length === 0 ||
                        containerNameFilters.includes(comp.container?.title || "N/A");

                    const searchMatch = !searchTerm ||
                        String(comp[getKeyIgnoreCase(comp, 'title')] || '')
                            .toLowerCase()
                            .includes(searchTerm.toLowerCase()) ||
                        String(comp[getKeyIgnoreCase(comp, 'description')] || '')
                            .toLowerCase()
                            .includes(searchTerm.toLowerCase()) ||
                        String(comp[getKeyIgnoreCase(comp, 'id')] || '')
                            .toLowerCase()
                            .includes(searchTerm.toLowerCase()) ||
                        String(comp[getKeyIgnoreCase(comp, 'type')] || '')
                            .toLowerCase()
                            .includes(searchTerm.toLowerCase());

                    return matchesContainerName && searchMatch;
                })
            }));

            // Apply sorting if a sort key is selected
            if (sortConfig.key) {
                if (sortConfig.key === 'containerName') {
                    // For container name sorting, sort the containers first
                    result.sort((a, b) => {
                        const containerA = a.components[0]?.container?.title || 'N/A';
                        const containerB = b.components[0]?.container?.title || 'N/A';
                        return sortConfig.direction === 'asc'
                            ? containerA.localeCompare(containerB)
                            : containerB.localeCompare(containerA);
                    });

                    // Then flatten the components while maintaining container order
                    const sortedComponents = result.flatMap(container => container.components);
                    return [{
                        components: sortedComponents
                    }];
                } else {
                    // For other fields, sort components directly
                    const allComponents = result.flatMap(container => container.components);
                    const sortedComponents = allComponents.sort((a, b) => {
                        const valueA = String(a[getKeyIgnoreCase(a, sortConfig.key || '')] || '');
                        const valueB = String(b[getKeyIgnoreCase(b, sortConfig.key || '')] || '');
                        return sortConfig.direction === 'asc'
                            ? valueA.localeCompare(valueB)
                            : valueB.localeCompare(valueA);
                    });

                    return [{
                        components: sortedComponents
                    }];
                }
            }

            return result;
        } catch (error) {
            console.error("Error in processedData calculation:", error);
            return [{ components: [] }];
        }
    }, [data, searchTerm, containerNameFilters, sortConfig]);

    // Effect section - all useEffects in one place
    // Update session storage whenever filter state changes
    useEffect(() => {
        if (!uniquePageIdentifier) return;

        const filterConfig: PersistentFilterConfig = {
            searchTerm,
            containerNameFilters,
            sortConfig
        };
        savePersistentFilter(filterConfig);
    }, [searchTerm, containerNameFilters, sortConfig, uniquePageIdentifier]);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (filterRef.current && !filterRef.current.contains(event.target as Node)) {
                setIsContainerFilterOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    // Separate useEffect for initial container names to avoid conditional hook calls
    useEffect(() => {
        // Only set to all container names if there are no persisted filters and filterOptions is available
        if (
            filterOptions.containerNames.length > 0 &&
            (!persistedFilter || persistedFilter.containerNameFilters.length === 0) &&
            containerNameFilters.length === 0
        ) {
            setContainerNameFilters([...filterOptions.containerNames]);
        }
    }, [filterOptions.containerNames, containerNameFilters.length, persistedFilter]);

    // Toggle container filter selection
    const toggleContainerFilter = (containerName: string) => {
        setContainerNameFilters(prev =>
            prev.includes(containerName)
                ? prev.filter(name => name !== containerName)
                : [...prev, containerName]
        );
    };

    // Clear all container filters
    const clearContainerFilters = () => {
        setContainerNameFilters([]);
    };

    // Sorting handler
    const handleSort = (key: string) => {
        setSortConfig((prev): SortConfig => {
            const newDirection: 'asc' | 'desc' =
                prev.key === key && prev.direction === 'asc'
                    ? 'desc'
                    : 'asc';

            return {
                key,
                direction: newDirection
            };
        });
    };

    // Clear all filters with session storage reset
    const clearAllFilters = () => {
        setSearchTerm('');
        clearContainerFilters();
        setSortConfig({ key: null, direction: 'asc' });

        // Remove the specific page's filter from session storage
        if (typeof window !== 'undefined' && uniquePageIdentifier) {
            sessionStorage.removeItem(`${uniquePageIdentifier}`);
        }
    };

    function sanitizeDescription(description: any) {
        // Remove special characters and trim the string
        return description.replace(/[^\w\s]/g, '').trim();
    }

    // Determine container label color based on name
    const getContainerLabelClass = (name: string): string => {
        // If no name is provided, return a default color
        if (!name) return 'bg-semantic-purple-100 text-semantic-purple-800';

        // Generate a hash code from the name length
        const hashCode = name.length % 10;

        // Define color palettes with different intensity levels using semantic colors
        const colorPalettes = [
            { bg: 'bg-primary-100', text: 'text-primary-800' },
            { bg: 'bg-semantic-green-100', text: 'text-semantic-green-800' },
            { bg: 'bg-semantic-red-100', text: 'text-semantic-red-800' },
            { bg: 'bg-semantic-yellow-100', text: 'text-semantic-yellow-800' },
            { bg: 'bg-primary-100', text: 'text-primary-800' },
            { bg: 'bg-semantic-red-100', text: 'text-semantic-red-800' },
            { bg: 'bg-semantic-purple-100', text: 'text-semantic-purple-800' },
            { bg: 'bg-semantic-green-100', text: 'text-semantic-green-800' },
            { bg: 'bg-primary-100', text: 'text-primary-800' },
            { bg: 'bg-primary-100', text: 'text-primary-800' }
        ];

        // Select a color palette based on the hash code
        const selectedPalette = colorPalettes[hashCode];

        return `${selectedPalette.bg} ${selectedPalette.text}`;
    };

    // Default card renderer
    const defaultCardRenderer = (item: Component) => {
        // Return null conditionally at this level for items that shouldn't be rendered at all
        if (isModal && !(item.properties?.ContainerType?.toLowerCase() === "internal" || item.properties?.ContainerType === undefined)) {
            return null;
        }

        // Create a separate component for internal rendering to manage state consistently
        const CardContent = () => {
            const [copied, setCopied] = useState(false);

            const copyToClipboard = async () => {
                try {
                    await navigator.clipboard.writeText(item.project_name || item.repositoryUrl);
                    setCopied(true);
                    setTimeout(() => setCopied(false), 2000);
                } catch (err) {
                    console.error('Failed to copy text: ', err);
                }
            };

            if (isModal) {
                // Check if repository is connected
                const hasRepository = item.project_name || item.repositoryUrl;
                
                // Check if this container is selected
                const itemId = String(item.id || '');
                const isSelected = selectedContainers?.all_containers || 
                    (selectedContainers?.containers && itemId.length > 0 && selectedContainers.containers.includes(itemId));

                return (
                    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 hover:shadow-md hover:border-orange-200 transition-all duration-200 h-[420px] flex flex-col">
                                                {/* Header Section with Checkbox */}
                        <div className="flex flex-col gap-2 mb-2 flex-shrink-0">
                            <div className="flex items-center justify-between">
                                {/* Checkbox */}
                                <input
                                    type="checkbox"
                                    checked={isSelected}
                                    onChange={(e) => {
                                        e.stopPropagation();
                                        if (onContainerSelect) {
                                            onContainerSelect(item);
                                        }
                                    }}
                                    className="w-4 h-4 flex-shrink-0 text-orange-600 rounded border-gray-300 focus:ring-orange-500"
                                />
                                <ExternalLink
                                    size={14}
                                    className="text-gray-400 hover:text-orange-600 cursor-pointer transition-colors flex-shrink-0"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        if(item?.id) {
                                            router.push(buildProjectUrl(projectId, `architecture/container/${item.id}`));
                                        } else {
                                            router.push(buildProjectUrl(projectId, 'architecture/container'));
                                        }
                                    }}
                                />
                            </div>
                            {/* Container Label on new line */}
                            <div>
                                <span className={`px-2 py-0.5 rounded-md typography-caption inline-block ${getContainerLabelClass(item[getKeyIgnoreCase(item, 'title')] || "Untitled Container")} `}>
                                    {item[getKeyIgnoreCase(item, 'title')] || "Untitled Container"}
                                </span>
                            </div>
                        </div>

                        {/* Content Section - Flexible */}
                        <div className="flex flex-col flex-1 min-h-0">
                            {/* Title and Description Section */}
                            <div className="flex-shrink-0 border-b pb-3 mb-3">
                                <h3 className="typography-body font-weight-semibold text-gray-900 mb-1.5">{item[getKeyIgnoreCase(item, 'title')]}</h3>
                                <p className="typography-body-sm text-gray-600 mb-1 line-clamp-2 leading-relaxed">{sanitizeDescription(item[getKeyIgnoreCase(item, 'description')])}</p>
                                <p className="typography-body-sm text-gray-500 mb-2">#{item?.id}</p>
                            </div>

                            {/* Repository Section - Flexible */}
                            <div className="flex-1 flex flex-col min-h-0">
                                <h4 className="text-gray-600 font-weight-medium typography-body-sm mb-2 flex-shrink-0">Connected Repository</h4>
                                <div className="flex-1 min-h-0">
                                    {hasRepository ? (
                                        <div className="w-full p-3 bg-gray-50 rounded-sm flex flex-col items-start gap-2">
                                            {/* Repository Name and Copy Icon Row */}
                                            <div className="flex items-center w-full">
                                                <div className="text-gray-700 typography-body-sm font-weight-medium truncate pr-2">
                                                    {getRepositoryDisplayName(item)}
                                                </div>
                                                <button
                                                    onClick={copyToClipboard}
                                                    title={`Copy: ${item.project_name ? `${item.project_name}` : (item.repositoryUrl)}`}
                                                    className="ml-auto p-1 hover:bg-gray-200 rounded transition-colors"
                                                >
                                                    {copied ? (
                                                        <CheckIcon className="h-3 w-3 text-green-500" />
                                                    ) : (
                                                        <ClipboardIcon className="h-3 w-3 text-gray-500" />
                                                    )}
                                                </button>
                                            </div>


                                            {/* Branch Row */}
                                            <div className="flex items-center w-full">
                                                {/* Git Branch Icon and Branch Name */}
                                                <div className="flex items-center">
                                                    <GitBranch className="mr-1 h-3 w-3 text-gray-500" />
                                                    <span className="text-gray-600 typography-body-sm font-weight-normal truncate">
                                                        {item.branch || "Not configured"}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="w-full p-3 bg-gray-50 rounded-sm border border-gray-200">
                                            <div className="flex items-center gap-2">
                                                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                                                <span className="text-gray-700 typography-body-sm font-weight-medium">Repository not connected</span>
                                            </div>
                                            <p className="text-gray-600 typography-caption mt-1">Configure repository during code generation</p>
                                        </div>
                                    )}
                                </div>

                                <div className="mt-3 flex-shrink-0">
                                    <div className="flex flex-col gap-1.5">
                                        <div className="flex items-center gap-2 flex-wrap">
                                            {/* Framework Tag */}
                                            <span className="px-2 py-0.5 bg-orange-100 text-orange-700 rounded-full typography-body-sm">
                                                {item.framework ? (item.framework.charAt(0).toUpperCase() + item.framework.slice(1)) + " App" : "React App"}
                                            </span>
                                            
                                            {/* Platform Tag */}
                                            {(item.platform || item.properties?.platform) && (
                                                <span className="px-2 py-0.5 bg-primary-100 text-primary-700 rounded-full typography-body-sm">
                                                    {(item.platform || item.properties?.platform).charAt(0).toUpperCase() + (item.platform || item.properties?.platform).slice(1)}
                                                </span>
                                            )}
                                            
                                            {/* Container Type Tag */}
                                            {(item.container_type || item.properties?.container_type) && (
                                                <span className="px-2 py-0.5 bg-green-100 text-green-700 rounded-full typography-body-sm">
                                                    {(item.container_type || item.properties?.container_type).charAt(0).toUpperCase() + (item.container_type || item.properties?.container_type).slice(1)}
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Generate Button Section - Fixed at bottom */}
                        <div className="flex justify-end mt-3 pt-3 border-t flex-shrink-0">
                            {(() => {
                                // Get the loading state for this specific item's generate action
                                const generateLoadingKey = `${item.id}-generate`;
                                const isGenerateLoading = loadingStates[generateLoadingKey];
                                
                                return (
                                    <button
                                        className={`h-7 px-3 flex items-center justify-center rounded-md overflow-hidden text-white typography-body-sm font-weight-medium transition-colors duration-200 transform hover:scale-[1.02] active:scale-[0.98] ${
                                            isGenerateLoading 
                                                ? 'bg-orange-400 cursor-not-allowed' 
                                                : 'bg-orange-500 hover:bg-orange-600 active:bg-orange-700'
                                        }`}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            if (!isGenerateLoading) {
                                                // Find and execute "Config" action
                                                const configAction = actionButtons?.find(
                                                    button => button.label === "Config"
                                                );
                                                if (configAction) {
                                                    configAction.onClick(item);
                                                }
                                            }
                                        }}
                                        disabled={isGenerateLoading}
                                    >
                                        {isGenerateLoading ? (
                                            <>
                                                <Loader2 className="h-4 w-4 animate-spin mr-1" />
                                                Loading...
                                            </>
                                        ) : (
                                            'Config'
                                        )}
                                    </button>
                                );
                            })()}
                        </div>
                    </div>
                );
            }

            // Regular view using GenericCard
            return (
                <GenericCard
                    title={item[getKeyIgnoreCase(item, 'title')]}
                    description={sanitizeDescription(item[getKeyIgnoreCase(item, 'description')])}
                    id={item[getKeyIgnoreCase(item, 'id')]}
                    containerName={item?.container?.title || "N/A"}
                    branch={item?.branch}
                    onClick={() => onCardClick(item)}
                />
            );
        };

        // Return the CardContent component
        return <CardContent />;
    };

    const cardRenderer = renderCard || defaultCardRenderer;

    const handleSearch = (term: string) => {
        setSearchTerm(term);
    };

    const selectAllContainers = () => {
        setContainerNameFilters(filterOptions.containerNames);
    };

    return (
        <div className={`${isModal ? 'flex flex-col' : "h-[calc(100vh-250px)]"} space-y-2 transition-all duration-300 ease-in-out`}>
            {/* Search and Filter Panel */}
            {(isModal === false) && (
                <>
                    <div className="flex items-center justify-between mb-3 bg-white">
                        {/* Search Input */}
                        <div className="">
                            <Search
                                searchTerm={searchTerm}
                                setSearchTerm={handleSearch}
                            />
                        </div>

                        <div className='inline-flex items-center space-x-3'>
                            {/* Container Name Multi-Select Filter */}
                            <div className="relative typography-body-sm" ref={filterRef}>
                                <button
                                    onClick={() => setIsContainerFilterOpen(!isContainerFilterOpen)}
                                    className="inline-flex items-center justify-start gap-2 font-weight-medium transition-colors duration-200 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500 disabled:bg-gray-100 px-4 py-2 typography-body-sm max-h-9 w-56"
                                >
                                    <Filter className="mr-3 text-gray-400 w-4 h-4" />
                                    {containerNameFilters.length > 0
                                        ? `Group (${containerNameFilters.length})`
                                        : "Select Groups"}
                                </button>

                                {isContainerFilterOpen && (
                                    <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg z-50 py-2 filter-dropdown max-h-60">
                                        <div className="p-2 border-b flex justify-between items-center sticky top-0 bg-white z-10">
                                            <span className="font-weight-semibold">Filters</span>
                                            <div className="space-x-2">
                                                <button
                                                    onClick={selectAllContainers}
                                                    className="typography-body-sm text-primary"
                                                >
                                                    Select All
                                                </button>
                                                <button
                                                    onClick={clearContainerFilters}
                                                    className="typography-body-sm text-primary"
                                                >
                                                    Clear
                                                </button>
                                            </div>
                                        </div>
                                        <div className="overflow-y-auto custom-scrollbar max-h-40">
                                            {filterOptions.containerNames.map((containerName) => (
                                                <label
                                                    key={containerName}
                                                    className="flex items-center p-2 hover:bg-gray-100 cursor-pointer"
                                                >
                                                    <input
                                                        type="checkbox"
                                                        className="rounded border-gray-300"
                                                        checked={containerNameFilters.includes(containerName)}
                                                        onChange={() => toggleContainerFilter(containerName)}
                                                    />
                                                    <span className='ml-2 typography-body-sm text-gray-700'>{containerName}</span>
                                                </label>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Sort Dropdown */}
                            <div className="relative flex items-center justify-center typography-body-sm">
                                <div className="absolute left-0 pl-3">
                                    <ArrowDownNarrowWide className="text-gray-400 w-4 h-4" />
                                </div>
                                <select
                                    value={sortConfig.key || ""}
                                    onChange={(e) => handleSort(e.target.value)}
                                    className="px-10 pr-10 py-2 inline-flex items-center justify-center gap-2 font-weight-medium transition-colors duration-200 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500 disabled:bg-gray-100 typography-body-sm max-h-9"
                                >
                                    <option value="">Sort By</option>
                                    {filterOptions.sortKeys.map((key) => (
                                        <option key={key.value} value={key.value}>
                                            {key.label}
                                        </option>
                                    ))}
                                </select>
                                {sortConfig.key && (
                                    <button
                                        onClick={() => handleSort(sortConfig.key as string)}
                                        className="ml-2 text-gray-600 hover:text-gray-800 focus:outline-none"
                                    >
                                        {sortConfig.direction === "asc" ? (
                                            <BootstrapTooltip title={TOOLTIP_CONTENT.common.sortDesc}>
                                                <ArrowUp className="text-gray-400 w-4 h-4" />
                                            </BootstrapTooltip>
                                        ) : (
                                            <BootstrapTooltip title={TOOLTIP_CONTENT.common.sortAsc}>
                                                <ArrowDown className="text-gray-400 w-4 h-4" />
                                            </BootstrapTooltip>
                                        )}
                                    </button>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Results Count and Clear Filters */}
                    {(searchTerm || sortConfig.key || containerNameFilters.length > 0) && (
                        <div className="flex items-center justify-between typography-body-sm text-gray-600 mb-2">
                            <span>
                                {processedData.reduce((total, container) =>
                                    total + container.components.length, 0
                                )} results found
                            </span>
                            <button
                                onClick={clearAllFilters}
                                className="text-orange-600 hover:underline"
                            >
                                Clear All
                            </button>
                        </div>
                    )}
                </>
            )}

            {/* Card Grid */}
            <div className={`${isModal ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-3 pb-4' : gridClassName}`}>             
                {processedData.flatMap((container, containerIdx) =>
                    container.components.map((comp, compIdx) => (
                        <React.Fragment key={`${comp.id || containerIdx + '-' + compIdx}`}>
                            {cardRenderer(comp, container as any)}
                        </React.Fragment>
                    ))
                )}
            </div>

            {/* No Results State */}
            {processedData.length === 0 && (
                <EmptyStateView type="noSearchResult" onClick={() => {
                    setSearchTerm('');
                    clearContainerFilters();
                    setSortConfig({ key: null, direction: 'asc' });
                }} />
            )}
        </div>
    );
};

export default GenericCardGrid;
