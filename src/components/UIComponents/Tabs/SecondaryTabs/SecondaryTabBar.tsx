import React, { useState, useRef, useEffect } from 'react';
import { TabBarProps } from '@/types/SecondaryTabs/tab.types';
import { BootstrapTooltip } from '../../ToolTip/Tooltip-material-ui';

export const TabBar: React.FC<TabBarProps> = ({
    tabs,
    activeTab,
    onTabClick
}) => {
    const [containerWidth, setContainerWidth] = useState(0);
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const updateWidth = () => {
            if (containerRef.current) {
                setContainerWidth(containerRef.current.clientWidth);
            }
        };

        const timer = setTimeout(updateWidth, 100);
        const handleResize = () => setTimeout(updateWidth, 50);
        window.addEventListener('resize', handleResize);

        return () => {
            clearTimeout(timer);
            window.removeEventListener('resize', handleResize);
        };
    }, []);

    useEffect(() => {
        if (containerRef.current) {
            const timer = setTimeout(() => {
                setContainerWidth(containerRef.current?.clientWidth || 0);
            }, 200);
            return () => clearTimeout(timer);
        }
    }, [tabs]);

    const getTotalContentWidth = () => {
        return tabs.reduce((total, tab) => {
            const baseWidth = Math.max(50, tab.label.length * 8 + 28);
            return total + baseWidth;
        }, 0);
    };

    const getTabInfo = (label: string) => {
        const totalContentWidth = getTotalContentWidth();
        const naturalWidth = Math.max(50, label.length * 8 + 28);

        if (totalContentWidth <= containerWidth) {
            return {
                label: label,
                width: naturalWidth,
                useNaturalWidth: true
            };
        }

        const proportionalWidth = (naturalWidth / totalContentWidth) * containerWidth;
        const minWidth = 40;
        const finalWidth = Math.max(minWidth, proportionalWidth);

        let abbreviatedLabel = label;
        if (finalWidth < 80) {
            if (finalWidth < 50) {
                abbreviatedLabel = label.substring(0, 2) + (label.length > 2 ? '...' : '');
            } else if (finalWidth < 65) {
                abbreviatedLabel = label.substring(0, 4) + (label.length > 4 ? '...' : '');
            } else {
                const firstWord = label.split(' ')[0];
                abbreviatedLabel = firstWord.length > 6 ? firstWord.substring(0, 6) + '...' : firstWord;
            }
        }

        return {
            label: abbreviatedLabel,
            width: finalWidth,
            useNaturalWidth: false
        };
    };

    return (
        <div className="flex items-center w-full" ref={containerRef}>
            <div className="flex w-full" style={{ zIndex: 1 }}>
                {tabs.map((tab) => {
                    const tabInfo = getTabInfo(tab.label);
                    return (
                        <BootstrapTooltip key={tab.id} title={tab?.tooltip || tab.label} placement="bottom">
                            <div
                                className={`secondary-tab ${activeTab === tab.name ? 'active-tab' : 'inactive-tab'} cursor-pointer transition-all duration-200`}
                                onClick={() => onTabClick(tab.name)}
                                style={{
                                    width: `${tabInfo.width}px`,
                                    minWidth: '35px',
                                    maxWidth: tabInfo.useNaturalWidth ? 'none' : '180px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    padding: '4px 8px',
                                    flexShrink: tabInfo.useNaturalWidth ? 0 : 1
                                }}
                            >
                                <span className="text-center whitespace-nowrap overflow-hidden text-ellipsis">
                                    {tabInfo.label}
                                </span>
                            </div>
                        </BootstrapTooltip>
                    );
                })}
            </div>
        </div>
    );
};