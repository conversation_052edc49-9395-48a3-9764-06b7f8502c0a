import React, { ReactNode } from 'react';
import { LucideIcon } from 'lucide-react';
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";

type ButtonVariant = 'primary' | 'secondary' | 'danger' | 'icon' | 'text' | 'orange';
type ButtonSize = 'sm' | 'md' | 'lg';

interface UiButtonProps {
  onClick: () => void;
  children?: ReactNode;
  icon?: LucideIcon;  // Changed to only accept LucideIcon
  iconPosition?: 'left' | 'right';
  variant?: ButtonVariant;
  size?: ButtonSize;
  tooltip?: string;
  className?: string;
  iconStyle?: string;
  disabled?: boolean;
  ariaLabel?: string;
  placement?: 'top' | 'bottom' | 'left' | 'right';
}

export const UiButton: React.FC<UiButtonProps> = ({
  onClick,
  children,
  icon: IconComponent,  // Renamed to IconComponent
  iconPosition = 'left',
  variant = 'primary',
  size = 'md',
  tooltip,
  className = '',
  iconStyle = '',
  disabled = false,
  ariaLabel,
  placement = 'bottom-end'
}) => {
  const baseStyles = 'inline-flex items-center justify-center transition-colors duration-200 rounded-md focus:outline-none';

  const variantStyles = {
    primary: 'bg-gray-500 hover:bg-gray-600 text-white',
    secondary: 'bg-gray-50 hover:bg-gray-100 text-gray-700',
    danger: 'bg-red-600 hover:bg-red-700 text-white',
    icon: 'text-gray-600 hover:text-gray-700 hover:bg-gray-100 rounded-full',
    text: 'text-gray-600 hover:text-gray-700 hover:bg-gray-100',
    orange: 'bg-primary-500 hover:bg-primary-600 text-white'
  };

  const sizeStyles = {
    sm: 'typography-body-sm gap-1',
    md: 'typography-body gap-2',
    lg: 'typography-body-lg gap-2'
  };

  const isIconOnly = Boolean(IconComponent && !children);
  const iconSize = {
    sm: 16,
    md: 20,
    lg: 24
  };

  const buttonStyles = `
    ${baseStyles}
    ${variantStyles[variant]}
    ${!isIconOnly ? sizeStyles[size] : 'p-2'}
    ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
    ${className}
    `.trim();

  return (
    <BootstrapTooltip title={tooltip || ''} placement={placement}>
      <span>
        <button
          onClick={onClick}
          className={buttonStyles}
          disabled={disabled}
          aria-label={ariaLabel}
        >
          {iconPosition === 'left' && IconComponent && (
            <IconComponent className={iconStyle} size={iconSize[size]} />
          )}
          {children}
          {iconPosition === 'right' && IconComponent && (
            <IconComponent className={iconStyle} size={iconSize[size]} />
          )}
        </button>
      </span>
    </BootstrapTooltip>
  );
};