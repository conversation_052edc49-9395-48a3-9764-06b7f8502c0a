import React, { ReactNode } from 'react';
import { HiOutlineSearch } from 'react-icons/hi';

import '@/styles/components/UI.css';

interface SearchInputProps {
  value: string;
  children?: ReactNode;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  withContainer?: boolean;
}

export const SearchInput: React.FC<SearchInputProps> = ({
  value,
  children,
  onChange,
  placeholder = 'Search',
  withContainer = false,
}) => {
  return (
    <div className={`relative flex items-center w-full ${withContainer ? 'search-container' : ''}`}>
      <div className={`${withContainer ? 'search-icon' : 'search-icon-without-container'}`}>
        <HiOutlineSearch />
      </div>
      <input
        type="text"
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        className="px-2 py-1 pl-8 border border-custom-border rounded w-full shadow-sm"
      />
      {children && (
        children
      )}
    </div>
  );
};