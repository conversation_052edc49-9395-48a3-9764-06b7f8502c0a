import React from 'react';

const CardGroupSkeletonLoder = () => {
    return (
        <div className="w-full space-y-4 transition-opacity duration-300 ease-in-out">
            <div className="flex justify-between items-center">
                <div className="flex items-center space-x-2 bg-custom-bg-primary rounded-lg p-2 w-64">
                    <div className="h-4 w-4 bg-semantic-gray-200 rounded-lg animate-pulse"></div>
                    <div className="h-4 w-32 bg-semantic-gray-200 rounded-lg animate-pulse"></div>
                </div>
                <div className="flex items-center space-x-2">
                    <div className="bg-custom-bg-primary rounded-lg p-2 w-32">
                        <div className="h-4 w-full bg-semantic-gray-200 rounded-lg animate-pulse"></div>
                    </div>
                    <div className="bg-custom-bg-primary rounded-lg p-2 w-32">
                        <div className="h-4 w-full bg-semantic-gray-200 rounded-lg animate-pulse"></div>
                    </div>
                </div>
            </div>

            <div className="flex items-center space-x-2">
                <div className="h-4 w-24 bg-gray-100 rounded-lg animate-pulse"></div>
                <div className="h-4 w-16 bg-gray-100 rounded-lg animate-pulse"></div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 min-h-[60vh]">
                {[...Array(9)].map((_, i) => (
                    <div key={i} className="bg-white rounded-lg p-4 animate-pulse">
                        <div className="h-6 w-24 bg-gray-50 rounded-lg mb-2"></div>
                        <div className="h-8 w-4/5 bg-gray-200 rounded-lg mb-3"></div>
                        <div className="h-4 w-full bg-gray-100 rounded-lg mb-4"></div>
                        <div className="flex justify-between items-center">
                            <div className="h-4 w-16 bg-gray-100 rounded-lg"></div>
                            <div className="h-6 w-24 bg-gray-100 rounded-lg"></div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default CardGroupSkeletonLoder;