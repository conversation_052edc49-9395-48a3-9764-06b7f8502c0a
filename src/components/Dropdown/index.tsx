"use client";

import React, { useContext } from "react";
import { Button } from "@/components/Buttons";
import { MoreHorizontal } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "./dropdown-menu";
import { AlertContext } from "../NotificationAlertService/AlertList";

const Dropdown: React.FC = () => {
  const { showAlert } = useContext(AlertContext);

  const handleBtnClick = () => {
    showAlert("Functionality was not implemented", "info");
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          size="icon"
          className="w-5 h-5 p-1 bg-neutral-100 rounded-md border justify-center items-center inline-flex"
          onClick={handleBtnClick}
          disabled
        >
          <MoreHorizontal className="w-5 h-5 text-default-600" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-[196px]"
        align="end"
        side="bottom"
        avoidCollisions
      >
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuItem onClick={handleBtnClick}>Item 1</DropdownMenuItem>
        <DropdownMenuItem onClick={handleBtnClick}>Item 2</DropdownMenuItem>
        <DropdownMenuItem onClick={handleBtnClick}>Item 3</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default Dropdown;