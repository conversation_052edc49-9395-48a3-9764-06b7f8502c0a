import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";

interface SidebarItem {
  route: string;
label: string;
}

interface SidebarDropdownProps {
  items: SidebarItem[];
}

const SidebarDropdown: React.FC<SidebarDropdownProps> = ({ items }) => {
const pathname = usePathname();

return (
    <ul className="sidebar-dropdown">
      {items.map((item) => (
        <li key={item.route}>
<Link
href={item.route}
            className={`sidebar-dropdown-item ${pathname === item.route
              ? "sidebar-dropdown-item-active"
              : "sidebar-dropdown-item-inactive"
              }`}
            aria-current={pathname === item.route ? "page" : undefined}
>
{item.label}
</Link>
        </li>
      ))}
    </ul>
);
};

export default SidebarDropdown;