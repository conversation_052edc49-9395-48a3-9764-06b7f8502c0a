# Sidebar Component with Dark Theme Support

This component provides a customizable sidebar with dark theme support for the KAVIA UI.

## Usage

### Dark Theme Toggle

At the top of the Sidebar component (`src/components/Sidebar/index.tsx`), there's a flag to enable/disable dark theme:

```tsx
// =============================================
// TOGGLE THIS FLAG TO ENABLE/DISABLE DARK THEME
// Set to true to enable dark theme for the sidebar
// =============================================
const ENABLE_DARK_THEME = false;
// =============================================
```

Simply change this flag to `true` to enable dark theme throughout the sidebar.

### Basic Usage

```jsx
import Sidebar from '@/components/Sidebar';

// Default light theme
<Sidebar />

// With dark theme
<Sidebar darkTheme={true} />
```

### Controlling Dark Theme with State

```jsx
import { useState } from 'react';
import Sidebar from '@/components/Sidebar';

function Layout() {
  const [darkTheme, setDarkTheme] = useState(false);
  
  return (
    <div>
      <Sidebar darkTheme={darkTheme} />
      <button onClick={() => setDarkTheme(!darkTheme)}>
        Toggle Dark Theme
      </button>
    </div>
  );
}
```

### Matching Login State

Similar to the build page, you can match the dark theme to the login state:

```jsx
import Sidebar from '@/components/Sidebar';

function App() {
  const loggedInState = isLoggedIn(); // Your login state check
  
  return (
    <div>
      <Sidebar darkTheme={!loggedInState} />
    </div>
  );
}
```

## Features

- Dark theme support for the sidebar, matching the build page's pre-login state
- Darkened sidebar items, icons, and text
- Dark theme support for dropdowns and drawers
- White logo support for dark theme
- Dark theme compatibility throughout nested components

## Implementation Details

The dark theme is implemented using data attributes instead of class names for styling. This preserves the original styling while adding dark theme capability. Key components with dark theme support:

- Sidebar/index.tsx
- SidebarItem.tsx
- SidebarDropdown.tsx
- SidebarFooter.tsx
- PlanPopup.tsx
- Drawer.tsx

## CSS

Dark theme styles are implemented in:

- src/styles/sidebar.css
- src/styles/components/_drawer.css

These styles use data attributes like `[data-dark-theme="true"]` to apply dark theme styles without changing the original styling. 