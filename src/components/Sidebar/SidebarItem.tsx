//@ts-nocheck
import React, { useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import SidebarDropdown from "@/components/Sidebar/SidebarDropdown";
import { BootstrapTooltip } from "@/components/UIComponents/ToolTip/Tooltip-material-ui";

// Define the interface for the sidebar item
interface SidebarItemType {
  label: string;
  route: string;
  icon: React.ReactElement;
  children?: SidebarItemType[];
}

// Define the props interface for the SidebarItem component
interface SidebarItemProps {
  item: SidebarItemType;
  index: number;
  pageName: string;
  setPageName: (pageName: string) => void;
  isCollapsed?: boolean;
  handleDrawerToggle?: () => void;
  isActive: boolean;
  onClick?: () => void;
}

const SidebarItem: React.FC<SidebarItemProps> = ({
  item,
  index,
  pageName,
  setPageName,
  isCollapsed = false,
  handleDrawerToggle,
  isActive,
  onClick,

}) => {
  const pathname = usePathname();

  useEffect(() => {
    // Update the active item based on pathname
    const updatedPageName = item.label.toLowerCase();
    if (pathname.includes(updatedPageName)) {
      setPageName(updatedPageName);
    }
  }, [pathname, item.label, setPageName]);

  const handleClick = () => {
    const updatedPageName = item.label.toLowerCase();
    setPageName(updatedPageName);
    if (onClick) onClick(); // Trigger click handler passed as prop

    if (handleDrawerToggle) handleDrawerToggle();
  };

  return (
    <li className="sidebar-item">
      <BootstrapTooltip title={item.label} placement="right">
        <Link
          href={item.route}
          onClick={handleClick}
          className={`sidebar-link ${isActive ? "active" : ""}`}
   
        >
          <div className={`sidebar-icon-wrapper ${isActive ? "active" : ""}`}>
            {React.cloneElement(item.icon, {
              className: `sidebar-icon ${isActive ? "active" : ""}`
            })}
          </div>
          {!isCollapsed && <span >{item.label}</span>}
          {item.children && (
            <svg
              className={`sidebar-dropdown-icon ${!isActive ? "rotated" : ""}`}
              fill="currentColor"
              viewBox="0 0 20 20"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                d="M10 12a1 1 0 01-.7-.29l-4-4a1 1 111.4-1.42L10 9.58l3.3-3.3a1 1 111.4 1.42l-4 4a1 1 01-.7.3z"
                clipRule="evenodd"
              />
            </svg>
          )}
        </Link>
      </BootstrapTooltip>

      {item.children && isActive && <SidebarDropdown items={item.children} />}
    </li>
  );
};

export default SidebarItem;