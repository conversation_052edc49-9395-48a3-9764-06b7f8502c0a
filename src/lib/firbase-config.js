import { initializeApp } from "firebase/app";
import { getMessaging } from "firebase/messaging";

const firebaseConfig = {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
    authDomain: "kavia-467d8.firebaseapp.com",
    projectId: "kavia-467d8",
    storageBucket: "kavia-467d8.firebasestorage.app",
    messagingSenderId: "701815814656",
    appId: "1:701815814656:web:cf787f7c9988c9e03f36e5",
    measurementId: "G-50TD7RCF0W"
};


const firebaseApp = initializeApp(firebaseConfig);
export default firebaseApp;

export const messaging = typeof window !== 'undefined' && 
                       'serviceWorker' in navigator
                       ? getMessaging(firebaseApp)
                       : null;