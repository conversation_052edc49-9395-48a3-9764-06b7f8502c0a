
const DRIVER_STEPS = [
    //discussion chat panel
    {element: ".chat-panel", popover: {title: "Discussion Chat Panel", description: "You can discuss with Kavia AI to assist you with projects here."}},

    //topbar tabs
    {element: "#overview", popover: {title: "Overview Menu", description: "Contains overview of the project"}},
    {element: ".projectAssetButton", popover: {title: "Project Assets", description: "You can manage your project assets here."}},
    {element: "#workitems", popover: {title: "Overview Menu", description: "Contains overview of the project"}},
    {element: "#requirements", popover: {title: "Overview Menu", description: "Contains overview of the project"}},
    {element: "#architecture", popover: {title: "Overview Menu", description: "Contains overview of the project"}},
    {element: "#ui_ux", popover: {title: "Overview Menu", description: "Contains overview of the project"}},
    {element: "#codegeneration", popover: {title: "Overview Menu", description: "Contains overview of the project"}},

    //DOM elements for overview page
    {element: ".overviewContainerPadding", popover: {title: "Project Overview", description: "Initial overview of the Project"}},
    {element: ".userIconDiv", popover: {title: "Add Users", description: "Add selected users to the project."}},
    {element: ".overviewUpdateProject", popover: {title: "Update Project", description: "Click to discuss with Kavia AI to update project details."}},
    {element: ".overviewAutoConfigure", popover: {title: "Auto Configure", description: "Click to let Kavia Auto-Configure the project items."}},
    {element: ".overviewProjectProgress", popover: {title: "Project Progress", description: "Current progress of the project."}},
    {element: ".overviewTotalNodes", popover: {title: "Total Nodes", description: "Total number of nodes in the project."}},
    {element: ".overviewCredits", popover: {title: "Credits", description: "Total credits needed in the project."}},
    {element: ".overviewProgressBar", popover: {title: "Project Progress", description: "Total progress in the project. Click on the progress bar of each item to see more details."}},
    {element: ".overviewProgressDetails", popover: {title: "Progress Detials", description: "Detial of each progress"}},
    {element: "#Description", popover: {title: "Description", description: "Description of project"}},
    {element: "#Scope", popover: {title: "Scope", description: "Scope of project"}},
    {element: "#Objective", popover: {title: "Objective", description: "Objective of the project"}},
    {element: "#Architecture-Pattern", popover: {title: "Architecture Pattern", description: "Architecture pattern of the project"}},
    {element: "#Architecture-Strategy", popover: {title: "Architecture Strategy", description: "Architecture strategy of the project"}},
    {element: "#Team-Composition", popover: {title: "Team Composition", description: "Roles of different members required in the project"}},

    //DOM elements for Query Page
    {element: ".generateGraphButton", popover: {title: "Generate Graph", description: "Click on this button to generate graph."}},
    {element: ".addNewRepoButton", popover: {title: "Add New Repository", description: "Click on this button to add a new repository."}},
    {element: ".startQuery", popover: {title: "Start a query ", description: "Click on this button to start a new query."}},
    {element: ".repoTable", popover: {title: "Repository Table", description: "This table lists all the repositories added."}},
    {element: ".queryHistory", popover: {title: "Query History", description: "Past Query generated for the system."}},
    {element: ".workItemsUpdateButton", popover: {title: "Update Workitems", description: "Click on this button to talk to Kavia AI and update work items."}},
    {element: ".workItemsCreateButton", popover: {title: "Create Workitems", description: "Click on this button to talk to Kavia AI and create work items."}},
    {element: ".workItemsCreateButton", popover: {title: "Create Workitems", description: "Click on this button to talk to Kavia AI and create work items."}},
    {element: ".workItemAutoConfigureButton", popover: {title: "Auto Configure", description: "Click on this button to let Kavia AI configure the work items."}},
    {element: ".workItemsTable", popover: {title: "Work Items Table", description: "List of all the work items created by Kavia."}},
    {element: ".updateRequirementsButton", popover: {title: "Update Requirements", description: "Click on this button to talk to Kavia AI and update the requirements."}},
    {element: ".createRequirementsButton", popover: {title: "Create Requirements", description: "Click on this button to let Kavia AI configure the work items."}},
    {element: ".requirementsConfigureButton", popover: {title: "Auto Configure", description: "Click on this button to let Kavia AI configure the requirements."}},
    {element: ".requirementTableMainDiv", popover: {title: "Requirements Table", description: "List of all the requirements created by Kavia."}},
    
    //Tab headers of architecture page
    {element: "#architecture-requirement", popover: {title: "Architecture Requirements Tab", description: "Click on this button to get information about the architecture requirements."}},
    {element: "#system-context", popover: {title: "System Context Tab", description: "Overall System Context for the project. Contains User List, External Systems, and System Context Diagrams."}},
    {element: "#container", popover: {title: "System Container Tab", description: "Contails different containers required for the system. Each container handles a part of the system"}},
    {element: "#component", popover: {title: "Component Tab", description: "Information about all the components required for the system."}},
    {element: "#design", popover: {title: "Design Tab", description: "Information about all the design for system components."}},
    {element: "#interfaces", popover: {title: "Interfaces Tab", description: "Information about different interfaces required for the system."}},
    {element: "#software-architecture", popover: {title: "SAD Tab", description: "Software-Architecture Document for the system"}},
    {element: "#prd", popover: {title: "PRD Tab", description: "Product Requirement Document that explains the detials, features, and product functionalities."}},
    {element: "#api-docs", popover: {title: "API-Docs Tab", description: "Information about documentation for API endpoints of the system."}},
    
    //DOM elements of the architecture requirement page
    {element: ".architectureCreateRequirementsButton", popover: {title: "Create Requirements", description: "Click on this button to talk to Kavia AI to create new architecture requirements."}},
    {element: ".architectureUpdateRequirementsButton", popover: {title: "Update Requirements", description: "Click on this button to talk to Kavia AI and update the architecture requirements."}},
    {element: ".architectureRequirementsConfigureButton", popover: {title: "Update Requirements", description: "Click on this button to let Kavia AI automatically configure the architecture requirements."}},
    {element: "#architecture-requirementDescription", popover: {title: "Architecture Requirements Description", description: "This section provides the description of the architecture requirements."}},
    {element: "#architecture-requirementFunctional-Requirements", popover: {title: "Functional Requirements", description: "Functional Requirements of the system architecture."}},
    {element: "#architecture-requirementArchitectural-Requirements", popover: {title: "Architectural Requirements", description: "Architectural Requirements of the system architecture."}},

    //DOM elements of the system context page
    {element: ".createSystemContextButton", popover: {title: "Create System Context", description: "Click on this button to talk to Kavia AI to create the system context."}},
    {element: ".updateSystemContextButton", popover: {title: "Update System Context", description: "Click on this button to talk to Kavia AI to update the system context."}},
    {element: "#system-contextDescription", popover: {title: "System Context Description", description: "Description of the system context."}},
    {element: "#system-contextUsers", popover: {title: "Users", description: "List of users in the system."}},
    {element: "#system-contextExternal-Systems", popover: {title: "External Systems", description: "List of external systems in the system context."}},
    {element: "#system-contextSystem-Context-Diagram", popover: {title: "System Context Diagram", description: "System Context Diagram. It visually represents a system's interactions with external entities, showing data flow and boundaries and provides a high-level overview for understanding system scope and connections."}},
    {element: "#system-contextContainer-Diagram", popover: {title: "Container Diagram", description: "Container diagram that shows interaction between differents components of the system."}},
    
    //DOM elements for architecture container
    {element: ".architectureContainerGrid", popover: {title: "Container Grid", description: "List of different containers used in the system."}},

    //DOM elements for architecture components
    {element: ".architecture-component-grid", popover: {title: "Components Grid", description: "List of different components used in the system."}},

    //DOM elements for architecture design
    {element: ".architecture-design-grid", popover: {title: "Architecture Design Grid", description: "List of different design of components used in the system."}},

    //DOM elements for architecture interface
    {element: ".architecture-interface-grid", popover: {title: "Architecture Interface Grid", description: "List of different interfaces required by the system."}},

    //DOM elements for SAD Page
    {element: "#main-sad-content", popover: {title: "Software Architecture Document", description: "Root Software Architecture document for the project. It contains information from all the previous sections in the architecture. You can download this document as a PDF or sync the current state of document to cloud storage."}},

    //DOM elements for PRD Page
    {element: "#main-prd-content", popover: {title: "Product Requirements Document", description: "Product Requirement Document for the system. Lists the overview, problem statement, and proposed solution with all the technical and non-technical requirements."}}
];


const DRIVER_DICTIONARY = {
  overview: [
    {popover: {title: "Welcome to your project.", description: "Take a quick tour of your project! This will help understand the contents in your project easily.<br><br>You can navigate around the tour using arrow keys or by pressing the next and previous button. Let's get started!"}},
    {element: "#projectAssetButton", popover: {title: "Project Assets", description: "You can manage your project assets here. This includes any of your existing repositories and documents."}},
    {element: "#overviewAutoConfigButton", popover: {title: "Auto Configure", description: "You can configure the project items using Kavia AI here."}},
    {element: "#projectTimeline", popover: {title: "Project Timeline", description: "You can view the current stage of the project here. You can get started from here!!"}}
  ],
  query: [
    {element: ".repoTable", popover: {title: "Repository Table", description: "This table lists all the repositories added."}},
    {element: ".generateGraphButton", popover: {title: "Generate Graph", description: "Click on this button to generate graph.<br><br> You have to select one repository beforehand."}},
    {element: ".addNewRepoButton", popover: {title: "Add New Repository", description: "Click on this button to add a new repository."}},
    {element: ".startQuery", popover: {title: "Start a query ", description: "Click on this button to start a new query."}},
    {element: ".queryHistory", popover: {title: "Query History", description: "Past Query generated for the system."}},
    {element: "#workitems", popover: {title: "Overview Menu", description: "Continue your tour by clicking on this work-items tab. You can restart this tour again after coming back to the overview page."}},
  ],
  discussionChat: [
    {element: "#discussionChatModal", popover: {title: "Discussion Panel", description: "You can discuss more about the project with KaviaAI here", side: 'top'}},
    {element: "#discussionChatPanel", popover: {title: "Chat Section", description: "You can type the enhancements you want in the project here. After you are satisfied with the response, type 'capture' and press 'enter'. You will be able to merge changes in your project after that."}}
  ],
  projectAsset: [
    {element: "#projectAssetModal", popover: {title: "Project Assets", description: "You can setup your project assets here. You can add repositories, or add documents related to your project", side: 'top'}},
  ],
  // statusPanel: [
  //   {element: "#statusPanel", popover: {title: "Status Panel", description: "You can see the status and progress of your config task here."}},
  // ]
}

export const getRequiredSteps = (attribute) => {
  return DRIVER_DICTIONARY[attribute];
}

export const modifyTourSteps = (attribute, element, message) => {
  if(Array.isArray(DRIVER_DICTIONARY[attribute])){
    DRIVER_DICTIONARY[attribute].forEach((item, index, arr) => {
      if (item.element === element){
        arr[index] = {element: item.element, popover: {title: item.popover.title, description: message}};
      }
    });
  }
}

export const getFilteredSteps = () => {
    return DRIVER_STEPS.filter((step) => {
      if (typeof window === "undefined") {
        return false; // Skip filtering on the server
      }
      return document.querySelector(step.element) !== null;
    })
};