
export enum PatternType {
  Monolithic = "monolithic",
  MonolithicApplication = "monolithic-application",
  MonolithicService = "monolithic-service",
  MultiContainerService = "multi-container-service",
  MultiContainerSingleComponent = "multi-container-single-component",
  Adaptive = "adaptive",
}
  
export const CONFIG_TIMING_BY_PATTERN: Record<
  PatternType,
  Record<string, number>
> = {
  [PatternType.Monolithic]: {
    project_autoconfig: 40,
    requirements_autoconfig: 40,
    epic_autoconfig: 320,
    user_story_autoconfig: 1600,
    testcase_autoconfig: 1600,
    component_testcase_autoconfig: 320,
    architectural_requirements_autoconfig: 40,
    system_context_autoconfig: 40,
    container_autoconfig: 640,
    components_autoconfig: 1280,
    interface_autoconfig: 320,
    design_autoconfig: 640,
    documentation_autoconfig: 320,
    termination: 0,
  },
  [PatternType.MonolithicApplication]: {
    project_autoconfig: 40,
    requirements_autoconfig: 40,
    epic_autoconfig: 320,
    user_story_autoconfig: 1600,
    testcase_autoconfig: 1600,
    component_testcase_autoconfig: 320,
    architectural_requirements_autoconfig: 40,
    system_context_autoconfig: 40,
    container_autoconfig: 640,
    components_autoconfig: 1280,
    interface_autoconfig: 320,
    design_autoconfig: 640,
    documentation_autoconfig: 320,
    termination: 0,
  },
  [PatternType.MonolithicService]: {
    project_autoconfig: 40,
    requirements_autoconfig: 40,
    epic_autoconfig: 320,
    user_story_autoconfig: 1600,
    testcase_autoconfig: 1600,
    component_testcase_autoconfig: 320,
    architectural_requirements_autoconfig: 40,
    system_context_autoconfig: 40,
    container_autoconfig: 640,
    components_autoconfig: 1280,
    interface_autoconfig: 320,
    design_autoconfig: 640,
    documentation_autoconfig: 320,
    termination: 0,
  },
  [PatternType.MultiContainerService]: {
    project_autoconfig: 40,
    requirements_autoconfig: 40,
    epic_autoconfig: 320,
    user_story_autoconfig: 1600,
    testcase_autoconfig: 1600,
    component_testcase_autoconfig: 320,
    architectural_requirements_autoconfig: 40,
    system_context_autoconfig: 40,
    container_autoconfig: 640,
    components_autoconfig: 1280,
    interface_autoconfig: 320,
    design_autoconfig: 640,
    documentation_autoconfig: 320,
    termination: 0,
  },
  [PatternType.MultiContainerSingleComponent]: {
    project_autoconfig: 40,
    requirements_autoconfig: 40,
    epic_autoconfig: 320,
    user_story_autoconfig: 1600,
    testcase_autoconfig: 1600,
    component_testcase_autoconfig: 320,
    architectural_requirements_autoconfig: 40,
    system_context_autoconfig: 40,
    container_autoconfig: 640,
    components_autoconfig: 1280,
    interface_autoconfig: 320,
    design_autoconfig: 640,
    documentation_autoconfig: 320,
    termination: 0,
  },
  [PatternType.Adaptive]: {
    project_autoconfig: 40,
    requirements_autoconfig: 40,
    epic_autoconfig: 320,
    user_story_autoconfig: 1600,
    testcase_autoconfig: 1600,
    component_testcase_autoconfig: 40,
    architectural_requirements_autoconfig: 40,
    system_context_autoconfig: 40,
    container_autoconfig: 120,
    components_autoconfig: 40,
    interface_autoconfig: 40,
    design_autoconfig: 160,
    documentation_autoconfig: 320,
    termination: 0,
  },
};