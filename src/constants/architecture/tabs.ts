import { FaProjectDiagram, FaTasks, FaComments } from 'react-icons/fa';
import { IconType } from 'react-icons';

interface Tab {
    id: number;
    name: string;
    label: string;
    icon: IconType;
}
export const ARCHITECTURE_TABS: Tab[] = [
    {
        id: 1,
        name: "architecture-requirement",
        label: "Architecture Requirements",
        icon: FaProjectDiagram
    },
    {
        id: 2,
        name: "system-context",
        label: "System Context",
        icon: FaTasks
    },
    {
        id: 3,
        name: "container",
        label: "Containers",
        icon: FaTasks
    },
    {
        id: 4,
        name: "component",
        label: "Components",
        icon: FaTasks
    },
    {
        id: 5,
        name: "design-details",
        label: "Design Details",
        icon: FaTasks
    },
    {
        id: 6,
        name: "design",
        label: "Designs",
        icon: FaComments
    },
    {
        id: 7,
        name: "interfaces",
        label: "Interfaces",
        icon: FaProjectDiagram
    },
    {
        id: 8,
        name: "software-architecture",
        label: "SAD",
        icon: FaTasks
    },
    {
        id: 9,
        name: "api-docs",
        label: "API Docs",
        icon: FaComments
    }
];

export const DEFAULT_TAB = "architecture-requirement";