// src/services/projectService.js
// Updated to handle backend-only projects properly

/**
 * Project Service for handling project-related operations
 */

import { startProjectInit } from "@/utils/projectApi";

/**
 * Determines if the project is backend-only based on selected frameworks and API response
 * @param {string} selectedfrontendFramework - Selected frontend framework
 * @param {string} selectedbackendFramework - Selected backend framework
 * @param {Object} projectInitResponse - API response from project initialization
 * @returns {boolean} - True if this is a backend-only project
 */
const isBackendOnlyProject = (selectedfrontendFramework, selectedbackendFramework, projectInitResponse) => {
  // Check if frontend framework is explicitly "None" or empty
  const frontendIsNone = !selectedfrontendFramework || 
                         selectedfrontendFramework.trim() === "" || 
                         selectedfrontendFramework === "None";
  
  // Check if backend framework is selected
  const backendIsSelected = selectedbackendFramework && 
                           selectedbackendFramework.trim() !== "" && 
                           selectedbackendFramework !== "None";
  
  // Check API response structure - backend projects have backend container but no frontend
  const hasBackendContainer = projectInitResponse.backend?.container;
  const hasFrontendContainer = projectInitResponse.frontend?.container;
  
  return (frontendIsNone && backendIsSelected) || (hasBackendContainer && !hasFrontendContainer);
};

/**
 * Fetches the project blueprint based on the user input
 * @param {string} projectDescription - The user's project description
 * @param {string} selectedfrontendFramework - Selected frontend framework
 * @param {string} selectedbackendFramework - Selected backend framework
 * @param {Array} selectedBuildTypes - Array of selected build types
 * @param {string} project_name - Optional project name
 * @returns {Promise<Object>} - The project blueprint object
 */
export const fetchProjectBlueprint = async (
  projectDescription, 
  selectedfrontendFramework = "", 
  selectedbackendFramework = "", 
  selectedBuildTypes = [], 
  project_name = ""
) => {  
  try {
    // Validate inputs
    if (!projectDescription || projectDescription.trim() === "") {
      throw new Error("Project description is required");
    }

    
    
    
    
    
    

    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    
    const projectInitResp = await startProjectInit(
      projectDescription, 
      project_name, 
      selectedfrontendFramework, 
      selectedbackendFramework, 
      selectedBuildTypes
    );
    
    
    

    // Validate API response
    if (!projectInitResp) {
      console.error("No response received from API");
      throw new Error("No response received from the server. Please check your connection and try again.");
    }

    if (!projectInitResp.llmResponse) {
      console.error("Invalid API response: Missing llmResponse");
      console.error("Available keys:", Object.keys(projectInitResp));
      throw new Error("Invalid API response: Missing llmResponse");
    }

    const projectInitResponse = projectInitResp.llmResponse;
    
    
    
    // Extract data from the actual API response structure with fallbacks
    const overview = projectInitResponse.overview || {};
    const frontend = projectInitResponse.frontend || {};
    const backend = projectInitResponse.backend || {};
    
    
    
    
    

    // Determine if this is a backend-only project
    const isBackendOnly = isBackendOnlyProject(selectedfrontendFramework, selectedbackendFramework, projectInitResponse);
    
    

    // Build tech stack based on selected frameworks and project type
    const techStack = {
      frontend: [],
      backend: [],
      database: []
    };
    
    
    
    // Handle tech stack based on project type
    if (isBackendOnly) {
      // Backend-only project logic
      
      
      // Set frontend to "None" for backend-only projects
      techStack.frontend = ["None"];
      
      // Add backend framework
      if (selectedbackendFramework && selectedbackendFramework.trim() !== "" && selectedbackendFramework !== "None") {
        
        techStack.backend.push(selectedbackendFramework);
      } else if (backend.container?.framework) {
        
        techStack.backend.push(backend.container.framework);
      } else if (overview.backend_framework) {
        
        techStack.backend.push(overview.backend_framework);
      }
      
    } else {
      // Frontend/fullstack project logic (existing logic)
      if (selectedfrontendFramework && selectedfrontendFramework.trim() !== "" && selectedfrontendFramework !== "None") {
        
        techStack.frontend.push(selectedfrontendFramework);
      }
      
      if (selectedbackendFramework && selectedbackendFramework.trim() !== "" && selectedbackendFramework !== "None") {
        
        techStack.backend.push(selectedbackendFramework);
      }
      
      // Process build types for additional framework mapping
      if (selectedBuildTypes && selectedBuildTypes.length > 0) {
        
        
        selectedBuildTypes.forEach(buildType => {
          
          
          switch (buildType) {
            case 'web':
              if (techStack.frontend.length === 0 && overview.frontend_framework) {
                
                techStack.frontend.push(overview.frontend_framework);
              }
              break;
            case 'mobile':
              if (techStack.frontend.length === 0 && overview.frontend_framework) {
                
                techStack.frontend.push(overview.frontend_framework);
              }
              break;
            case 'backend':
              if (techStack.backend.length === 0 && overview.backend_framework) {
                
                techStack.backend.push(overview.backend_framework);
              }
              break;
            case 'database':
              if (overview.database_framework) {
                
                techStack.database.push(overview.database_framework);
              }
              break;
            default:
              
          }
        });
      }
      
      // Fallback to API response frameworks if none selected
      if (techStack.frontend.length === 0 && overview.frontend_framework) {
        
        techStack.frontend.push(overview.frontend_framework);
      }
      if (techStack.backend.length === 0 && overview.backend_framework) {
        
        techStack.backend.push(overview.backend_framework);
      }
    }
    
    // Handle database framework
    if (techStack.database.length === 0 && overview.database_framework) {
      
      techStack.database.push(overview.database_framework);
    }
    
    // Ensure arrays have default values if empty
    if (techStack.frontend.length === 0) {
      
      techStack.frontend = [isBackendOnly ? "None" : "React 19"];
    }
    if (techStack.backend.length === 0) {
      
      techStack.backend = ["None"];
    }
    if (techStack.database.length === 0) {
      
      techStack.database = ["None"];
    }
    
    
    
    
    // Build blueprint with conditional properties based on project type
    const blueprint = {
      id: projectInitResp.projectNodeInfo?.id || Math.random().toString(36).substring(2, 9),
      name: projectInitResponse.projectTitle || overview.project_name || project_name || projectDescription || "New Project",
      description: projectInitResponse.description || overview.description || projectDescription || "",
      features: projectInitResponse.features || backend.container?.features || [],
      techStack: techStack,
      estimatedTime: "1-2 weeks",
      complexity: "simple",
      projectInfo: projectInitResp.projectNodeInfo || null,
      isBackendOnly: isBackendOnly
    };

    // Only add architecturePattern if it's provided by the API
    if (projectInitResponse.architecturePattern) {
      blueprint.architecturePattern = projectInitResponse.architecturePattern;
    }

    // Add frontend-specific properties only for non-backend-only projects
    if (!isBackendOnly) {
      blueprint.colors = projectInitResponse.colors || {
        primary: "#4CAF50",
        secondary: "#FFC107",
        accent: "#2196F3"
      };
      blueprint.theme = frontend.container?.theme || "light";
      blueprint.layoutDescription = projectInitResponse.layoutDescription || 
                                   frontend.container?.layoutDescription || 
                                   "Modern responsive layout";
    } else {
      // Backend-only projects don't need colors, theme, or layout description
      
      blueprint.layoutDescription = projectInitResponse.layoutDescription || 
                                   "RESTful API architecture with clear endpoint organization";
    }
    
    
    
    
    return blueprint;
    
  } catch (error) {
    console.error("=== ERROR IN FETCH BLUEPRINT ===");
    console.error("Error name:", error.name);
    console.error("Error message:", error.message);
    console.error("Error stack:", error.stack);
    
    // Provide specific error messages based on error type
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new Error("Network error: Unable to connect to the server. Please check your internet connection and try again.");
    }
    
    if (error.message.includes('Failed to initialize project')) {
      throw new Error("Server error: The project initialization service is currently unavailable. Please try again later.");
    }
    
    if (error.message.includes('Invalid API response')) {
      throw new Error("Server error: Received invalid response from the API. Please try again.");
    }
    
    if (error.message.includes('Project description is required')) {
      throw new Error("Please provide a project description before proceeding.");
    }
    
    // Re-throw the original error with more context
    throw new Error(`Failed to generate project blueprint: ${error.message}`);
  }
};

/**
 * Implements the project based on the blueprint
 * @param {Object} blueprintData - The project blueprint data
 * @returns {Promise<Object>} - The implemented project response
 */
export const implementProject = async (blueprintData) => {
  try {
    
    
    
    // Verify we have the project ID from the blueprint
    if (!blueprintData.id) {
      console.error("No project ID found in the blueprint data!");
      throw new Error("Missing project ID in blueprint data");
    }
    
    // Simulate network delay for API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Use the ID from the blueprint instead of generating a new one
    const projectId = blueprintData.id;
    
    // Extract project details from the blueprint data
    // This includes any modifications made in the ProjectCreationModal
    const projectName = blueprintData.name || "New Project";
    const projectDesc = blueprintData.description || "";
    const featuresCount = blueprintData.features?.length || 0;
    
    // Handle framework names based on project type
    let frameworkName, languageName;
    
    if (blueprintData.isBackendOnly) {
      frameworkName = blueprintData.techStack?.backend?.[0] || "FastAPI";
      // Determine language based on backend framework
      const backendFramework = blueprintData.techStack?.backend?.[0] || "FastAPI";
      if (backendFramework.includes("Python") || backendFramework.includes("FastAPI") || backendFramework.includes("Django")) {
        languageName = "Python";
      } else if (backendFramework.includes("Node.js") || backendFramework.includes("Express")) {
        languageName = "JavaScript (ES6+)";
      } else if (backendFramework.includes("Ruby")) {
        languageName = "Ruby";
      } else if (backendFramework.includes("PHP")) {
        languageName = "PHP";
      } else if (backendFramework.includes("Java")) {
        languageName = "Java";
      } else {
        languageName = "Python"; // Default fallback
      }
    } else {
      frameworkName = blueprintData.techStack?.frontend?.[0] || "React 19";
      // Determine language based on frontend framework
      const frontendFramework = blueprintData.techStack?.frontend?.[0] || "React 19";
      if (frontendFramework.includes("React") || frontendFramework.includes("Next.js") || frontendFramework.includes("Vue.js") || frontendFramework.includes("Angular") || frontendFramework.includes("Svelte")) {
        languageName = "JavaScript (ES6+)";
      } else if (frontendFramework.includes("TypeScript")) {
        languageName = "TypeScript";
      } else {
        languageName = "JavaScript (ES6+)"; // Default fallback
      }
    }
    
    // projectInit API response
    const projectInitResponse = {
      success: true,
      id: projectId, // Use the same ID from the blueprint
      properties: {
        Title: projectName,
        Name: projectName,
        Status: "In Progress",
        CreatedAt: new Date().toISOString(),
        Description: projectDesc,
        Features: featuresCount,
        Framework: frameworkName,
        Language: languageName,
        ProjectType: blueprintData.isBackendOnly ? "Backend API" : "Full Stack Application"
      },
      message: `${blueprintData.isBackendOnly ? 'Backend API' : 'Project'} implementation started successfully!`
    };
    
    
    
    
    return projectInitResponse;
    
  } catch (error) {
    console.error("=== ERROR IN IMPLEMENT PROJECT ===");
    console.error("Error:", error);
    throw new Error(`Failed to implement project: ${error.message}`);
  }
};