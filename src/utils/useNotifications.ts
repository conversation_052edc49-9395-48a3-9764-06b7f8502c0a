import { useState, useEffect, useContext } from "react";
import { PanelContext } from "../components/Context/PanelContext";
import { 
  fetchNotifications as fetchNotificationsFromApi,
  markAllNotificationAsRead,
  markNotificationAsRead 
} from "@/utils/api";

export const useNotifications = () => {
  const [notifications, setNotifications] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<any>(null);
  const [notificationTooltips, setNotificationTooltips] = useState<{
    [key: number]: string;
  }>({});
  const { getProject } = useContext(PanelContext);

  const fetchNotifications = async () => {
    const cachedData = sessionStorage.getItem("notifications");
    const notificationsFromCache = cachedData ? JSON.parse(cachedData) : [];

    if (notificationsFromCache.length) {
      setNotifications(notificationsFromCache);
    }

    try {
      const response = await fetchNotificationsFromApi();
      if (!cachedData || JSON.stringify(response) !== cachedData) {
        setNotifications(response);
        sessionStorage.setItem("notifications", JSON.stringify(response));
      }
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  const fetchProject = async (projectid: any) => {
    try {
      const project = await getProject(projectid);
      if (project && project.status !== "Node not found") {
        setNotificationTooltips((prev) => ({
          ...prev,
          [projectid]: "Click to view",
        }));
        return project;
      } else {
        setNotificationTooltips((prev) => ({
          ...prev,
          [projectid]: "Project is deleted",
        }));
        return null;
      }
    } catch (error) {
      setNotificationTooltips((prev) => ({
        ...prev,
        [projectid]: "Project is deleted",
      }));
      return null;
    }
  };

  const markRead = async (
    notification_id: any,
    handleValueChange: (value: number) => void
  ) => {
    try {
      const updatedNotifications = notifications.map((notification) =>
        notification.notification_id === notification_id
          ? { ...notification, is_read: true }
          : notification
      );
      setNotifications(updatedNotifications);
      sessionStorage.setItem("notifications", JSON.stringify(updatedNotifications));

      await markNotificationAsRead(notification_id);
      await fetchNotifications();
      handleValueChange(
        updatedNotifications.filter((notification) => !notification.is_read).length
      );
    } catch (err) {
    }
  };

  const markAllRead = async (handleValueChange: (value: number) => void) => {
    try {
      const updatedNotifications = notifications.map((notification) => ({
        ...notification,
        is_read: true,
      }));
      setNotifications(updatedNotifications);
      sessionStorage.setItem("notifications", JSON.stringify(updatedNotifications));

      await markAllNotificationAsRead();
      await fetchNotifications();
      handleValueChange(0);
    } catch (err) {
    }
  };

  useEffect(() => {
    fetchNotifications();
  }, []);

  return {
    notifications,
    loading,
    error,
    markRead,
    markAllRead,
    setNotifications,
    notificationTooltips,
    fetchProject,
  };
};
