// utils/figmaAPI.js
"use client";

import { getHeaders } from "./api";
import Cookies from 'js-cookie';
const base_url = process.env.NEXT_PUBLIC_API_URL;
const SHOW_NAME = 'figma';
const ws_url = process.env.NEXT_PUBLIC_WS_URL;

export const getFigmaFile = async (figmaLink, projectId) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/file?figma_link=${encodeURIComponent(figmaLink)}&project_id=${projectId}`, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      const data = await response.json();
      throw new Error(data.detail || "Failed to fetch Figma file");
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

export async function getFigmaFiles(projectId) {
  const endpoint = `${base_url}/${SHOW_NAME}/get_figma_files`;

  try {
    const response = await fetch(`${endpoint}?project_id=${projectId}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (!response.ok) {
      throw new Error('Failed to get Figma files');
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
}
export async function getFigmaJsonFiles(projectId, fileKey) {
  const endpoint = `${base_url}/${SHOW_NAME}/list_figma_files_CGA`;

  try {
    const response = await fetch(`${endpoint}?project_id=${projectId}&file_key=${fileKey}`, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Failed to get Figma JSON files: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching Figma JSON files:', error);
    throw error;
  }
}

export const getFrameImage = async (figmaLink, frameId) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/frame-image?figma_link=${encodeURIComponent(figmaLink)}&frame_id=${encodeURIComponent(frameId)}`, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to fetch frame image");
    }

    const data = await response.json();
    return data.imageUrl;
  } catch (error) {
    
    throw error;
  }
};

export const downloadAllFrames = async (figmaLink) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/download?figma_link=${encodeURIComponent(figmaLink)}`, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to download all frames");
    }

    return await response.blob();
  } catch (error) {
    
    throw error;
  }
};

export const linkFramesToDesign = async (designId, figmaLink, selectedFrames) => {
  try {
    const response = await fetch('/api/figma/link-frames', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        designId,
        figmaLink,
        frames: selectedFrames
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to link frames');
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

export const getFramePreview = async (figmaLink, frameId) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/frame-preview?figma_link=${encodeURIComponent(figmaLink)}&frame_id=${encodeURIComponent(frameId)}`, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to fetch frame preview");
    }

    const data = await response.json();
    return data.previewUrl;
  } catch (error) {
    
    throw error;
  }
};

export const getFrameThumbnail = async (figmaLink, frameId) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/frame-thumbnail?figma_link=${encodeURIComponent(figmaLink)}&frame_id=${encodeURIComponent(frameId)}`, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error("Failed to fetch frame thumbnail");
    }

    const data = await response.json();
    return data.thumbnailUrl;
  } catch (error) {
    
    throw error;
  }
};

export const linkFigmaComponents = async (designId, file, unlink = false) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/link-figma-components?design_id=${designId}`, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify({
        figma_link: file.url,  // Changed from figmaLink to file.url
        name: file.name,
        sizes: file.sizes,
        added_by: file.added_by,
        unlink: unlink
      }),
    });

    if (!response.ok) {
      const data = await response.json();
      throw new Error(data.detail || 'Failed to link Figma components');
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};


export async function deleteFigmaFile(projectId, name, url) {
  const base_url = backend_base_url;
  const endpoint = `${base_url}/figma/delete_figma_file`;

  try {
    const response = await fetch(`${endpoint}?project_id=${projectId}`, {
      method: 'DELETE',
      headers: await getHeaders(),
      body: JSON.stringify({
        name: name,
        url: url
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function addFigmaFile(projectId, name, url) {
  const base_url = backend_base_url;
  const endpoint = `${base_url}/figma/add_figma_file`;

  try {
    const response = await fetch(`${endpoint}?project_id=${projectId}`, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify({
        name: name,
        url: url
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function updateFigmaFile(projectId, name, figmaLink) {
  const base_url = backend_base_url;
  const endpoint = `${base_url}/figma/update_figma_file`;

  try {
    const response = await fetch(`${endpoint}?project_id=${projectId}`, {
      method: 'PUT',
      headers: await getHeaders(),
      body: JSON.stringify({
        name: name,
        url: figmaLink
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail);
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
}

export const startDiscussion = async (projectId, selectedDesignId, designType) => {
  try {
    if (designType === 'image') {
      designType = 'images'
    }
    else{
      designType = 'figma'
    }
    const response = await fetch(
      `${base_url}/${SHOW_NAME}/start_discussion?project_id=${parseInt(projectId)}&extraction_type=${designType}`,
      {
        method: 'POST',
        headers: await getHeaders(),
        body: JSON.stringify({ selected_design_id: selectedDesignId }),
      }
    );

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.detail);
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

// export const sendFigmaExtraction = async (discussionId, selectedDesignId, message_to_send,designType, onMessage, onError, controller) => {
//   const ctrl = controller || new AbortController();
//   const { signal } = ctrl;

//   if (signal.aborted) {
//     return ctrl;
//   }
//   if (designType === 'image') {
//     designType = 'images'
//   }

//   try {
//     let streamClosed = false;

//     await fetchEventSource(`${base_url}/${SHOW_NAME}/figma_extraction?discussion_id=${discussionId}`, {
//       method: 'POST',
//       headers: {
//         ...(await getHeaders()),
//         'Authorization': `Bearer ${Cookies.get('idToken')}`,
//         'Accept': 'text/event-stream',
//         'Content-Type': 'application/json',
//       },
//       body: JSON.stringify({
//         discussion_id: discussionId,
//         selected_design_id: selectedDesignId,
//         user_request: message_to_send,
//         extraction_type: designType,
//       }),
//       openWhenHidden: true,
//       signal,

//       onopen(response) {
//         if (response.ok && response.status === 200) {
//           return Promise.resolve();
//         }
//         return Promise.reject(new Error(`Failed to open stream: ${response.status} ${response.statusText}`));
//       },

//       onmessage(event) {
//         if (streamClosed || signal.aborted) return;

//         if (event) {
//           try {
//             // handle ping events gracefully
//             // sample : ping - 2025-04-15 09:01:20.998887+00:00
//             // check if event is a string and contains ping
//             if (!event.data || event.data === 'ping') {
//               return;
//             }
//             const parsedData = JSON.parse(event.data);

//             // Handle the stop signal
//             if (parsedData.stop) {
//               streamClosed = true;
//               ctrl.abort();
//             }

//             onMessage(parsedData);
//           } catch (_err) {
//             streamClosed = true;
//             ctrl.abort();
//             onError(new Error('Failed to parse message data'));
//           }
//         }
//       },

//       onclose() {
        
//         streamClosed = true;
//         ctrl.abort();
//       },

//       onerror(err) {
//         
//         streamClosed = true;
//         ctrl.abort();
//         onError(err);
//         return Promise.reject(err);
//       },
//     });

//     return ctrl;
//   } catch (error) {
//     ctrl.abort();
//     throw error;
//   }
// };


const activeWebSockets = {};

// Update sendFigmaExtraction to use persistent connections
export const sendFigmaExtraction = async (discussionId, selectedDesignId,selectedFrame, message_to_send, designType) => {
  // Normalize designType
  if (designType === 'image') {
    designType = 'images';
  }
  
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/figma_extraction?discussion_id=${discussionId}`, {
      method: 'POST',
      headers: {
        ...(await getHeaders()),
        'Authorization': `Bearer ${Cookies.get('idToken')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        discussion_id: discussionId,
        user_request: message_to_send,
        selected_design_id: selectedDesignId,
        extraction_type: designType,
        selected_frame: selectedFrame.id,
      })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to send Figma extraction request');
    }
    
    return await response.json();
  } catch (error) {
    
    throw error;
  }
}
// Add a utility function to explicitly close a WebSocket connection when needed
export const closeWebSocketConnection = (discussionId) => {
  if (activeWebSockets[discussionId]) {
    
    activeWebSockets[discussionId].socket.close();
    delete activeWebSockets[discussionId];
    return true;
  }
  return false;
};

// Add a cleanup function that can be called when component unmounts
export const cleanupWebSocketConnections = () => {
  Object.keys(activeWebSockets).forEach(id => {
    if (activeWebSockets[id].socket.readyState === WebSocket.OPEN) {
      activeWebSockets[id].socket.close();
    }
    delete activeWebSockets[id];
  });
  
};
// Helper function to register a Figma extraction agent
export const registerFigmaExtractionAgent = async (session_id) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/register_agent/${session_id}`, {
      method: 'POST',
      headers: {
        ...(await getHeaders()),
        'Authorization': `Bearer ${Cookies.get('idToken')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        session_id: session_id,
        user_id: Cookies.get('userId'),
        tenant_id: Cookies.get('tenant_id')
      })
    });
    
    if (!response.ok) {
      throw new Error(`Failed to register Figma extraction agent: ${response.status} ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    
    return { status: 'error', message: error.message };
  }
};
export const getFigmaMessagesHistory = async (discussionId) => {
  try {
    const response = await fetch(`${base_url}/${SHOW_NAME}/messages_history?discussion_id=${discussionId}`, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      const data = await response.json();
      throw new Error(data.detail || "Failed to fetch messages history");
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};


export const getFigmaCodeFiles = async (discussionId) => {

  const response = await fetch(`${base_url}/${SHOW_NAME}/files/${discussionId}`, {
    method: "GET",
    headers: await getHeaders(),
  });

  if (!response.ok) {
    const data = await response.json();
    throw new Error(data.detail || "Failed to fetch messages history");
  }

  return await response.json();


}


export const getPastDiscussions = async (projectId, selectedDesignId) => {
  const response = await fetch(
    `${base_url}/${SHOW_NAME}/past_discussions/${projectId}?selected_design_id=${selectedDesignId}`,
    {
      method: "GET",
      headers: await getHeaders(),
    }
  );

  if (!response.ok) {
    const data = await response.json();
    throw new Error(data.detail || "Failed to fetch past discussions");
  }

  return await response.json();
};


export const uploadExternalImages = async (projectId, groupName, files) => {
  try {
    const url = new URL(`${base_url}/${SHOW_NAME}/create_ext_images`);
    url.searchParams.append('project_id', projectId);
    url.searchParams.append('group_name', groupName);

    const formData = new FormData();
    

    for (const file of files) {
      formData.append('files', file, file.name);
      
    }

    const response = await fetch(url.toString(), {
      method: "POST",
      headers: {
        'Authorization': `Bearer ${Cookies.get('idToken')}`,

      },
      body: formData,
    });

    // Handle response same as before
    const responseData = await response.json();
    if (!response.ok) {
      throw new Error(responseData.message || responseData.detail ||
        `Failed to upload images (Status: ${response.status})`);
    }

    return responseData;
  } catch (error) {
    
    throw error;
  }
};

export const addMoreImages = async (projectId, figmaExtId, files) => {
  try {
    const url = new URL(`${base_url}/${SHOW_NAME}/add_more_images/${projectId}/${figmaExtId}`);
    
    const formData = new FormData();
    for (const file of files) {
      formData.append('files', file, file.name);
    }

    const response = await fetch(url.toString(), {
      method: "POST",
      headers: {
        'Authorization': `Bearer ${Cookies.get('idToken')}`,
      },
      body: formData,
    });

    const responseData = await response.json();
    if (!response.ok) {
      throw new Error(responseData.message || responseData.detail || 
        `Failed to add images (Status: ${response.status})`);
    }

    return responseData;
  } catch (error) {
    
    throw error;
  }
};

export const mergeChanges = async (projectId, discussionId, figmaExtId,designType) => {
  try {
    const requestBody = {
      project_id: projectId,
      discussion_id: discussionId,
      figma_ext_id: figmaExtId,
      type_of: designType

    };

    const response = await fetch(`${base_url}/${SHOW_NAME}/merge_changes`, {
      method: "POST",
      headers: await getHeaders(),
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const data = await response.json();
      throw new Error(data.detail || "Failed to merge changes");
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

// Functions to call the rename and delete endpoints for external images

export const renameExternalImage = async (figmaExtId, fileId, newFilename) => {
  try {
    const response = await fetch(
      `${base_url}/${SHOW_NAME}/rename_ext_image/${figmaExtId}/${fileId}?new_filename=${encodeURIComponent(newFilename)}`,
      {
        method: "PUT",
        headers: await getHeaders(),
      }
    );

    if (!response.ok) {
      const data = await response.json();
      throw new Error(data.message || "Failed to rename image");
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};


export const deleteExternalImage = async (figmaExtId, fileId) => {
  try {
    const response = await fetch(
      `${base_url}/${SHOW_NAME}/delete_ext_image/${figmaExtId}/${fileId}`,
      {
        method: "DELETE",
        headers: await getHeaders(),
      }
    );

    if (!response.ok) {
      const data = await response.json();
      throw new Error(data.message || "Failed to delete image");
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

export const downloadFigmaCode = async (discussionId, customFilename = null) => {
  try {
    // Validate required parameter
    if (!discussionId) {
      throw new Error("Discussion ID is required");
    }

    const response = await fetch(`${base_url}/${SHOW_NAME}/download_figma_code/${discussionId}`, {
      method: "GET",
      headers: await getHeaders()
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(errorData?.detail || `Failed to download code (${response.status})`);
    }

    // Get filename from response headers or use custom/default
    const contentDisposition = response.headers.get('Content-Disposition');
    let filename = customFilename ? `${customFilename}.zip` : `figma_code${discussionId}.zip`;
    
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1].replace(/['"]/g, '');
      }
    }

    // Convert response to blob
    const blob = await response.blob();
    
    // Create download link and trigger download
    const downloadUrl = window.URL.createObjectURL(blob);
    const downloadLink = document.createElement('a');
    downloadLink.href = downloadUrl;
    downloadLink.download = filename;
    
    // Append to body, click, and cleanup
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
    
    // Cleanup blob URL
    window.URL.revokeObjectURL(downloadUrl);
    
    return true;

  } catch (error) {
    console.error('Download error:', error);
    throw error;
  }
};
/**
 * Upload a Figma JSON file as an attachment
 * @param {string} projectId - The project ID
 * @param {string} filePath - The file path on server
 * @param {string} filename - The filename
 * @param {string} relativePath - The relative path
 * @returns {Promise<Object>} Upload result
 */
export async function uploadFigmaJsonFile(projectId, filePath, filename, relativePath) {
  const endpoint = `${base_url}/${SHOW_NAME}/upload_figma_json`;

  try {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        ...await getHeaders(),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        project_id: parseInt(projectId),
        file_path: filePath,
        filename: filename,
        relative_path: relativePath
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `Upload failed: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error uploading Figma JSON file:', error);
    throw error;
  }
}