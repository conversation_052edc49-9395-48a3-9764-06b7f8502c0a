// utils/discussionAPI.js
"use client";
const backend_base_url = process.env.NEXT_PUBLIC_API_URL;

import { getHeaders } from "./api";

export async function getDiscussionParticipants(discussionId, participantType = 'all') {
    const encodedDiscussionId = encodeURIComponent(discussionId);
    const encodedParticipantType = encodeURIComponent(participantType);
    const url = `${backend_base_url}/discussion/${encodedDiscussionId}/users?participant_type=${encodedParticipantType}`;
  
    
  
    try {
      const response = await fetch(url, {
        method: "GET",
        headers: await getHeaders(),
      });
  
      
  
      if (!response.ok) {
        throw new Error(`HTTP error status: ${response.status}`);
      }
  
      const data = await response.json();
      
      return data;
    } catch (error) {
      
      throw error;
    }
}

export async function getCommentsForDiscussion(discussionId, modificationIndex) {
    const encodedDiscussionId = encodeURIComponent(discussionId);
    let url = `${backend_base_url}/discussion/${encodedDiscussionId}/comments`;
    
    if (modificationIndex != null) {
      url += `?modification_index=${modificationIndex}`;
    }
    
  
    try {
      const response = await fetch(url, {
        method: "GET",
        headers: await getHeaders(),
      });
  
      
  
      if (!response.ok) {
        throw new Error(`HTTP error status: ${response.status}`);
      }
  
      const data = await response.json();
  
      // Check if we have a message indicating no modifications
      if (data.message === "No modifications found") {
        return { comments: [] };
      }
  
      // Return the comments data
      return data;
    } catch (error) {
      
      throw error;
    }
}

export async function addOrEditCommentToDiscussion(discussionId, modificationIndex, comment, commentIndex = null) {
    const encodedDiscussionId = encodeURIComponent(discussionId);
    let url = `${backend_base_url}/discussion/${encodedDiscussionId}/comments/`;

    const queryParams = new URLSearchParams();
    if (modificationIndex != null) {
      queryParams.append('modification_index', modificationIndex);
    }
    if (commentIndex != null) {
      queryParams.append('comment_index', commentIndex);
    }

    url += `?${queryParams.toString()}`;
  
    
  
    try {
      const response = await fetch(url, {
        method: "POST",
        headers: await getHeaders(),
        "Content-Type": "application/json",

        body: JSON.stringify(comment ),
      });
  
      
  
      if (!response.ok) {
        throw new Error(`HTTP error status: ${response.status}`);
      }
  
      const data = await response.json();
      return data;
    } catch (error) {
      
      throw error;
    }
}

export async function deleteCommentFromDiscussion(discussionId, modificationIndex, commentIndex) {
    const encodedDiscussionId = encodeURIComponent(discussionId);
    let url = `${backend_base_url}/discussion/${encodedDiscussionId}/comments`;

    const queryParams = new URLSearchParams({
      modification_index: modificationIndex,
      comment_index: commentIndex
    });

    url += `?${queryParams.toString()}`;
  
    
  
    try {
      const response = await fetch(url, {
        method: "DELETE",
        headers: await getHeaders(),
      });
  
      
  
      if (!response.ok) {
        throw new Error(`HTTP error status: ${response.status}`);
      }
  
      const data = await response.json();
      return data;
    } catch (error) {
      
      throw error;
    }
}

export async function mergeModificationRequest(discussionId, modificationIndex, type, approverId = null) {
  const encodedDiscussionId = encodeURIComponent(discussionId);
  let url = `${backend_base_url}/discussion/${encodedDiscussionId}/merge_request`;

  const queryParams = new URLSearchParams({
    modification_index: modificationIndex,
    type: type
  });

  if (approverId) {
    queryParams.append('approver_id', approverId);
  }

  url += `?${queryParams.toString()}`;

  

  try {
    const response = await fetch(url, {
      method: "PATCH",
      headers: await getHeaders(),
    });

    

    if (!response.ok) {
      throw new Error(`HTTP error status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    
    throw error;
  }
}

export async function getDiscussionModifications(discussionId) {
  const encodedDiscussionId = encodeURIComponent(discussionId);
  const url = `${backend_base_url}/discussion/${encodedDiscussionId}/modifications`;

  

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    

    if (!response.ok) {
      if (response.status === 404) {
        
        return [];
      }
      throw new Error(`HTTP error status: ${response.status}`);
    }

    const data = await response.json();
    return data.modifications;
  } catch (error) {
    
    throw error;
  }
}

