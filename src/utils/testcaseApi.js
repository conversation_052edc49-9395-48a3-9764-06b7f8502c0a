import { getHeaders} from './api';

// Use the correct environment variable for API URL
const backend_base_url = process.env.NEXT_PUBLIC_API_URL;

/**
 * Fetches test case categories for a project
 * @param {string|number} projectId - The ID of the project
 * @returns {Promise<Array>} - Array of test case categories
 */
export async function getTestCaseCategories(projectId) {
  const url = `${backend_base_url}/testcase/get_categories/${projectId}`;
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: await getHeaders(),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch test case categories: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    
    throw error;
  }
}

/**
 * Fetches test cases by category for a project
 * @param {string|number} projectId - The ID of the project
 * @param {string} categoryName - The name of the category
 * @returns {Promise<Array>} - Array of test cases in the specified category
 */
export async function getTestCasesByCategory(projectId, categoryName) {
  // Encode the category name for URL
  const encodedCategory = encodeURIComponent(categoryName);
  const url = `${backend_base_url}/testcase/get_testcases_by_category/${projectId}/${encodedCategory}`;
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: await getHeaders(),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch test cases by category: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    
    throw error;
  }
}

/**
 * Fetches test cases by node type for a project
 * @param {string|number} projectId - The ID of the project
 * @param {string} nodeType - The type of node (e.g., 'UserStory')
 * @returns {Promise<Array>} - Array of test cases for the specified node type
 */
export async function getTestCasesByNodeType(projectId, nodeType) {
  // Encode the node type for URL
  const encodedNodeType = encodeURIComponent(nodeType);
  const url = `${backend_base_url}/testcase/get_testcases_by_node_type/${projectId}/${encodedNodeType}`;
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: await getHeaders(),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch test cases by node type: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    
    throw error;
  }
}

/**
 * Fetches automatable test cases for a project based on automation status
 * @param {string|number} projectId - The ID of the project
 * @param {boolean} canBeAutomated - Filter for test cases that can/cannot be automated
 * @returns {Promise<Array>} - Array of test cases matching the automation criteria
 */
export async function getAutomatableTestCases(projectId, canBeAutomated) {
  const url = `${backend_base_url}/testcase/get_automatable_testcases/${projectId}?can_be_automated=${canBeAutomated}`;
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: await getHeaders(),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch automatable test cases: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    
    throw error;
  }
}

/**
 * Fetches the root test case for a specific test case in a project
 * @param {string|number} projectId - The ID of the project
 * @param {string|number} testCaseId - The ID of the test case to find the root for
 * @returns {Promise<Object>} - The root test case object
 */
export async function getTestCaseRoot(projectId, testCaseId) {
  const url = `${backend_base_url}/testcase/get_testcase_root/${projectId}/${testCaseId}`;
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: await getHeaders(),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch test case root: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    
    throw error;
  }
}


/**
 * Fetches the root test case by ID without requiring a project ID
 * @param {string|number} testCaseId - The ID of the test case to find the root for
 * @returns {Promise<Object>} - The root test case object
 */
export async function getTestCaseRootById(testCaseId) {
  const url = `${backend_base_url}/testcase/get_testcase_root_by_id/${testCaseId}`;
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: await getHeaders(),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch test case root by ID: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    
    throw error;
  }
}

/**
 * Exports test cases to Excel for a project
 * @param {string|number} projectId - The ID of the project
 * @returns {Promise<Blob>} - Excel file blob for download
 */
export async function exportTestCasesToExcel(projectId) {
  const url = `${backend_base_url}/discussion/export_test_cases/${projectId}`;
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: await getHeaders(),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to export test cases to Excel: ${response.status}`);
    }
    
    return await response.blob();
  } catch (error) {
    
    throw error;
  }
}
