import { backend_base_url, getHeaders } from './api';

/**
 * Updates a user's status in the system
 * @param {string} tenantId - The tenant/organization ID
 * @param {string} email - The email of the user to update
 * @param {string} status - The new status ('active' or 'inactive')
 * @returns {Promise<Object>} - The response from the API
 */
export async function updateUserStatus(tenantId, email, status) {
  if (!tenantId || !email || !status) {
    throw new Error('Tenant ID, email, and status are required');
  }

  const url = `${backend_base_url}/manage/super/update_user_status?tenant_id=${encodeURIComponent(tenantId)}&email=${encodeURIComponent(email)}&status=${encodeURIComponent(status)}`;

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: await getHeaders(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData?.detail || `Failed to update user status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
}

/**
 * Updates a user's status in the system by user ID
 * @param {string} tenantId - The tenant/organization ID
 * @param {string} userId - The ID of the user to update
 * @param {string} status - The new status ('active' or 'inactive')
 * @returns {Promise<Object>} - The response from the API
 */
export async function updateUserStatusById(tenantId, userId, status) {
  if (!tenantId || !userId || !status) {
    throw new Error('Tenant ID, user ID, and status are required');
  }

  const url = `${backend_base_url}/manage/super/update_user_status_by_id?tenant_id=${encodeURIComponent(tenantId)}&user_id=${encodeURIComponent(userId)}&status=${encodeURIComponent(status)}`;

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: await getHeaders(),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData?.detail || `Failed to update user status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
}

