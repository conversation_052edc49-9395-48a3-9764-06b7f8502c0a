import { useState, useEffect } from 'react';

export const useResponsiveDimensions = () => {
  const [mainContentWidth, setMainContentWidth] = useState('calc(100% - 12vw)');
  const [contentHeight, setContentHeight] = useState('78vh');

  const calculateDimensions = () => {
    const screenWidth = window.innerWidth;

    // Width calculation - adjusted to ensure consistent right padding
    if (screenWidth >= 1600) setMainContentWidth('calc(100% - 1rem)'); // Use fixed right padding
    else if (screenWidth >= 1280) setMainContentWidth('calc(100% - 1rem)');
    else if (screenWidth >= 1024) setMainContentWidth('calc(100% - 1rem)');
    else setMainContentWidth('calc(100% - 1rem)');

    // Height calculation
    const viewportHeight = window.innerHeight;
    const calculatedHeight = Math.min(viewportHeight * 0.74, 800);
    setContentHeight(`${calculatedHeight}px`);
  };

  useEffect(() => {
    // Initial calculation
    calculateDimensions();

    // Add resize listener
    window.addEventListener('resize', calculateDimensions);

    // Cleanup listener
    return () => window.removeEventListener('resize', calculateDimensions);
  }, []);

  return { mainContentWidth, contentHeight, calculateDimensions };
};
