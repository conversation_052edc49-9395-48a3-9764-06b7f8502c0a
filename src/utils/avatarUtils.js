import Image from 'next/image';
import kavia<PERSON><PERSON> from '../../public/logo/kavia_light_logo.svg'
export const getKaviaAvatar = () => {

    return (
        <>
                                        <div className="flex items-center mb-2">
                                  <div className="h-6 w-6 mr-2 flex items-center justify-center">
                                    <Image src={kaviaLogo} alt="Kavia Logo" width={24} height={24} />
                                </div>
                              </div>
        </>
    )
}

export const getUserAvatar = (name, username) => {
    return (
        <>
                
            <Image
            className="h-6 w-6 rounded-full "
            src={`https://ui-avatars.com/api/?name=${name || username}&background=F97316&color=FFFFFF`}
            alt="User Avatar"
            width={24}
            height={24}
            unoptimized
            />
                                  
        </>
    )
}

export const getUserAvatarV2= (name, picture_link=null) => {
    return (
        <Image 
        className="h-6 w-6 rounded-full flex-shrink-0 ml-4"
        src={picture_link || `https://ui-avatars.com/api/?name=${name}&background=A371F7&color=FFFFFF`}
        alt={name}
        width={24}
        height={24}
        unoptimized
      />
    )
}
