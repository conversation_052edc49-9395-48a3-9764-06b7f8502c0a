import crypto from 'crypto';

// Constants
const TENANT_SALT = process.env.NEXT_PUBLIC_TENANT_SALT;
const ROOT_TENANT_ID = process.env.NEXT_PUBLIC_ROOT_TENANT_ID;

if (!TENANT_SALT) {
    throw new Error("TENANT_SALT is not defined or empty");
}



export function encrypt(data, salt=TENANT_SALT) {
    const key = crypto.createHash('sha256').update(salt).digest().slice(0, 16); // Ensure 16-byte key
    const cipher = crypto.createCipheriv('aes-128-ecb', key, Buffer.alloc(0)); // Use an empty IV
    const encrypted = Buffer.concat([cipher.update(data, 'utf8'), cipher.final()]);
    return encrypted.toString('base64').replace(/=+$/, ''); // Base64 without padding
}

export function decrypt(data, salt=TENANT_SALT) {
    const key = crypto.createHash('sha256').update(salt).digest().slice(0, 16); // Ensure 16-byte key
    const decipher = crypto.createDecipheriv('aes-128-ecb', key, Buffer.alloc(0)); // Use an empty IV
    const decrypted = Buffer.concat([decipher.update(String(data), 'base64'), decipher.final()]);
    return decrypted.toString('utf8');
}


export function encryptTenantId(tenantId) {
    const key = crypto.createHash('sha256').update(TENANT_SALT).digest().slice(0, 16); // Ensure 16-byte key
    const cipher = crypto.createCipheriv('aes-128-ecb', key, Buffer.alloc(0)); // Use an empty IV
    const encrypted = Buffer.concat([cipher.update(tenantId, 'utf8'), cipher.final()]);
    return encrypted.toString('base64').replace(/=+$/, ''); // Base64 without padding
}

export function encryptString(data) {
    return encrypt(data, TENANT_SALT);
}

export function decryptString(data) {
    return decrypt(data, TENANT_SALT);
}

export const decryptStringClipboard = (value)=>{
    try {
        return decryptString(value);
    } catch (_error) {
        return value;
    }
};

export function decryptTenantId(encrypted) {
    try {
        if (!encrypted) {
            throw new Error('Encrypted tenant ID is required');
        }
        const key = crypto.createHash('sha256').update(TENANT_SALT).digest().slice(0, 16); // Ensure 16-byte key
        const decipher = crypto.createDecipheriv('aes-128-ecb', key, Buffer.alloc(0)); // Use an empty IV
        const decrypted = Buffer.concat([
            decipher.update(Buffer.from(encrypted, 'base64')),
            decipher.final(),
        ]);
        return decrypted.toString('utf8'); 
    } catch (_error) {
        return null;
    }
}


export function encryptedTenantId() {
    return encryptTenantId(ROOT_TENANT_ID);
}

export function getRootTenantId() {
    return ROOT_TENANT_ID;
}

export default {
    encryptTenantId,
    decryptTenantId,
};

