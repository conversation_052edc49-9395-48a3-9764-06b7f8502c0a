// GRAPH.js

import { getHeaders } from '@/utils/api';

const backend_base_url = process.env.NEXT_PUBLIC_API_URL;

export async function fetchGraph(dbType) {
  const url = `${backend_base_url}/graph/labels?graph=${dbType}`;

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data['lables'];
  } catch (error) {
    
    throw error;
  }
}

// src/utils/graphAPI.js

export async function fetchGraphDiagram(node_id, dbType, depth, nodeTypes) {
  const url = `${backend_base_url}/graph/get_old_graph_nodes_and_edges`;

  // Create the body data for the POST request
  const bodyData = {
    graph: dbType,  // 'code' or 'project' based on your dbType parameter
    depth: depth || 2,  // Default to 2 if depth isn't provided
    node_types: nodeTypes || [],  // Default to an empty array if nodeTypes isn't provided
  };

  // Add node_id to the body if it's provided
  if (node_id) {
    bodyData.node_id = node_id;
  }

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify(bodyData),  // Send the data as JSON
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
}


export async function fetchdemoGraphDiagram(node_id, dbType, depth, nodeTypes) {
  const url = `${backend_base_url}/graph/get_new_graph_nodes_and_edges`;

  // Create the body data for the POST request
  const bodyData = {
    graph: dbType,  // 'code' or 'project' based on your dbType parameter
    depth: depth || 2,  // Default to 2 if depth isn't provided
    node_types: nodeTypes || [],  // Default to an empty array if nodeTypes isn't provided
  };

  // Add node_id to the body if it's provided
  if (node_id) {
    bodyData.node_id = node_id;
  }

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify(bodyData),  // Send the data as JSON
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
}

export async function fetchNodeById(nodeId,  graph ) {

  const url = `${backend_base_url}/graph/${nodeId}?graph=${graph}`;
  
  try {
      const response = await fetch(url, {
          method: 'GET',
          headers: await getHeaders()
      });

      // Check if the response is successful
      if (!response.ok) {
          throw new Error(`Error fetching node: ${response.statusText}`);
      }

      const data = await response.json();
      return data; // Return the fetched node details

  } catch (error) {
      
      throw error; // Rethrow the error for further handling
  }
};


export const fetchsearchDiagram = async (projectId, graph, searchText = '') => {
  const url = `${backend_base_url}/graph/newgraph_search_nodes/${projectId}?graph=${graph}&search_text=${encodeURIComponent(searchText)}`;

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (!response.ok) {
      throw new Error(`Error fetching node: ${response.statusText}`);
    }

    const data = await response.json();
    return data; // Return the fetched node details

  } catch (error) {
    
    throw error; // Rethrow the error for further handling
  }
};

export const fetchsamplesearchDiagram = async (projectId, graph, searchText = '') => {
  const url = `${backend_base_url}/graph/oldgraph_search_nodes/${projectId}?graph=${graph}&search_text=${encodeURIComponent(searchText)}`;

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: await getHeaders()
    });

    if (!response.ok) {
      throw new Error(`Error fetching node: ${response.statusText}`);
    }

    const data = await response.json();
    return data; // Return the fetched node details

  } catch (error) {
    
    throw error; // Rethrow the error for further handling
  }
};