//src/utils/fileAPI.js
import { getHeadersRaw } from "./api";
const backend_base_url = process.env.NEXT_PUBLIC_API_URL;

export const extractText = async (formData) => {
    let headersRaw = getHeadersRaw()
    delete headersRaw["Content-Type"]
    const response = await fetch(`${backend_base_url}/file/extract_text_discussion`, {
      method: 'POST',
      headers: headersRaw,
      body: formData,
    });
    if (!response.ok) {
      throw new Error('Failed to extract text from file');
    }
    return response.json();
  };

  export const extractTextV1 = async (formData) => {
    let headersRaw = getHeadersRaw()
    delete headersRaw["Content-Type"]
    const response = await fetch(`${backend_base_url}/file/v1/extract_text/`, {
      method: 'POST',
      headers: headersRaw,
      body: formData,
    });
    if (!response.ok) {
      throw new Error('Failed to extract text from file');
    }
    return response.json();
  };

  export const uploadFile = async (file, discussionId) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('discussion_id', discussionId);
    let headersRaw = getHeadersRaw()
    delete headersRaw["Content-Type"]
  
    const response = await fetch(`${backend_base_url}/file/upload`, {
      method: 'POST',
      headers: headersRaw,
      body: formData,
    });
  
    if (!response.ok) {
      throw new Error('Failed to upload file');
    }
  
    return response.json();
  };

/**
 * Upload a file attachment for chat
 * @param {File} file - File to upload
 * @param {string|number} projectId - The project ID
 * @returns {Promise<Object>} - Attachment data response
 */
export const uploadAttachment = async (file, projectId) => {
  const formData = new FormData();
  const projectIdInt = parseInt(projectId);
  
  // Check if it's a file path (for Figma JSON files)
  if (typeof file === 'object' && file.path && !file.stream) {
    // Handle file path upload
    formData.append('file_path', file.path);
    formData.append('filename', file.filename);
  } else {
    // Handle regular File object upload
    formData.append('file', file);
  }
  
  formData.append('project_id', projectIdInt);
  
  let headersRaw = getHeadersRaw();
  delete headersRaw["Content-Type"];
  headersRaw["accept"] = "application/json";
  
  const response = await fetch(`${backend_base_url}/file/upload-attachment`, {
    method: 'POST',
    headers: headersRaw,
    body: formData,
  });
  
  if (!response.ok) {
    let errorText;
    try {
      const errorData = await response.json();
      errorText = JSON.stringify(errorData);
    } catch (e) {
      errorText = await response.text();
    }
    throw new Error(`Failed to upload attachment: ${response.status} ${response.statusText}`);
  }
  
  return response.json();
};

  /**
   * Upload multiple file attachments for chat
   * @param {File[]} files - Array of files to upload
   * @param {string|number} projectId - The project ID
   * @returns {Promise<Object[]>} - Array of attachment data responses
   */
  export const uploadMultipleAttachments = async (files, projectId) => {
    // Upload files sequentially to avoid overwhelming the server
    const results = [];
    
    for (const file of files) {
      try {
        projectId = parseInt(projectId);
        const result = await uploadAttachment(file, projectId);
        results.push(result);
      } catch (error) {
        
        // Continue with other files even if one fails
        results.push({ 
        success: false, 
        error: error.message, 
        filename: file.name || file.filename  // Handle both file.name and file.filename
      });

      }
    }
    
    return results;
  };