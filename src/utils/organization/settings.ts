import {getHeaders} from "@/utils/api";
import { decryptStringClipboard } from "@/utils/hash";

const backend_base_url = process.env.NEXT_PUBLIC_API_URL;

export interface SecureSettingValue {
  name: string;
  value: string;
  secure: boolean;
}

export interface IntegrationSettings {
  figma: SecureSettingValue[];
}

export interface SettingModel {
  integrations: IntegrationSettings;
}

// Helper function to handle secure values
export function getSettingValue(setting: SecureSettingValue): string {
  if (setting.secure) {
    return decryptStringClipboard(setting.value);
  }
  return setting.value;
}

export async function getSettings(): Promise<SettingModel> {
  const response = await fetch(`${backend_base_url}/org/settings/`, {
    method: 'GET',
    headers: await getHeaders(),
  });

  if (!response.ok) {
    throw new Error('Failed to fetch settings');
  }

  return response.json();
}

export async function updateSettings(settings: Partial<SettingModel>): Promise<void> {
  const response = await fetch(`${backend_base_url}/org/settings/`, {
    method: 'POST',
    headers: await getHeaders(),
    body: JSON.stringify(settings),
  });

  if (!response.ok) {
    throw new Error('Failed to update settings');
  }
}
