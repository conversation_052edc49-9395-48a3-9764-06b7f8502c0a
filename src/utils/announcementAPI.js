// utils/announcementAPI.js
import { getHeaders } from "./api";

const base_url = process.env.NEXT_PUBLIC_API_URL;
const SHOW_NAME = 'announcements';

export const createDraft = async (draftData) => {
    try {
        const response = await fetch(`${base_url}/${SHOW_NAME}/drafts`, {
            method: "POST",
            headers: await getHeaders(),
            body: JSON.stringify(draftData),
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || "Failed to create draft");
        }

        return await response.json();
    } catch (error) {
        
        throw error;
    }
};

export const getDrafts = async () => {
    try {
        const response = await fetch(`${base_url}/${SHOW_NAME}/drafts`, {
            method: "GET",
            headers: await getHeaders(),
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || "Failed to fetch drafts");
        }

        return await response.json();
    } catch (error) {
        
        throw error;
    }
};

export const deleteDraft = async (draftId) => {
    try {
        const response = await fetch(`${base_url}/${SHOW_NAME}/drafts/${draftId}`, {
            method: "DELETE",
            headers: await getHeaders(),
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || "Failed to delete draft");
        }

        return await response.json();
    } catch (error) {
        
        throw error;
    }
};

export const publishAnnouncement = async (announcementData) => {
    try {
        const response = await fetch(`${base_url}/${SHOW_NAME}/publish`, {
            method: "POST",
            headers: await getHeaders(),
            body: JSON.stringify(announcementData),
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || "Failed to publish announcement");
        }

        return await response.json();
    } catch (error) {
        
        throw error;
    }
};

export const getActiveAnnouncements = async () => {
    try {
        const headers = await getHeaders();
        const response = await fetch(`${base_url}/${SHOW_NAME}/active`, {
            method: "GET",
            headers: headers,
        });

        if (!response.ok) {
            return []; // Return empty array instead of throwing
        }

        return await response.json();
    } catch (error) {
        
        return []; // Return empty array on error
    }
};

export const deleteAnnouncement = async (announcementId) => {
    try {
        const response = await fetch(`${base_url}/${SHOW_NAME}/${announcementId}`, {
            method: "DELETE",
            headers: await getHeaders(),
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || "Failed to delete announcement");
        }

        return await response.json();
    } catch (error) {
        
        throw error;
    }
};

export const getAllAnnouncements = async () => {
    try {
        const response = await fetch(`${base_url}/${SHOW_NAME}/all`, {
            method: "GET",
            headers: await getHeaders(),
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || "Failed to fetch announcements");
        }

        return await response.json();
    } catch (error) {
        
        throw error;
    }
};

export const acknowledgeAnnouncement = async (announcementId, acknowledgmentData) => {
    try {
        const response = await fetch(`${base_url}/${SHOW_NAME}/${announcementId}/acknowledge`, {
            method: "POST",
            headers: await getHeaders(),
            body: JSON.stringify(acknowledgmentData),
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.detail || "Failed to acknowledge announcement");
        }

        return await response.json();
    } catch (error) {
        
        throw error;
    }
};