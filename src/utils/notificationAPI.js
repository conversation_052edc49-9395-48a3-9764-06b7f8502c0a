// utils/notificationAPI.js
import { getHeaders } from '@/utils/api';
const backend_base_url = process.env.NEXT_PUBLIC_API_URL;

export async function registerDevice(token, platform = 'web', appVersion = null, deviceInfo = null) {
  const url = `${backend_base_url}/notification/register`;

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify({
        token,
        platform,
        app_version: appVersion,
        device_info: deviceInfo || {
          userAgent: navigator.userAgent,
          platform: navigator.platform
        }
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
}

export async function sendTestNotification(receiverId, type = 'discussion', message = 'Test notification') {
  const url = `${backend_base_url}/notification/send`;

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify({
        receiver_id: receiverId,
        notification_type: type,
        message
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
}