'use client'

import showdown from "showdown";

const classMap = {
  h1: "typography-heading-4 font-weight-bold mb-2 whitespace-pre-wrap break-words",
  h2: "typography-body-lg font-weight-semibold mb-2 whitespace-pre-wrap break-words",
  h3: "typography-body font-weight-medium mb-2 whitespace-pre-wrap break-words",
  h4: "text-medium font-weight-medium mb-2 whitespace-pre-wrap break-words",
  h5: "typography-body-sm font-weight-medium mb-2  whitespace-pre-wrap break-words",
  h6: "typography-caption font-weight-medium mb-2  whitespace-pre-wrap break-words",
  p: "typography-body-sm font-weight-medium mb-1 whitespace-pre-wrap break-words",
  ul: "list-disc ml-4 typography-body-sm font-weight-medium mb-1 whitespace-pre-wrap break-words",
  ol: "list-decimal ml-4 typography-body-sm font-weight-medium mb-1 whitespace-pre-wrap break-words",
  li: "whitespace-pre-wrap break-words",
  strong: "font-weight-bold mt-2 text-gray-800 whitespace-pre-wrap break-words",
  a: "text-orange-600 hover:text-orange-800 underline  whitespace-pre-wrap break-words",
  blockquote: "border-l-4 border-gray-300 pl-4 py-1 mb-2 italic text-gray-600  whitespace-pre-wrap break-words",
  code: "markdown-code  bg-gray-100 rounded px-1 py-0.5 typography-body-sm text-gray-800 whitespace-pre-wrap break-words",
  pre: "markdown-pre bg-gray-100 rounded p-2 mb-2 whitespace-pre-wrap break-words",
  table: "min-w-full border-collapse mb-2  whitespace-pre-wrap break-words",
  th: "bg-semantic-gray-200 border border-semantic-gray-300 px-2 py-1 text-left text-semantic-gray-700  whitespace-pre-wrap break-words",
  td: "border border-semantic-gray-300 px-2 py-1 text-semantic-gray-600  whitespace-pre-wrap break-words",
  img: "max-w-full h-auto rounded shadow-lg mb-2  whitespace-pre-wrap break-words",
  hr: "border-t border-semantic-gray-300 my-2  whitespace-pre-wrap break-words",
  json: " typography-body-sm text-primary-foreground m-1 border border-semantic-gray-300 p-2 rounded-md mb-2 bg-semantic-gray-800 whitespace-pre-wrap break-words",
};
const bindings = Object.keys(classMap).map((key) => ({
  type: 'output',
  regex: new RegExp(`<${key}(.*)>`, 'g'),
  replace: `<${key} class="${classMap[key]}" $1>`,
}));

const conv = new showdown.Converter({
  extensions: [...bindings],
  omitExtraWLInCodeBlocks: true,
  parseImgDimensions: true,
  simplifiedAutoLink: true,
  literalMidWordUnderscores: true,
  strikethrough: true,
  tables: true,
  ghCodeBlocks: true,
  tasklists: true,
  smoothLivePreview: true,
  ghCompatibleHeaderId: true,
  encodeEmails: true,
  ellipsis: true,
  emoji: true
});

export const renderHTML = (text) => {
  try {
    if(!text || typeof text !== "string") return '';
    
    // Sanitize input to handle Unicode characters safely
    let sanitizedText = text;
    
    // Check for potentially problematic Unicode sequences
    try {
      // Ensure the text is properly encoded
      sanitizedText = decodeURIComponent(encodeURIComponent(text));
    } catch (encodeError) {
      // If encoding/decoding fails, use the original text
      console.warn('Unicode encoding/decoding failed:', encodeError);
      sanitizedText = text;
    }
    
    // Check if the entire text is wrapped in a single code block
    const trimmedText = sanitizedText.trim();
    
    // More flexible regex patterns to handle various code block formats
    const patterns = [
      /^```(\w+)?\s*\n([\s\S]*?)\n```$/,  // Standard: ```lang\ncontent\n```
      /^```(\w+)?\s*\n([\s\S]*?)```$/,    // Missing final newline: ```lang\ncontent```
      /^```(\w+)?\n([\s\S]*?)\n```$/,     // No space after lang: ```lang\ncontent\n```
      /^```(\w+)?([\s\S]*?)```$/          // Minimal: ```langcontent``` or ```\ncontent```
    ];
    
    for (const pattern of patterns) {
      try {
        const match = trimmedText.match(pattern);
        if (match) {
          // Extract the inner content (index 2 for most patterns, but handle edge cases)
          let innerContent = match[2];
          
          // If the content starts with a newline, remove it
          if (innerContent && innerContent.startsWith('\n')) {
            innerContent = innerContent.substring(1);
          }
          
          // If the content ends with a newline, keep it for proper markdown parsing
          if (innerContent) {
            // Process the inner content as markdown without the wrapper
            const html = conv.makeHtml(innerContent);
            return html
              .replace(/>\s+</g, '><')  // Remove whitespace between tags
              .replace(/\n\s*\n/g, '\n')  // Remove multiple empty lines
              .replace(/^\s+|\s+$/g, '')  // Trim start and end
              .replace(/<p><br><\/p>/g, '')  // Remove empty paragraphs with just line breaks
              .replace(/<p>\s*<\/p>/g, '');  // Remove empty paragraphs
          }
        }
      } catch (patternError) {
        console.warn('Pattern matching error:', patternError);
        continue;
      }
    }
    
    // Otherwise, process normally with error handling
    const html = conv.makeHtml(sanitizedText);
    
    // Clean up extra whitespace
    return html
      .replace(/>\s+</g, '><')  // Remove whitespace between tags
      .replace(/\n\s*\n/g, '\n')  // Remove multiple empty lines
      .replace(/^\s+|\s+$/g, '')  // Trim start and end
      .replace(/<p><br><\/p>/g, '')  // Remove empty paragraphs with just line breaks
      .replace(/<p>\s*<\/p>/g, '');  // Remove empty paragraphs
      
  } catch (error) {
    console.error('Error in renderHTML:', error, 'Input text:', text);
    
    // Fallback to simple text processing if markdown conversion fails
    try {
      if (typeof text === 'string') {
        return text
          .replace(/&/g, '&amp;')
          .replace(/</g, '&lt;')
          .replace(/>/g, '&gt;')
          .replace(/"/g, '&quot;')
          .replace(/'/g, '&#x27;')
          .replace(/\n/g, '<br>');
      }
      return '';
    } catch (fallbackError) {
      console.error('Fallback rendering also failed:', fallbackError);
      return 'Error rendering content';
    }
  }
};


export function findValueOfProperty(obj, propertyName) {
  let reg = new RegExp(propertyName, "i"); // "i" to make it case insensitive
  return Object.keys(obj).reduce((result, key) => {
    if (reg.test(key)) result.push(obj[key]);
    return result;
  }, []);
}

export const getTitleForRelationship = (relationship) => {
  if (relationship == "hasChild"){
    return "Child Nodes";
  }
  if (relationship == "interfacesWith"){
    return "Interfaces";
  }
  return relationship;
}

export function groupRelationshipsByType(relationships) {

  return relationships.reduce((acc, relationship) => {
    if (!acc[relationship.type]) {
      acc[relationship.type] = [];
    }
    acc[relationship.type].push(relationship);
    return acc;
  }, {});
}

export const updateSearchParams = (router, pathname, searchParams, paramKey, paramValue) => {
  const newParams = new URLSearchParams(searchParams);
  newParams.set(paramKey, paramValue);
  router.replace(`${pathname}?${newParams.toString()}`);
};

//will avoid going back to code generation or discussion panel after pressing back button by instructing browser to go 2 pages back
export const updateSessionStorageBackHistory = (steps = 2) => {
  if(sessionStorage.getItem("querySet")){
    let backSteps = Number(sessionStorage.getItem("querySet"));
    backSteps -= steps;
    sessionStorage.setItem("querySet", backSteps);
  }
  else{
    sessionStorage.setItem("querySet", "-3");
  }
}
