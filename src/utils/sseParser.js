/**
 * Utility functions for parsing Server-Sent Events (SSE) responses
 */

/**
 * Parse SSE response data and extract JSON
 * @param {string} sseData - Raw SSE response data
 * @returns {Object|null} - Parsed JSON object or null if parsing fails
 */
export const parseSSEData = (sseData) => {
  if (!sseData) return null;

  const lines = sseData.split('\n');
  
  // Look for the last valid data line (working backwards)
  for (let i = lines.length - 1; i >= 0; i--) {
    const line = lines[i].trim();
    if (line.startsWith('data: ')) {
      try {
        const jsonData = line.substring(6); // Remove 'data: ' prefix
        return JSON.parse(jsonData);
      } catch (error) {
        console.warn('Failed to parse SSE line:', line, error);
        continue;
      }
    }
  }
  
  return null;
};

/**
 * Check if a response is in SSE format
 * @param {Response} response - Fetch response object
 * @returns {boolean} - True if response is SSE format
 */
export const isSSEResponse = (response) => {
  const contentType = response.headers.get('content-type');
  return contentType && contentType.includes('text/event-stream');
};

/**
 * Read and parse SSE stream
 * @param {Response} response - Fetch response object
 * @returns {Promise<Object>} - Promise that resolves to parsed JSON data
 */
export const readSSEStream = async (response) => {
  const reader = response.body.getReader();
  const decoder = new TextDecoder();
  
  let result = '';
  while (true) {
    const { done, value } = await reader.read();
    if (done) break;
    result += decoder.decode(value, { stream: true });
  }
  
  const parsedData = parseSSEData(result);
  if (!parsedData) {
    throw new Error("No valid JSON data found in SSE response");
  }
  
  return parsedData;
};

/**
 * Debug utility to log SSE response data
 * @param {string} sseData - Raw SSE response data
 */
export const debugSSE = (sseData) => {
  console.group('🔍 SSE Debug');
  
  
  const lines = sseData.split('\n');
  
  
  const parsed = parseSSEData(sseData);
  
  
  console.groupEnd();
  return parsed;
};

export default {
  parseSSEData,
  isSSEResponse,
  readSSEStream,
  debugSSE
}; 