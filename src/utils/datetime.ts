import { format, parseISO, isValid } from 'date-fns';
import { enGB } from 'date-fns/locale';


const getStoredTimezone = (): string => {
  const storedConfig = sessionStorage.getItem("userConfiguration");
  if (storedConfig) {
    const config = JSON.parse(storedConfig);
    return config.configuration?.timezone || 'UTC'; // Default to 'UTC' if timezone is not available
  }
  return 'UTC'; // Default to 'UTC' if no configuration is found
};

export const formatDate = (dateString: string | null): string => {
  try {
    if (!dateString) return '';
    const date = parseISO(dateString);
    if (!isValid(date)) return '';
    return format(date, 'd MMMM yyyy', { locale: enGB });
  } catch (error) {
    
    return '';
  }
};

export const formatDateTime = (dateString: string | null, useLocalTimezone: boolean = false): string => {
  try {
    if (!dateString) return '';
    const date = parseISO(dateString);
    if (!isValid(date)) return '';
    
    if (useLocalTimezone) {
      const localDate = new Date(date.toLocaleString('en-US', { timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone }));
      return format(localDate, 'd MMMM yyyy HH:mm:ss', { locale: enGB });
    }
    
    return format(date, 'd MMMM yyyy HH:mm:ss', { locale: enGB });
  } catch (error) {
    
    return '';
  }
};

export const formatUTCToLocal = (utcDateString: string | null, showTime: boolean = true): string => {
  try {
    if (!utcDateString) return '';
    const date = new Date(utcDateString);
    if (isNaN(date.getTime())) return '';

    const timezone = getStoredTimezone();
    
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      ...(showTime && { // Add time options only if showTime is true
        hour: 'numeric',
        minute: 'numeric',
      }),
      timeZone: timezone,
    };

    return new Intl.DateTimeFormat(undefined, options).format(date);
  } catch (error) {
    
    return '';
  }
};

export const getLocalTimeZone = (): string => {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
};

// Formatting dates in project list and chat list
export const formatListDate = (dateString: string | null): string => {
  try {
    if (!dateString) return "";

    const options: Intl.DateTimeFormatOptions = {
      day: "numeric",
      month: "long",
      year: "numeric",
    };

    const date = new Date(dateString);

    if (isNaN(date.getTime())) return "";

    return date.toLocaleDateString("en-GB", options);
  } catch (error) {
    
    return "";
  }
};