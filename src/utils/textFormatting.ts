export const capitalizeText = (text?: string, separator: string = '_'): string => {
    try {
      // Handle undefined, null, or empty string
      if (!text) {
        return '';
      }
  
      // Handle if text is not a string
      if (typeof text !== 'string') {
        return String(text);
      }
  
      return text
        .split(separator)
        .map(word => 
          word
            .trim()
            .toLowerCase()
            .replace(/^./, char => char.toUpperCase())
        )
        .join(' ');
    } catch (error) {
      
      return text || '';
    }
  };