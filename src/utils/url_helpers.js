

/**
 * Creates a preview URL by adding a port number to a host URL
 * @param {string} hostUrl - The base host URL
 * @param {number|string} portNumber - The port number to append
 * @returns {string} - The complete preview URL with port
 */
const getPreviewUrl = (hostUrl, portNumber) => {
  try {
    // Parse the host URL
    const url = new URL(hostUrl);
    
    // Remove any existing port
    url.port = '';
    
    // Get the base URL without any path or query parameters
    const baseUrl = `${url.protocol}//${url.hostname}`;
    
    // Return the base URL with the port appended
    return `${baseUrl}:${portNumber}`;
  } catch (error) {
    
    return null;
  }
};

export { getPreviewUrl };



/**
 * Changes the host of a URL while preserving or handling port numbers
 * @param {string} url - The original URL
 * @param {string} newHost - The new host to use
 * @returns {string} - The URL with the updated host
 */
const changeUrlHost = (url, newHost) => {
  try {
    // Parse the original URL
    const parsedUrl = new URL(url);
    
    // Store the original port
    const originalPort = parsedUrl.port;
    
    // Parse the new host to ensure it's valid
    // If newHost includes protocol, use it; otherwise use the original protocol
    let newUrl;
    try {
      // Check if newHost has a protocol
      if (!/^https?:\/\//i.test(newHost)) {
        // No protocol, so prepend the original protocol
        newHost = `${parsedUrl.protocol}//${newHost}`;
      }
      newUrl = new URL(newHost);
    } catch (error) {
      // If newHost is not a valid URL, assume it's just a hostname
      newUrl = new URL(`${parsedUrl.protocol}//${newHost}`);
    }
    
    // Preserve the path, query, and hash from the original URL
    newUrl.pathname = parsedUrl.pathname;
    newUrl.search = parsedUrl.search;
    newUrl.hash = parsedUrl.hash;
    
    // Handle port based on requirements
    if (originalPort) {
      // If original URL had a port, use it
      newUrl.port = originalPort;
    }
    // If no port in the original URL, leave it as is (no port)
    
    return newUrl.toString();
  } catch (error) {
    
    return null;
  }
};

export { changeUrlHost };
// Example usage:
// const hostUrl = "https://example.com";
// const portNumber = 8080;
// const previewUrl = getPreviewUrl(hostUrl, portNumber);
//  // Output: "https://example.com:8080"

/**
 * Extracts the host from a URL
 * @param {string} url - The URL to extract the host from
 * @param {boolean} includeProtocol - Whether to include the protocol in the result
 * @returns {string} - The host of the URL
 */
const getUrlHost = (url, includeProtocol = false) => {
  try {
    // Parse the URL
    const parsedUrl = new URL(url);
    
    // Return the host with or without protocol based on the parameter
    return includeProtocol 
      ? `${parsedUrl.protocol}//${parsedUrl.hostname}`
      : parsedUrl.hostname;
  } catch (error) {
    
    return null;
  }
};

/**
 * Extracts the port from a URL
 * @param {string} url - The URL to extract the port from
 * @param {string|number} defaultPort - The default port to return if none exists
 * @returns {string|number|null} - The port of the URL or the default port if none exists
 */
const getUrlPort = (url, defaultPort = null) => {
  try {
    // Parse the URL
    const parsedUrl = new URL(url);
    
    // Return the port if it exists, otherwise return the default port
    return parsedUrl.port ? parsedUrl.port : defaultPort;
  } catch (error) {
    
    return null;
  }
};

export { getUrlHost, getUrlPort };
