// api.js
import axios from 'axios';
import { getHeadersRaw } from './api';


const SHOW_NAME = 'architecture';

export const createDesignNode = async (architectureId, designProperties={}) => {
  try {
    let queryParams = '?architecture_id=' + architectureId;
    const response = await axios.post(
      `${process.env.NEXT_PUBLIC_API_URL}/${SHOW_NAME}/design/${queryParams}`,
      {
        design_properties: designProperties
      },
      {
        headers: getHeadersRaw(),
      }
    );

    if (response.status === 200) {
      return response.data;
    } else {
      throw new Error('Failed to create design node');
    }
  } catch (error) {
    
    if (error.response && error.response.data) {
      // If the server sent a detailed error message, throw that
      throw new Error(error.response.data.detail || 'Failed to create design node');
    }
    throw error;
  }
};

export const fetchArchitectureLeafNodes = async (projectId) => {
  try {
    const response = await axios.get(
      `${process.env.NEXT_PUBLIC_API_URL}/${SHOW_NAME}/architecture_leaf_nodes/${projectId}`,
      {
        headers: getHeadersRaw(),
      }
    );

    if (response.status === 200) {
      return response.data;
    } else {
      throw new Error('Failed to fetch low level design data');
    }
  } catch (error) {
    
    throw error;
  }
};

export const fetchArchitecturalRequirement = async (projectId) => {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_API_URL}/${SHOW_NAME}/architectural_requirement/${projectId}`,
        {
          headers: getHeadersRaw(),
        }
      );
  
      if (response.status === 200) {
        return response.data;
      } else {
        throw new Error('Failed to fetch architectural requirement data');
      }
    } catch (error) {
      
      throw error;
    }
  };

export const getInterfaceChildren = async (interfaceId) => {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_API_URL}/${SHOW_NAME}/interface_childs/${interfaceId}`,
        {
          headers: getHeadersRaw(),
        }
      );
  
      if (response.status === 200) {
        return response.data;
      } else{
        return [];
      }
      
    }
    catch (error) {
      
      return [];
    }
  };

 export const fetchLowLevelDesignDetails = async (architectureLeafId) => {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_API_URL}/architecture/design_nodes/${architectureLeafId}`,
        {
          headers: getHeadersRaw(),
        }
      );

      if (response.status === 200) {
        return response.data;
      } else{
        return [];
      }
      
    }
    catch (error) {
      
      return [];
    }


  };