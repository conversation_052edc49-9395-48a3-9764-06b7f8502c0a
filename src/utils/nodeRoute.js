import {getHeaders} from "./api";

const backend_base_url = process.env.NEXT_PUBLIC_API_URL;

/**
 * Fetches related nodes for a given node ID and relationship type
 * @param {string} nodeId - The ID of the node to get related nodes for
 * @param {string} nodeType - The type of the node (e.g., 'UserStory')
 * @param {string} relationshipType - The type of relationship to filter by (e.g., 'VERIFIES')
 * @returns {Promise<Array>} - Array of related nodes
 */
export async function getRelatedNodes(nodeId, nodeType, relationshipType) {
  if (!nodeId || !nodeType || !relationshipType) {
    throw new Error("Node ID, node type, and relationship type are required");
  }

  const encodedNodeId = encodeURIComponent(nodeId);
  const encodedNodeType = encodeURIComponent(nodeType);
  const encodedRelationshipType = encodeURIComponent(relationshipType);
  
  const url = `${backend_base_url}/node/get_related_nodes/${encodedNodeId}/${encodedNodeType}/${encodedRelationshipType}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: await getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`HTTP error status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    
    throw error;
  }
}
