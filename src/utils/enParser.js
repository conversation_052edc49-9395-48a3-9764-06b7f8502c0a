import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>boardList,
  MessageSquare,
  CheckSquare,
  AlertTriangle,
  Bell,
  User,
  BarChart2,
  Settings,
  FileText,
  GitBranch,
  Code,
  FileCode2,
  Database,
  Bug,
  Info
} from 'lucide-react';

const getIcon = (key) => {
  if (key.includes('project') || key.includes('projects')) return FolderOpen;
  if (key.includes('work item') || key.includes('work items')) return ClipboardList;
  if (key.includes('requirement') || key.includes('requirements')) return FileText
  if (key.includes('Discussion') || key.includes('Chat')) return MessageSquare;
  if (key.includes('Task')) return CheckSquare;
  if (key.includes('Error') || key.includes('NotFound')) return AlertTriangle;
  if (key.includes('Notification')) return Bell;
  if (key.includes('User')) return User;
  if (key.includes('Graph')) return BarChart2;
  if (key.includes('Architecture')) return GitBranch;
  if (key.includes('Settings')) return Settings;
  if (key.includes('Design') || key.includes('Diagram') || key.includes('design')) return FileText;
  if (key.includes('Test')) return Bug;
  if (key.includes('Api')) return Code;
  if (key.includes('Interface')) return FileCode2;
  if (key.includes('Data')) return Database;
  return Info;  // default icon
};

const cleanContentFromPTags = (content) => {
  return content.replace(/<p>/g, '').replace(/<\/p>/g, '');
};

const EnParser = ({ content }) => {
  let data = cleanContentFromPTags(content);
  const Icon = getIcon(data);
  return (
    <div className="flex text-font">
      {Icon && <Icon className="mr-2" width={16} height={16} />}
      <span>{data}</span>
    </div>
  );
};


export default EnParser;