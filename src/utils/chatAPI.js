// src/utils/chatApi.ts

import { getHeaders } from './api';

const backend_base_url = process.env.NEXT_PUBLIC_API_URL;

export async function renameChat(discussionId, newTitle) {
    const base_url = backend_base_url;
  
    try {
      const response = await fetch(`${base_url}/conversation/rename?discussion_id=${discussionId}&new_title=${encodeURIComponent(newTitle)}`, {
        method: 'POST',
        headers: await getHeaders(),
      });
  
      if (!response.ok) {
        throw new Error(`HTTP error status: ${response.status}`);
      }
  
      const data = await response.json();
      return data;
    } catch (error) {
      
      throw error;
    }
}

export async function deleteChat(discussionId) {
    const base_url = backend_base_url;
  
    try {
      const response = await fetch(`${base_url}/conversation/chat?discussion_id=${discussionId}`, {
        method: 'DELETE',
        headers: await getHeaders(),
      });
  
      if (!response.ok) {
        throw new Error(`HTTP error status: ${response.status}`);
      }
  
      const data = await response.json();
      return data;
    } catch (error) {
      
      throw error;
    }
}