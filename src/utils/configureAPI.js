"use client";

import { getHeaders } from "./api";

let base_url = process.env.NEXT_PUBLIC_API_URL;

const SHOW_NAME = 'v2/tasks';

export const getLatestActiveTask = async (projectId, nodeId, nodeType) => {
  const response = await fetch(`${base_url}/${SHOW_NAME}/get_latest_active_task/${projectId}?node_id=${nodeId}&node_type=${nodeType}`, {
    method: "GET",
    headers: await getHeaders(),
  });

  if (!response.ok) {
    throw new Error("Failed to fetch latest active task");
  }

  return await response.json();
};

export const getPastTasks = async (projectId, page = 1, limit = 10) => {
  const response = await fetch(`${base_url}/${SHOW_NAME}/past_tasks/${projectId}?page=${page}&limit=${limit}`, {
    method: "GET",
    headers: await getHeaders(),
  });

  if (!response.ok) {
    throw new Error("Failed to fetch past tasks");
  }

  return await response.json();
};

export const getTotalPastTasks = async (projectId) => {
  const response = await fetch(`${base_url}/${SHOW_NAME}/total_past_tasks/${projectId}`, {
    method: "GET",
    headers: await getHeaders(),
  });

  if (!response.ok) {
    throw new Error("Failed to fetch total past tasks");
  }

  return await response.json();
};

export const getTaskUpdatesNonStreaming = async (taskId) => {
  const response = await fetch(`${base_url}/${SHOW_NAME}/configure/task_updates/non_streaming/${taskId}`, {
    method: "GET",
    headers: await getHeaders(),
  });

  if (!response.ok) {
    throw new Error("Failed to fetch task updates");
  }

  return await response.json();
};


