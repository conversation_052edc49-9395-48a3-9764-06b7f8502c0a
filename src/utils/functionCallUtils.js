// utils/functionCallUtils.js
export const processFunctionCalls = (functionCalls) => {
  if (!functionCalls || functionCalls.length === 0) return [];

  // Sort all calls by timestamp (newest first)
  const sortedCalls = [...functionCalls].sort(
    (a, b) => new Date(b.timestamp) - new Date(a.timestamp)
  );

  // Keep track of unique calls
  const uniqueCalls = [];
  const seen = new Set();

  // Process all calls
  for (const call of sortedCalls) {
    const callKey = JSON.stringify({
      function_name: call.function_name,
      reason: call.reason,
    });

    // Add call if it's not a duplicate
    if (!seen.has(callKey)) {
      seen.add(callKey);
      uniqueCalls.push(call);
    }
  }

  return uniqueCalls;
};