// utils/paymentAPI.js

import { getHeaders } from '@/utils/api';
const backend_base_url = process.env.NEXT_PUBLIC_API_URL;
const SHOW_NAME = 'payment';

// Get available subscription products
export const getProducts = async () => {
  try {
    const response = await fetch(`${backend_base_url}/products/list`, {
      method: 'GET',
      headers: await getHeaders(),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch products: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

// Create a checkout session for subscription
export const createCheckoutSession = async (priceId) => {
  try {
    let body = {
      price_id: priceId,
      success_url: `${window.location.origin}/payment/success`,
      cancel_url: `${window.location.origin}/payment/cancel`,
    }
    const response = await fetch(`${backend_base_url}/${SHOW_NAME}/create-checkout-session`, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify(body),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to create checkout session: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

// Check if user is already subscribed
export const checkUserSubscription = async () => {
  try {
    const response = await fetch(`${backend_base_url}/${SHOW_NAME}/check-subscription`, {
      method: 'GET',
      headers: await getHeaders(),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to check subscription: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

// Get subscription plan name
export const getSubscriptionPlanName = async (productId) => {
  try {
    const products = await getProducts();
    const product = products.find(p => p.id === productId);
    return product ? product.name : 'Unknown Plan';
  } catch (error) {
    
    return 'Unknown Plan';
  }
};

// Handle success callback
export const handlePaymentSuccess = async (internalSecretToken) => {
  try {
    const response = await fetch(`${backend_base_url}/${SHOW_NAME}/success`, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify({ internal_secret_token: internalSecretToken }),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to confirm payment success: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

// Handle cancel callback
export const handlePaymentCancel = async (internalSecretToken) => {
  try {
    const response = await fetch(`${backend_base_url}/${SHOW_NAME}/cancel`, {
      method: 'POST',
      headers: await getHeaders(),
      body: JSON.stringify({ internal_secret_token: internalSecretToken }),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to confirm payment cancellation: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    
    throw error;
  }
};

// Get latest subscription details
export const getLatestSubscription = async (user_id) => {
  try {
    const response = await fetch(`${backend_base_url}/payment/latest-subscription/${user_id}`, {
      method: 'GET',
      headers: await getHeaders(),
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch latest subscription: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    
    return null;
  }
};

// Get product details by price ID
export const getProductDetailsByPriceId = async (priceId) => {
  try {
    const products = await getProducts();
    const product = products.find(p => p.price_id === priceId);
    if (!product) {
      throw new Error(`No product found for price ID: ${priceId}`);
    }
    return {
      productName: product.product_name,
      credits: product.credits,
      description: product.product_description,
      currency: product.currency
    };
  } catch (error) {
    
    throw error;
  }
};

