
export type NodeType = 
  | 'project'
  | 'workItem'
  | 'workitemroot'
  | 'requirementroot'
  | 'requirement'
  | 'epic'
  | 'userStory'
  | 'architecture'
  | 'architecturalRequirement'
  | 'functional'
  | 'functionalRequirement'
  | 'nonFunctional'
  | 'nonFunctionalRequirement'
  | 'systemContext'
  | 'container'
  | 'component'
  | 'interface'
  | 'design'
  | 'documentation'
  | 'prd'
  | 'sad';

export type ContentType = 
  | 'text-diff' 
  | 'mermaid' 
  | 'code-diff' 
  | 'project' 
  | 'workitemroot' 
  | 'requirementroot' 
  | 'workitem';

export interface Metadata {
  status?: string;
  assignee?: string;
  priority?: string;
  [key: string]: any;
}

export interface ContentVersion {
  type: ContentType;
  title: string;
  description?: string;
  properties?: {
    scope?: string[];
    objective?: string[];
    [key: string]: any;
  };
  metadata?: Metadata;
  mermaid?: string;
  code?: string;
  oldText?: string;
  newText?: string;
}

export interface NodeContent {
  old: ContentVersion;
  updated: ContentVersion;
}

export interface TreeNode {
  id: string;
  name: string;
  type: NodeType;
  description?: string;
  count?: number;
  status?: string;
  children?: TreeNode[];
  content?: NodeContent;
  properties?: { [key: string]: any };
}

export interface TreeData extends TreeNode {
  type: 'project';
}

export interface TreeNodeProps {
  node: TreeNode;
  level: number;
  onSelect: (nodeId: string) => void;
  selectedNodeId: string | null;
  fetchNodeVersions: (nodeId: any, nodeType: any) => void;
}

export interface FileTreeProps {
  data: TreeData[];
  onSelect: (nodeId: string) => void;
  selectedNodeId: string | null;
  fetchNodeVersions: (nodeId: any, nodeType: any) => void;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  refetchData?: () => void;
  projectName: string;
}

export interface DiffContentProps {
  type: 'old' | 'updated';
  content: ContentVersion;
  metaData: Metadata;
}

export interface ReconfigModalProps {
  onClose: () => void;
  onApprove: () => void;
}