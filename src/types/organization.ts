// types/organization.ts
export type OrganizationStep = 'details' | 'adminSetup' | 'planSelection' | 'featureConfiguration' | 'review';
export type BillingCycle = 'monthly' | 'yearly';

export interface Feature {
  text: string;
  available: boolean;
  icon: string;
}

export interface Plan {
  name: string;
  monthlyPrice: string;
  yearlyPrice: string;
  monthlyText: string;
  yearlyText: string;
  features: Feature[];
}

// types/organization.ts
export interface FeatureState {
  userManagement: {
    maxUsers: boolean;
    roleCustomization: boolean;
  };
  integrationHub: {
    apiAccess: boolean;
    github: boolean;
    jira: boolean;
    apiAccessRight: boolean;
    githubRight: boolean;
    jiraRight: boolean;
  };
  analyticsBoard: {
    customReports: boolean;
    exportCapabilities: boolean;
  };
}

export interface OrganizationState {
  featureConfiguration: {
    enableAll: boolean;
    features: FeatureState;
  };
}