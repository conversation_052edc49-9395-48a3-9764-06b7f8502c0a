"use client";
import React, { useState } from "react";
import { MoreVertical } from "lucide-react";
import TableComponent from "@/components/SimpleTable/DropDownTable";

const IssuesTab = () => {
  const [issues] = useState([
    { id: "1", title: "Issue 1", date: "07/09/2024" },
    { id: "2", title: "Issue 2", date: "07/09/2024" },
    { id: "3", title: "Issue 3", date: "07/09/2024" },
    { id: "4", title: "Issue 4", date: "07/09/2024" },
    { id: "5", title: "Issue 5", date: "07/09/2024" },
  ]);

  const headers = [
    { key: "title", label: "Issues" },
    { key: "date", label: "Date" },
    { key: "action", label: "", icon: <MoreVertical size={16} /> },
  ];

  const handleRowClick = (id) => {
    
  };

  const handleActionClick = (id) => {
    
  };

  return (
    <div className="pb-16">
      <h2 className="typography-heading-4 font-weight-semibold mb-4">Issues</h2>
      <p className="typography-body-sm text-gray-600 mb-4">
        This test case aims to validate the accuracy of the temperature
        prediction algorithm by comparing the predicted temperatures against
        actual recorded temperatures for a given set of locations over a
        specific time period.
      </p>
      <TableComponent
        title=""
        data={issues}
        headers={headers}
        onRowClick={handleRowClick}
        onActionClick={handleActionClick}
        sortableColumns={{ title: true, date: true }}
        itemsPerPage={5}
      />
    </div>
  );
};

export default IssuesTab;
