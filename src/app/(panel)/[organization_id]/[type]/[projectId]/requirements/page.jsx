"use client"
import React, { useContext, useState, useEffect } from "react";
import WorkItemTabContent from "@/components/BrowsePanel/WorkItemTabContent";
import { TabContext } from "@/components/Context/TabContext";
import "@/styles/tabs/requirements.css"
import { useUser } from "@/components/Context/UserContext";

const Page = () => {
  const [isShowTab, setIsShowTab] = useContext(TabContext);
  const [logs, setLogs] = useState(null);
  const { is_admin, tenant_id, fetchUserData } = useUser();

  useEffect(() => {
    if (is_admin == null || tenant_id == null) {
      fetchUserData();
    }
  }, [is_admin, tenant_id]);

  return (
    <>
      <div className="requirementContainerWrapper relative">
        <WorkItemTabContent setIsShowTab={setIsShowTab} logs={logs} setLogs={setLogs} />
      </div>
    </>
  );
};

export default Page;
