"use client"
import { ItemDetails } from '@/components/BrowsePanel/Architecture/ItemDetails';
import { usePathname } from "next/navigation"

const Page = () => {
    const pathname = usePathname()
    const pathSegments = pathname.split('/');
    const archId = pathSegments.length > 5 ? pathSegments[5] : null;

    return (
        <div className='h-full overflow-y-auto custom-scrollbar'>
            <ItemDetails archId={archId} />
        </div>
    );
}

export default Page;