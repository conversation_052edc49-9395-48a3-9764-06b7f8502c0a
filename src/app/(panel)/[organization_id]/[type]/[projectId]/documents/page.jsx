'use client';
import { useState, useEffect, useContext } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { DocumentSidebar } from '@/components/Documents/DocumentSidebar';
import { DocumentHeader } from '@/components/Documents/DocumentHeader';
import { DocumentList } from '@/components/Documents/DocumentList';
import { DocumentGrid } from '@/components/Documents/DocumentGrid';
import { getDocumentVersions, downloadDocument } from '@/utils/documentationAPI';
import { useUser } from "@/components/Context/UserContext";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";

const EmptyState = ({ message }) => (
  <div className="flex flex-col items-center justify-center h-64 space-y-4">
    <div className="p-4 rounded-full bg-gray-100">
      <svg
        className="w-8 h-8 text-gray-400"
        fill="none"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
    </div>
    <p className="text-gray-500 typography-body-lg">{message}</p>
  </div>
);

export default function DocumentsPage({ params }) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const currentType = searchParams.get('type') || 'PRD';
  const [currentSearch, setCurrentSearch] = useState('');
  const currentView = searchParams.get('view') || 'list';

  const [documents, setDocuments] = useState([]);
  const [filteredDocuments, setFilteredDocuments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const { is_admin, tenant_id, fetchUserData } = useUser();
  const { showAlert } = useContext(AlertContext);

  useEffect(() => {
    if (is_admin == null || tenant_id == null) {
      fetchUserData();
    }
  }, [is_admin, tenant_id]);

  const updateQueryParams = (updates) => {
    const params = new URLSearchParams(searchParams);
    Object.entries(updates).forEach(([key, value]) => {
      if (value) {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });
    router.push(`?${params.toString()}`, { scroll: false });
  };

  const handleDownload = async (file) => {
    try {
      const blob = await downloadDocument(
        params.projectId,
        currentType,
        file.version,
        file.file_name,
        file.folder_path
      );

      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = file.file_name;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      
      // Using a more elegant toast notification would be better here
      alert('Failed to download file');
    }
  };

  const fetchDocuments = async () => {
    if (!currentType) return;

    setLoading(true);
    setError(null);
    try {
      const versions = await getDocumentVersions(params.projectId, currentType);
      const allDocuments = versions.flatMap(version =>
        version.files.map(file => ({
          ...file,
          version: version.version,
          folder_path: file.key.split('/')[file.key.split('/').length - 2]
        }))
      );

      setDocuments(allDocuments);
      setFilteredDocuments(allDocuments);
    } catch (error) {
      
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const filteredDocs = documents.filter(doc => doc.file_name.toLowerCase().includes(currentSearch.toLowerCase()))
    setFilteredDocuments(filteredDocs);
  }, [currentSearch])

  useEffect(() => {
    fetchDocuments();
  }, [currentType, params.projectId]);

  return (
    <div className="relative flex h-screen overflow-hidden bg-gray-50">
      {/* Sidebar */}
      <div className="w-64 h-full border-r border-gray-200 bg-white shadow-sm overflow-y-auto">
        <DocumentSidebar
          selectedType={currentType}
          onTypeSelect={(type) => updateQueryParams({ type })}
        />
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col h-full">
        {/* Fixed Header */}
        <div className="shrink-0 bg-white border-b border-gray-200">
          <DocumentHeader
            onSearch={(query) => setCurrentSearch(query)}
            currentSearch={currentSearch}
            viewMode={currentView}
            onViewModeChange={(mode) => updateQueryParams({ view: mode })}
            currentType={currentType}
            onUploadSuccess={fetchDocuments}
          />
        </div>

        {/* Content Area with Scrollbar */}
        <div className="flex-1 min-h-0"> {/* min-h-0 is crucial for nested flex containers */}
          <main className="h-full">
            <div className="h-full overflow-auto px-6 py-4">
              {error ? (
                <div className="rounded-lg bg-red-50 p-4 mt-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="typography-body-sm font-weight-medium text-red-800">Error loading documents</h3>
                      <div className="mt-2 typography-body-sm text-red-700">{error}</div>
                    </div>
                  </div>
                </div>
              ) : !currentType ? (
                <EmptyState message="Select a document type to view files" />
              ) : documents.length === 0 && currentSearch ? (
                <EmptyState message={`No documents found try with different document name `} />
              ) : documents.length === 0 ? (
                <EmptyState message="There are no documents available" />
              ) : (
                <div className="max-h-[66vh] overflow-y-auto custom-scrollbar">
                  {currentView === 'list' ? (
                    <DocumentList
                      documents={filteredDocuments}
                      onDownload={handleDownload}
                    />
                  ) : (
                    <DocumentGrid
                      documents={filteredDocuments}
                      onDownload={handleDownload}
                    />
                  )}
                </div>
              )}
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}