// File: /query/components/EmptyState.jsx
import React from 'react';
import queryEmpty from "../../../../../../../public/images/query_empty.png";
import Image from "next/image";

const EmptyState = () => (
  <div className="h-full flex flex-col items-center justify-center p-8 text-center">
    <div className="relative w-[300px] h-[200px] mb-6">
      <Image
        src={queryEmpty}
        alt="No queries"
        fill
        priority
        className="object-contain"
        sizes="(max-width: 300px) 100vw, 300px"
      />
    </div>
    <h2 className="typography-body-lg font-weight-semibold text-gray-800 mb-2">
      No Queries Saved Yet!
    </h2>
    <p className="text-gray-600 max-w-md">
      Your saved queries will appear here. Start by asking a question in the chat and
      save your favorites to build your library.
    </p>
  </div>
);

export default EmptyState;