'use client';
import React, { useContext, useRef, useEffect, useState } from 'react';
import CodeSnippetViewer from '@/components/CodeSnippetViewer';
import MermaidChart from '@/components/Chart/MermaidChart';
import { renderHTML } from '@/utils/helpers';
import Accordion from '@/components/BrowsePanel/Architecture/Accordian';
import { Download, Save } from 'lucide-react';
import { AlertContext } from '@/components/NotificationAlertService/AlertList';
import { uploadDocument } from '@/utils/api';
import { BootstrapTooltip } from '@/components/UIComponents/ToolTip/Tooltip-material-ui';
import '@/styles/components/download-button.css';

const DocumentView = ({ documentData, accordionKey, fetchContent = null, projectId, folderId, savedDocuments=[], setSavedDocuments }) => {
  const { showAlert } = useContext(AlertContext);
  const contentRef = useRef(null);
  const [exportingId, setExportingId] = useState(null);
  const [savingId, setSavingId] = useState(null);
  const [openAccordion, setOpenAccordion] = useState(null);

  const accordionRefs = useRef({});

  const documents = Array.isArray(documentData) ? documentData : [documentData];
  const completeDocuments = documents.filter(doc => doc?.isComplete);

  useEffect(() => {
    if (openAccordion) {
      scrollAccordionToView();
    } else if (completeDocuments.length > 0) {
      const lastDocId = `doc-${completeDocuments.length - 1}`;
      setOpenAccordion(lastDocId);
    }
  }, [documentData]);

  useEffect(() => {
    scrollAccordionToView();
  }, [openAccordion]);

  const handleAccordionToggle = (docId, docIndex = 0, title = "") => {
    setOpenAccordion(prev => {
      let newOpenId;
      if (prev === docId) {
        newOpenId = null;
      } else {
        if (fetchContent && title) {
          fetchContent(docIndex, title);
        }
        newOpenId = docId;
      }
      return newOpenId;
    });
  };

  const scrollAccordionToView = () => {
    if (openAccordion) {
      setTimeout(() => {
        const accordionElement = accordionRefs.current[openAccordion];
        const parentElement = accordionElement?.closest('.parent');
        if (accordionElement && parentElement) {
          accordionElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
          parentElement.style.paddingTop = "32px";
        }
      }, 100);
    } else {
      if (typeof document !== "undefined") {
        const parentElement = document.querySelector('.parent');
        if (parentElement) {
          parentElement.style.paddingTop = "16px";
        }
      }
    }
  };

  const handleExport = async (doc, type="download") => {
    try {
      if(type == "download"){
        setExportingId(doc.id || doc.title);
      }
      else {
        setSavingId(doc.id || doc.title);
      }
      const html2pdf = (await import('html2pdf.js')).default;

      if (!contentRef.current) {
        throw new Error('Content element not found');
      }

      const exportContainer = contentRef.current.cloneNode(true);

      // Remove control elements
      const controlElements = exportContainer.querySelectorAll(
        '.p-4.flex.justify-between.items-center.border-b, .mermaid-controls'
      );
      controlElements.forEach(element => element.remove());

      // Prevent content from splitting across pages
      const contentDivs = exportContainer.querySelectorAll('.prose > div');
      contentDivs.forEach(div => {
        div.style.pageBreakInside = 'avoid';
        div.style.breakInside = 'avoid';
      });

      const diagramElements = exportContainer.querySelectorAll('.mermaid, .relative');
      diagramElements.forEach(element => {
        element.style.pageBreakInside = 'avoid';
        element.style.breakInside = 'avoid';
      });

      // Convert all .markdown-code elements into inline <span> with the same visual style
      exportContainer.querySelectorAll('.markdown-code').forEach(codeEl => {
        const span = document.createElement('span');
        span.textContent = codeEl.textContent;

        // Force inline rendering with matching visual style
        Object.assign(span.style, {
          fontFamily: 'Inter, monospace',
          fontSize: 'var(--font-size-md)',
        });

        codeEl.replaceWith(span);
      });


      exportContainer.querySelectorAll('.markdown-pre').forEach(preEl => {
        const span = document.createElement('span');
        span.textContent = preEl.textContent;

        // Force inline rendering with matching visual style
        Object.assign(span.style, {
          fontFamily: 'Inter, monospace',
          fontSize: 'var(--font-size-md)',
        });

        codeEl.replaceWith(span);
      });

      const opt = {
        margin: 1,
        filename: `${(doc.title || 'document').replace(/^\s*#+\s*/, '').trim()}-export.pdf`,
        image: { type: 'jpeg', quality: 1 },
        html2canvas: {
          scale: 5,
          useCORS: true,
          logging: true
        },
        jsPDF: {
          unit: 'in',
          format: 'letter',
          orientation: 'portrait'
        },
        pagebreak: {
          mode: ['avoid-all', 'css', 'legacy'],
          before: '.page-break-before',
          after: '.page-break-after',
          avoid: ['.prose > div', '.mermaid', '.relative']
        }
      };

      if(type == 'download'){
        await html2pdf().set(opt).from(exportContainer).save();
        showAlert('PDF exported successfully!', 'success');
      }
      else if(type=="save"){ //save the document
        const pdfBlob = await html2pdf().set(opt).from(exportContainer).outputPdf('blob');
        const doc_title = doc.title || 'document';

        const formData = new FormData();
        const filename = `${doc_title}.pdf`;
        formData.append('file', pdfBlob, filename);
        formData.append('project_id', projectId);
        formData.append('doc_type', 'SAVED');
        formData.append('version', '1');
        formData.append('folder_id', folderId);

        const response = await uploadDocument(formData);

        if(response.message === "Document uploaded successfully"){
          setSavedDocuments && setSavedDocuments(prev => [...prev, doc_title + ".pdf"]);
          setSavingId(null);
          showAlert('Document uploaded successfully!', 'success');
        }
        else {
          showAlert('Failed to upload document', 'error');
          setSavingId(null);
        }
      }
    } catch (error) {
      showAlert('Failed to export PDF', error);
    } finally {
      setExportingId(null);
    }
  };

  if (!documentData) return null;

  return (
    <div className="parent space-y-4 p-4 relative">
      {completeDocuments.map((doc, index) => {
        const docId = `doc-${index}`;
        return (
          <Accordion
            key={`${index}-${accordionKey}`}
            id={`accordion-${index}`}
            ref={(el) => {
              if (el) accordionRefs.current[docId] = el;
            }}
            isAccordionOpen={openAccordion === docId}
            onToggle={() => handleAccordionToggle(docId, index, doc.title)}
            title={
              <div className="flex justify-between items-center w-full">
                <div className="flex items-center gap-4">
                  <span className="typography-heading-4">
                    {(doc.title || ``).replace(/^\s*#+\s*/, '').trim()}
                  </span>
                  {doc.timestamp &&
                    <div className="flex items-center typography-caption text-gray-500 italic font-weight-light">
                      <span className="inline-block w-1.5 h-1.5 bg-gray-300 rounded-full mr-1"></span>
                      <span>{new Date(doc.timestamp).toLocaleString()}</span>
                    </div>
                  }
                </div>
              </div>
            }
            headerExtra={(isOpen) => isOpen && (
              <>
               <BootstrapTooltip title={savingId=== (doc.id || doc.title) ? "Saving..." : savedDocuments && savedDocuments.includes(`${doc.title}.pdf`) ? "Already Saved to Documents": "Save to Documents"} placement="bottom">
                  <button
                    className={`flex items-center gap-2 py-1 typography-body-sm ${
                      savingId === (doc.id || doc.title)
                        ? 'text-gray-400'
                        : 'text-gray-600 hover:text-primary'
                    } transition-colors duration-200`}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleExport(doc, "save");
                    }}
                    disabled={savingId === (doc.id || doc.title) || savedDocuments.includes(`${doc.title}.pdf`)}
                  >
                    {savingId === (doc.id || doc.title) ? (
                      <>
                        <Save size={16} className='blink-text'/>
                      </>
                    ) : savedDocuments.includes(`${doc.title}.pdf`) ? (
                      <>
                        <Save size={16} className='text-green-500'/>
                      </>
                    ) : (
                      <>
                        <Save size={16} />
                      </>
                    )}
                  </button>
                </BootstrapTooltip>
                <BootstrapTooltip title={exportingId=== (doc.id || doc.title) ? "Downloading" : "Export to PDF"} placement="bottom">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleExport(doc, "download");
                    }}
                    disabled={exportingId === (doc.id || doc.title)}
                    className={`flex items-center gap-2 py-1 typography-body-sm ${
                      exportingId === (doc.id || doc.title)
                        ? 'text-gray-400'
                        : 'text-gray-600 hover:text-primary'
                    } transition-colors duration-200`}
                  >
                    {exportingId === (doc.id || doc.title) ? (
                      <>
                        <Download size={16} className='blink-text'/>
                      </>
                    ) : (
                      <>
                        <Download size={16} />
                      </>
                    )}
                  </button>
                </BootstrapTooltip>
              </>
            )}
          >
            {openAccordion !== docId ? <></> :
              <div className="prose max-w-none" ref={contentRef}>
                {doc.sections ? (
                  doc.sections.map((section, sectionIndex) => (
                    <div key={sectionIndex} className="mb-4">
                      {typeof section === 'string' ? (
                        <div className="project-panel-content break-words">
                          <span
                            dangerouslySetInnerHTML={{
                              __html: renderHTML(section),
                            }}
                          />
                        </div>
                      ) : (
                        <div className="mt-2">
                          {section.type === 'code' && (
                            <div className={`relative ${sectionIndex !== doc.sections.length - 1 ? "page-break-after" : ""}`}>
                              <CodeSnippetViewer
                                code={section.content}
                                language={section.language}
                              />
                            </div>
                          )}
                          {section.type === 'mermaid' && (
                            <div className="relative">
                              <MermaidChart chartDefinition={section.content} />
                            </div>
                          )}
                          {!section.type && section.content && (
                            <div className="project-panel-content break-words">
                              <span
                                dangerouslySetInnerHTML={{
                                  __html: renderHTML(section.content),
                                }}
                              />
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ))
                ) : fetchContent ? (
                  <div className="spinner animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-primary"></div>
                ) : <></>}
              </div>
            }
          </Accordion>
        );
      })}
    </div>
  );
};

export default DocumentView;
