import React from 'react';

const EmptyState = () => {
  return (
    <div className="flex flex-col items-center justify-center h-full p-8 text-center">
      <h2 className="typography-heading-4 font-weight-semibold text-gray-900 mb-2">How Can KAVIA Help?</h2>
      <p className="text-gray-600 mb-8 max-w-md">
        Start a new conversation to get assistance with your AI workflow management needs.
      </p>

      <div className="w-full max-w-3xl">
        <h3 className="text-gray-700 font-weight-medium mb-4 text-left">Key Features:</h3>
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="bg-gray-50 p-6 rounded-lg">
            <h4 className="font-weight-medium text-gray-900 mb-2">Static Code Analysis</h4>
            <p className="text-gray-600 typography-body-sm">
              Analyze code structure and patterns manage AI-driven tasks
            </p>
          </div>
          <div className="bg-gray-50 p-6 rounded-lg">
            <h4 className="font-weight-medium text-gray-900 mb-2">Architecture Query</h4>
            <p className="text-gray-600 typography-body-sm">
              Explore system architecture and components effectively
            </p>
          </div>
          <div className="bg-gray-50 p-6 rounded-lg">
            <h4 className="font-weight-medium text-gray-900 mb-2">Documentation Query</h4>
            <p className="text-gray-600 typography-body-sm">
              Access and analyze documentation and deadlines
            </p>
          </div>
          <div className="bg-gray-50 p-6 rounded-lg">
            <h4 className="font-weight-medium text-gray-900 mb-2">Security Analysis</h4>
            <p className="text-gray-600 typography-body-sm">
              Review security and test coverage strategies
            </p>
          </div>
        </div>

        <div className="bg-gray-50 border border-dashed border-gray-200 rounded-lg p-6">
          <h4 className="font-weight-medium text-gray-900 mb-2">And Many More...</h4>
          <p className="text-gray-600 typography-body-sm">
            Ask any code-related question - we're here to help!
          </p>
        </div>
      </div>
    </div>
  );
};

export default EmptyState;