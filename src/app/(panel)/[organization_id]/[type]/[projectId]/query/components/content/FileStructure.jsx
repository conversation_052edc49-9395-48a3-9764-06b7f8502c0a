import React from 'react';
import { FolderIcon, FileIcon } from 'lucide-react';

const DUMMY_STRUCTURE = [
  {
    type: 'folder',
    name: 'src',
    children: [
      {
        type: 'folder',
        name: 'controllers',
        description: 'Request handlers',
        children: [
          {
            type: 'file',
            name: 'UserController.ts',
            description: 'User authentication logic'
          },
          {
            type: 'file',
            name: 'AuthController.ts',
            description: 'Authentication flows'
          }
        ]
      },
      {
        type: 'folder',
        name: 'models',
        description: 'Data models',
        children: [
          {
            type: 'file',
            name: 'User.ts',
            description: 'User model definition'
          }
        ]
      },
      {
        type: 'folder',
        name: 'services',
        description: 'Business logic',
        children: [
          {
            type: 'file',
            name: 'AuthService.ts',
            description: 'Authentication service'
          },
          {
            type: 'file',
            name: 'TokenService.ts',
            description: 'JWT token management'
          }
        ]
      }
    ]
  },
  {
    type: 'file',
    name: 'package.json',
    description: 'Project dependencies'
  },
  {
    type: 'file',
    name: 'tsconfig.json',
    description: 'TypeScript configuration'
  }
];

const FileStructure = ({ structure = null }) => {
  const fileStructure = structure || DUMMY_STRUCTURE;

  const renderItem = (item, level = 0) => {
    const paddingClass = `pl-${Math.min(level * 4, 16)}`; // Limiting padding to avoid tailwind class issues
    
    if (item.type === 'folder') {
      return (
        <div key={item.name} className="file-structure-item">
          <div className={`flex items-center py-2 ${paddingClass} hover:bg-gray-50`}>
            <FolderIcon className="w-4 h-4 text-yellow-500 mr-2 flex-shrink-0" />
            <span className="font-weight-medium">{item.name}</span>
            {item.description && (
              <span className="text-gray-500 typography-body-sm ml-2">• {item.description}</span>
            )}
          </div>
          {item.children && item.children.length > 0 && (
            <div className="ml-4">
              {item.children.map(child => renderItem(child, level + 1))}
            </div>
          )}
        </div>
      );
    }
    
    return (
      <div key={item.name} className={`flex items-center py-2 ${paddingClass} hover:bg-gray-50`}>
        <FileIcon className="w-4 h-4 text-gray-500 mr-2 flex-shrink-0" />
        <span>{item.name}</span>
        {item.description && (
          <span className="text-gray-500 typography-body-sm ml-2">• {item.description}</span>
        )}
      </div>
    );
  };

  // If no structure is available, show an empty state
  if (!fileStructure || !Array.isArray(fileStructure) || fileStructure.length === 0) {
    return (
      <div className="p-6">
        <div className="bg-white rounded-lg border border-gray-200">
          <div className="border-b border-gray-200 p-4">
            <h3 className="typography-body-lg font-weight-medium text-gray-900">Project Structure</h3>
            <p className="typography-body-sm text-gray-500 mt-1">
              No file structure available
            </p>
          </div>
          <div className="p-4 text-center text-gray-500">
            No files or folders to display
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="border-b border-gray-200 p-4">
          <h3 className="typography-body-lg font-weight-medium text-gray-900">Project Structure</h3>
          <p className="typography-body-sm text-gray-500 mt-1">
            Project Directory Structure
          </p>
        </div>
        <div className="p-4">
          {fileStructure.map(item => renderItem(item))}
        </div>
      </div>
    </div>
  );
};



export default FileStructure;

