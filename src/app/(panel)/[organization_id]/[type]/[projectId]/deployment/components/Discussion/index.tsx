// deployment/components/Discussion/index.tsx
//@ts-nocheck
'use client';

import React, { useState } from 'react';
import { 
  FaTimes, 
  FaEye,
  FaCode, 
  FaFileCode,
  FaList,
  FaProjectDiagram,
  FaUserCog 
} from 'react-icons/fa';
import DiscussionChatPanel from '@/components/DiscussionChatPanel';
import CodeGenerationTerminalPanel from '@/components/CodeGenrationPanel/TerminalPanel/TerminalPanel';
// import CodeComponent from '@/components/CodeGenrationPanel/CodeComponent';

import StepsComponent from '@/components/CodeGenrationPanel/StepsComponent';
import DiscussionPanelOverview from './components/Overview';
import UserInput from "./components/UserInput";

interface DiscussionModalProps {
  isOpen: boolean;
  onClose: () => void;
  nodeId?: string;
  nodeType?: string;
}

const DiscussionModal: React.FC<DiscussionModalProps> = ({
  isOpen,
  onClose,
  nodeId,
  nodeType
}) => {
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [activeTab, setActiveTab] = useState('Overview');
  const [isCodeInitialized, setIsCodeInitialized] = useState(true);

  if (!isOpen) return null;

  const tabs = [
    { label: 'Overview', icon: FaEye },
    { label: 'Code', icon: FaFileCode },
    { label: 'Terminal', icon: FaCode },
    { label: 'Steps', icon: FaList },
    { label: 'Infrastructure Visualisation', icon: FaProjectDiagram },
    { label: 'User Input', icon: FaUserCog }
  ];

  const handleConfirmClose = () => {
    setShowConfirmModal(true);
  };

  const confirmCloseModal = () => {
    setShowConfirmModal(false);
    onClose();
  };

  return (
    <>
      <div className="fixed inset-0 bg-gray-800 bg-opacity-50 z-50" />
      <div className="fixed discussion-modal inset-0 bg-gray-800 bg-opacity-75 flex justify-center items-center z-50">
        <div className="bg-[#ffffff] rounded-sm w-[97%] h-[93%] flex flex-col relative">
          {/* Modal Title */}
          <div className="px-5 py-3 border-b border-gray-200">
            <h2 className="typography-body-lg font-weight-semibold text-[#2A3439]">Infrastructure Configuration</h2>
          </div>

          <div className="flex flex-grow overflow-hidden px-5 py-5">
            {/* Left Panel */}
            <div className="min-w-[30%] max-w-[30%] border border-gray-200 p-1 rounded-md">
              <DiscussionChatPanel />
            </div>

            {/* Right Panel */}
            <div className="flex-grow overflow-auto justify-between">
              <div className="flex flex-col h-full bg-opacity-20 border-r border-t border-b">
                {/* Main Tabs and Control Buttons */}
                <div className="tab-buttons flex flex-col border-b border-gray-200">
                  <div className="flex items-center justify-between w-full px-2 py-1">
                    <div className="flex overflow-x-auto items-center scrollbar-hide space-x-2">
                      {tabs.map(({ label, icon: Icon }) => (
                        <button
                          key={label}
                          role="tab"
                          aria-selected={activeTab === label}
                          className={`flex items-center gap-2 px-4 py-0.5 cursor-pointer whitespace-nowrap rounded-md ${
                            activeTab === label
                              ? 'text-[#1c64f2] bg-white shadow-sm border border-gray-200'
                              : 'text-gray-500 hover:bg-gray-100'
                          }`}
                          onClick={() => setActiveTab(label)}
                        >
                          <Icon size={16} />
                          <span className="mt-1">{label}</span>
                        </button>
                      ))}
                    </div>

                    {/* Close button */}
                    <div className="flex-shrink-0 ml-4">
                      <button
                        onClick={handleConfirmClose}
                        className="p-2 text-gray-500 hover:text-gray-700 bg-gray-200 rounded-sm hover:bg-gray-300 transition-colors"
                      >
                        <FaTimes size={20} />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Content Area */}
                <div className="overflow-auto custom-scrollbar flex-grow">
                  <div className="tab-content p-4">
                    {/* Overview Tab Content */}
                    {activeTab === 'Overview' && (
                      <div className="flex flex-col">
        <DiscussionPanelOverview />
                      </div>
                    )}

                    {/* Code Tab Content */}
                    {activeTab === 'Code' && (
                      <div style={{ overflow: 'hidden', height: '100%' }}>
                        {/* <CodeComponent /> */}
                        <p> Code component</p>
                      </div>
                    )}

                    {/* Terminal Tab Content */}
                    {activeTab === 'Terminal' && <CodeGenerationTerminalPanel />}

                    {/* Steps Tab Content */}
                    {activeTab === 'Steps' && <StepsComponent />}

                    {/* Infrastructure Visualisation Tab Content */}
                    {activeTab === 'Infrastructure Visualisation' && (
                      <div>Infrastructure Visualisation Content</div>
                    )}

                    {/* User Input Tab Content */}
                    {activeTab === 'User Input' && (
                    <UserInput/>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Confirmation Modal */}
      {showConfirmModal && (
        <div className="fixed inset-0 flex items-center justify-center z-[60]">
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setShowConfirmModal(false)} />
          <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6 z-[70] relative">
            <div className="typography-body-lg font-weight-semibold text-center mb-4">
              Confirm Close
            </div>
            <p className="typography-body-sm mb-4 text-gray-600 text-center">
              Are you sure you want to close the Infrastructure Configuration?
            </p>
            <div className="flex justify-center space-x-4">
              <button
                onClick={() => setShowConfirmModal(false)}
                className="py-2 px-4 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
              >
                Cancel
              </button>
              <button
                onClick={confirmCloseModal}
                className="py-2 px-4 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default DiscussionModal;