// components/Accordion.js
"use client"
import React, { useState } from "react";
import { ChevronDown, ChevronUp } from 'lucide-react';

const Accordion = ({ title,isopen=false, children }) => {
  const [isOpen, setIsOpen] = useState(isopen);
  const toggleAccordion = () => setIsOpen(!isOpen);

  return (
    <div className="border border-gray-300 rounded-lg shadow-sm bg-white w-full mb-2">
      <div
        className="flex justify-between items-center p-3 cursor-pointer hover:bg-gray-100 transition-colors"
        onClick={toggleAccordion}
      >
        <div className="accordion-title truncate">{title}</div>
        <div className="text-gray-600">
          {isOpen ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
        </div>
      </div>
      {isOpen && (
        <div className="p-4 bg-gray-50 text-font">
          {children}
        </div>
      )}
    </div>
  );
};

export default Accordion;
