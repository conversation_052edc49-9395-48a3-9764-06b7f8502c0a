// app/(users)/layout.tsx
import type { Metadata } from "next";
import React from "react";
import "../globals.css";

export const metadata: Metadata = {
  icons: [
    {
      rel: "icon",
      url: "/logo/kavia_light_logo.svg",
      media: "(prefers-color-scheme: light)",
    },
    {
      rel: "icon",
      url: "/logo/kavia_dark_logo.svg",
      media: "(prefers-color-scheme: dark)",
    },
  ],
  title: "Users",
  description:
    "Kavia AI is a pioneering startup based in San Francisco, dedicated to revolutionizing workflow management. Our cutting-edge AI tools leverage Large language models and deep learning techniques to dramatically accelerate time-to-market for new products and features, ensuring our clients lead in innovation. At Kavia AI, we are committed to transforming businesses by making workflow automation more efficient and effective.",
};

export default function UsersLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="users-layout">
      <main className="flex-grow overflow-hidden content-right-padding">{children}</main>
    </div>
  );
}
