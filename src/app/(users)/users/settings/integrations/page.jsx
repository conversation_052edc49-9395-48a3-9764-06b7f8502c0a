"use client"
import React,{  useState } from 'react'
import AccountList from "../../../../../components/AccountList/AccountList"
import AppCard from "../../../../../components/Appcards/Appcard";
import AccountDetails from "../../../../../components/AccountDetails/AccountDetails";

const Page = () => {
    const [isAppCardClicked, setIsAppCardClicked] = useState(0);
    const [selectedApp, setSelectedApp] = useState(null);

    const handleAppCardClick = (app) => {
        setSelectedApp({ app: app });
        setIsAppCardClicked(1);
      };
    
      const onIntegrationClick = () => {
        setIsAppCardClicked(0);
      };
    
      const onAccountClick = (account) => {
        setSelectedApp({ ...selectedApp, account: account });
        setIsAppCardClicked(2);
      };
  return (
    <>
    <div >
        {isAppCardClicked === 0 ?(
            <AppCard
            onCardClick={handleAppCardClick}
            cardLists={[
              {
                title: "Popular",
                isSearch: true,
                listArray: [
                  {
                    title: "GitHub",
                    description:
                      "GitHub is a developer platform that allows developers to create, store, manage, and share their code.",
                    imgSrc: "https://cdn-icons-png.flaticon.com/512/25/25231.png",
                    isConnected: true,
                    isMenu: true,
                    connectedSettingsArray: ["Manage", "Disconnect"],
                  },
                  {
                    title: "Jira",
                    description:
                      "Jira is a project management tool that helps teams plan, track, and manage work.",
                    imgSrc: "https://cdn-icons-png.flaticon.com/512/915/915298.png",
                    isConnected: false,
                    isMenu: true,
                  },
                  {
                    title: "Google Workspace",
                    description:
                      "Google Workspace is a suite of cloud computing and collaboration tools.",
                    imgSrc: "https://cdn-icons-png.flaticon.com/512/281/281764.png",
                    isConnected: false,
                    isMenu: true,
                  },
                  {
                    title: "Slack",
                    description:
                      "Slack is a collaboration platform that helps teams communicate efficiently.",
                    imgSrc: "https://cdn-icons-png.flaticon.com/512/2111/2111615.png",
                    isConnected: false,
                    isMenu: true,
                  },
                ],
              },
              {
                title: "All apps",
                isSearch: false,
                listArray: [
                  {
                    title: "GitHub",
                    description:
                      "GitHub is a developer platform that allows developers to create, store, manage, and share their code.",
                    imgSrc: "https://cdn-icons-png.flaticon.com/512/25/25231.png",
                    isConnected: true,
                    connectedSettingsArray: ["Manage", "Disconnect"],
                  },
                  {
                    title: "Jira",
                    description:
                      "Jira is a project management tool that helps teams plan, track, and manage work.",
                    imgSrc: "https://cdn-icons-png.flaticon.com/512/915/915298.png",
                    isConnected: false,
                  },
                  {
                    title: "Google Workspace",
                    description:
                      "Google Workspace is a suite of cloud computing and collaboration tools.",
                    imgSrc: "https://cdn-icons-png.flaticon.com/512/281/281764.png",
                    isConnected: false,
                  },
                  {
                    title: "Slack",
                    description:
                      "Slack is a collaboration platform that helps teams communicate efficiently.",
                    imgSrc: "https://cdn-icons-png.flaticon.com/512/2111/2111615.png",
                    isConnected: false,
                  },
                ],
              },
            ]}
          />
        ) : isAppCardClicked === 1 ? (
            <AccountList
                  title={selectedApp?.app?.title}
                  onCardClick={onAccountClick}
                  onIntegrationClick={onIntegrationClick}
                  accountList={[
                    {
                      title: "Account 1",
                      isConnected: true,
                      togglebuttonsList: ["Manage", "Disconnect"],
                      id: "**********",
                      lastSynced: "36 mins",
                    },
                    {
                      title: "Account 2",
                      isConnected: false,
                      togglebuttonsList: ["Manage", "Connect"],
                      id: "***********",
                      lastSynced: "2 hrs",
                    },
                  ]}
                />
        ) :isAppCardClicked === 2 ?(
            <AccountDetails
                  title={selectedApp?.account?.title}
                  cardDetails={selectedApp?.account}
                  connectedSettingsArray={["Manage", "Disconnect"]}
                  onIntegrationClick={onIntegrationClick}
                />
        ):""}
    </div>
    </>
  )
}

export default Page