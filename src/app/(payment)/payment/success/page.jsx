"use client"
import React, { useState, useEffect ,useContext} from 'react'
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '@/components/KaviaLongLogo'
import { useRouter } from 'next/navigation'
import CheckIcon from '../../../../../public/images/payment/check_icon.svg'
import Image from 'next/image'
import { handlePaymentSuccess} from '@/utils/paymentAPI'
import {checkSubscriptionStatus} from "@/utils/api"
import { useSearchParams } from 'next/navigation'
import useAuth from '@/hooks/useAutoLogout'
import { AlertContext } from '@/components/NotificationAlertService/AlertList'

function SuccessPage() {
    const router = useRouter()
    const searchParams = useSearchParams()
    const [countdown, setCountdown] = useState(3)
    const [isProcessing, setIsProcessing] = useState(true)
    const { handleLogout } = useAuth()
    const {showAlert} = useContext(AlertContext)

    // Handle payment success logic
    useEffect(() => {
        const processPayment = async () => {
            try {
                const internal_secret_token = searchParams.get('internal_secret_token')
                if (!internal_secret_token) {
                    // return
                }

                await handlePaymentSuccess(internal_secret_token)
                setIsProcessing(false)
            } catch (error) {
                setIsProcessing(false)
            }
        }

        processPayment()
    }, [searchParams])

   useEffect(() => {
    if (isProcessing) return;

    let timer;
    if (countdown > 0) {
        timer = setInterval(() => {
            setCountdown((prev) => prev - 1);
        }, 1000);
    } else {
        const checkStatus = async () => {
            try {
                const result = await checkSubscriptionStatus();
                showAlert("Successfully upgraded your plans",'success')
            } catch (error) {
                console.error('Subscription check error:', error);
            }
            finally {
                handleLogout()
            }
        };
        checkStatus();
    }

    return () => {
        if (timer) clearInterval(timer);
    }
}, [countdown, router, isProcessing])

    return (
        <div className='min-h-screen bg-semantic-gray-900 flex flex-col relative'>
            {/* Add the gradient overlay */}
            <div className="absolute inset-0 bg-[radial-gradient(50%_50%_at_50%_50%,hsl(var(--primary))_0%,hsl(var(--semantic-gray-900))_100%)] opacity-60 blur-[514px] pointer-events-none" />

            {/* Logo */}
            <div className='flex items-center gap-2 p-[3vh] md:absolute md:top-[3vh] md:left-[3vw] relative z-10'>
                <KaviaLongLogo />
            </div>

            <div className='flex flex-col items-center justify-center flex-grow relative z-10'>
                <div className='w-full max-w-md p-8 bg-white rounded-lg shadow-lg text-center'>
                    {isProcessing ? (
                        <div className='flex flex-col items-center'>
                            <div className='animate-spin rounded-full w-[3rem] h-[3rem] border-t-2 border-b-2 border-orange-500 mb-4'></div>
                            <p className='text-gray-600'>Processing your payment...</p>
                        </div>
                    ) : (
                        <>
                            <div className='mb-6 flex justify-center'>
                                <div className='bg-green-100 rounded-full p-3'>
                                    <Image src={CheckIcon} alt="Success" width={40} height={40} />
                                </div>
                            </div>

                            <h1 className='typography-heading-2 font-weight-bold mb-4'>Payment Successful!</h1>
                            <p className='text-semantic-gray-600 mb-6'>Thank you for your purchase. You’ll be logged out to refresh your permissions.
                            Please log in again to continue.</p>

                            <p className='text-semantic-gray-600 mb-6'>
                                You will be redirected to the login page in {countdown} second{countdown !== 1 ? 's' : ''}...
                            </p>

                            <button
                                onClick={() => router.push('/users/login')}
                                className='w-full py-2 px-4 bg-primary hover:bg-primary/90 text-primary-foreground font-weight-semibold rounded-lg transition duration-200'
                            >
                                Go to Login Now
                            </button>
                        </>
                    )}
                </div>
            </div>
        </div>
    )
}

export default SuccessPage
