// src/app/dashboard/layout.tsx
'use client'
import { Sidebar } from '@/components/UserOnboarding/Sidebar';
import { Header } from '@/components/UserOnboarding/Header';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex flex-col h-screen bg-custom-bg-primary">
      <Header />
      <div className="flex flex-1 overflow-hidden">
        <Sidebar />
        <main className="flex-1 overflow-x-hidden overflow-y-auto p-3 content-right-padding">
          {children}
        </main>
      </div>
    </div>
  );
}