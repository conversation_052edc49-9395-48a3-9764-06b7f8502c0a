"use client"
import { useState, useEffect } from 'react';
import { NotificationPermission } from '@/components/NotificationPermission';
import { NotificationSettings } from '@/components/NotificationSettings';
import { NotificationDisplay } from '@/components/NotificationDisplay';
import { NotificationLoader } from '@/components/UIComponents/Loaders/LoaderGroup';

const NotificationsPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <NotificationLoader />;
  }
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center mb-8">
        <h1 className="typography-heading-2 font-weight-bold mr-7">Notifications</h1>
        <NotificationPermission />
      </div>
      
      <div className="space-y-8">
        <NotificationSettings />
        <NotificationDisplay />
      </div>
    </div>
  );
  
}

export default NotificationsPage;