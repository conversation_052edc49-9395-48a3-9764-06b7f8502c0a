'use client'

import { useEffect, useState } from "react";
import { fetchGroupDetails } from "@/utils/api";
import GroupPermission from '@/components/UserOnboarding/Groups/GroupPermission';
import Loader from "@/components/UserOnboarding/ui/Loader";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { useUser } from "@/components/Context/UserContext";

interface Permission {
  create: boolean;
  read: boolean;
  update: boolean;
  delete: boolean;
  merge: boolean;
}

interface Feature {
  name: string;
  permissions: Permission;
}

interface ApiPermissions {
  [key: string]: Permission;
}

interface ApiResponse {
  permissions: ApiPermissions;
  // ... other fields from API response if needed
}

const PermissionsPage = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [features, setFeatures] = useState<Feature[]>([]);
  const { tenant_id } = useUser();  

  useEffect(() => {
    async function fetchData() {
      try {
        const orgId = tenant_id;
        const path = window.location.pathname;
        const groupId = path.split("/groups/")[1].split("/")[0];

        if (!groupId) {
          
          return;
        }

        const data = await fetchGroupDetails(orgId, groupId) as ApiResponse;

        // Transform the permissions data with proper typing
        const transformedFeatures: Feature[] = Object.entries(data.permissions).map(([key, permissions]: [string, Permission]) => ({
          name: key.replace(/([A-Z])/g, ' $1').trim(),
          permissions: {
            create: Boolean(permissions.create),
            read: Boolean(permissions.read),
            update: Boolean(permissions.update),
            delete: Boolean(permissions.delete),
            merge: Boolean(permissions.merge)
          }
        }));

        setFeatures(transformedFeatures);
      } catch (error) {
        
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  const handlePermissionChange = (updatedFeatures: Feature[]) => {
    setFeatures(updatedFeatures);
  };

  if (loading) {
    return <Loader type="permissions" />;
  }

  if (features.length === 0) {
    return <EmptyStateView
    type="noPermissions"
    onClick={() => {}}
/>;
  }

  return (
    <div className="p-1">
      <GroupPermission 
        initialFeatures={features}
        onPermissionChange={handlePermissionChange}
      />
    </div>
  );
};

export default PermissionsPage;