"use client";
import { useEffect, useState } from "react";
import { fetchGroupDetails } from "@/utils/api";
import GroupStatCard from "@/components/UserOnboarding/ui/groupStatCard";
import GroupInformation from "@/components/UserOnboarding/Groups/GroupInformation";
import { Building } from "lucide-react";
import Loader from "@/components/UserOnboarding/ui/Loader";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { useUser } from "@/components/Context/UserContext";

interface User {
  id: string;
  name: string;
  email: string;
  status: string;
}

interface GroupData {
  name: string;
  description: string;
  created_by: string;
  created_at: string;
  access_level: string;
  status: string;
  users: User[];
  created_by_user: User;
}

const Page = () => {
  const [groupData, setGroupData] = useState<GroupData | null>(null);
  const [loading, setLoading] = useState(true);
  const { tenant_id } = useUser();

  useEffect(() => {
    async function fetchData() {
      try {
        const orgId = tenant_id;
        // Get the current URL path
        const path = window.location.pathname;
        // Extract group ID using regex or split
        const groupId = path.split("/groups/")[1].split("/")[0];
        // Or alternatively: path.match(/groups\/(grp-[^/]+)/)?.[1];

        if (!groupId) {
          
          return;
        }

        const data = await fetchGroupDetails(orgId, groupId);
        setGroupData(data);
      } catch (error) {
        
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);
  if (loading) {
    return <Loader type="groupDetails" />;
  }

  if (!groupData) {
    return <EmptyStateView type="noOverview" onClick={() => {}} />;
  }

  const memberCount = groupData.users?.length || 0;
  const memberLabel = memberCount === 1 ? "Member" : "Members";
  const activeMembers = groupData.users?.filter(user => user.status === 'active').length || 0;

  const groupDetails = {
    title: "Group Information",
    icon: Building,
    details: [
      {
        label: "Created By",
        value: groupData?.created_by_user?.name || "N/A",
      },
      {
        label: "Created Date",
        value: new Date(groupData.created_at).toLocaleDateString("en-US", {
          year: "numeric",
          month: "long",
          day: "numeric",
        }),
      },
      {
        label: "Access Label",
        value: groupData.access_level || "N/A",
      },
      {
        label: "Status",
        value:
          groupData.status?.charAt(0).toUpperCase() +
            groupData.status?.slice(1) || "N/A",
      },
    ],
    description: groupData.description || "No description available",
  };

  return (
    <div className="w-full flex flex-col gap-10">
      <div className="flex gap-7">
        <GroupStatCard
          title={memberLabel}
          value={memberCount}
          details="Total group members"
          type="members"
        />
        <GroupStatCard
          title="Active Members"
          value={activeMembers}
          details="Total active members"
          type="active"
        />
        <GroupStatCard
          title="Resources Access"
          value={0}
          details="Data not available"
          type="resources"
        />
      </div>

      <GroupInformation {...groupDetails} />
    </div>
  );
};

export default Page;
