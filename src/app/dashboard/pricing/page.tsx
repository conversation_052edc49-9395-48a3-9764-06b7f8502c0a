// src/app/dashboard/pricing/page.tsx
'use client';

import { useRouter } from 'next/navigation';
import React, { useState } from 'react';
import PlanSelection from '@/components/UserOnboarding/AddOrganization/PlanSelection';
import FeatureConfiguration from '@/components/UserOnboarding/AddOrganization/FeatureConfiguration';
import type { BillingCycle, OrganizationState } from '@/types';

type Step = 'planSelection' | 'featureConfiguration';

// Remove this interface and change the component definition
export default function Page() {  // Changed this line
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState<Step>('planSelection');
  const [selectedPlanDetails, setSelectedPlanDetails] = useState<{
    planIndex: number;
    billingCycle: BillingCycle;
  } | null>(null);

  const handleTabClick = (tabId: string) => {  // Added this function
    // Add your tab click handling logic here
    router.push(`/dashboard/${tabId}`);
  };

  const handlePlanSelection = (planIndex: number, billingCycle: BillingCycle) => {
    setSelectedPlanDetails({ planIndex, billingCycle });
    setCurrentStep('featureConfiguration');
  };

  const handleBack = () => {
    setCurrentStep('planSelection');
  };

  const handleNext = (featureConfig: OrganizationState['featureConfiguration']) => {
    localStorage.setItem('featureConfig', JSON.stringify(featureConfig));
    router.push('/dashboard/review');
  };

  return (
    <div className="w-full">
      {currentStep === 'planSelection' && (
        <PlanSelection
          selectedPlan={selectedPlanDetails?.planIndex}
          billingCycle={selectedPlanDetails?.billingCycle || 'monthly'}
          onNext={handlePlanSelection}
          back={handleTabClick}
        />
      )}

      {currentStep === 'featureConfiguration' && selectedPlanDetails && (
        <FeatureConfiguration
          selectedPlan={selectedPlanDetails.planIndex}
          onBack={handleBack}
          onNext={() => handleTabClick("reviewandconfirm")}
        />
      )}
    </div>
  );
}