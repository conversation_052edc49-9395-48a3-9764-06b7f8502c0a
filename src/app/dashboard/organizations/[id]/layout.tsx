// src/app/dashboard/organizations/[id]/layout.tsx
'use client'

import { FC, ReactNode, useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import TabNav from '@/components/UserOnboarding/Navigation/TabNav';
import OrganizationHeader from '@/components/UserOnboarding/OrganizationDetails/OrganizationHeader';
import Breadcrumb from '@/components/UserOnboarding/Navigation/BreadCrumb';
import { fetchOrganization } from '@/utils/api';

interface LayoutProps {
  children: ReactNode;
  params: {
    id: string;
  };
}

interface Organization {
  name: string;
  business_email: string;
  industrial_type: string;
  company_size: string;
  domain: string;
  image: string;
  plan_id: string;
  admin_id: string;
  configurations: {
    max_users: number;
    role_customization: boolean;
    api_access: boolean;
    github_integration: boolean;
    jira_integration: boolean;
    custom_reports: boolean;
    export_capabilities: boolean;
  };
  group_ids: string[];
  created_at: string;
  updated_at: string;
  id: string;
}

const OrganizationLayout: FC<LayoutProps> = ({ children, params }) => {
  const pathname = usePathname();
  const [organizationName, setOrganizationName] = useState<string | null>(null);
  const [organizationData, setOrganizationData] = useState<Organization | null>(null);
  const [copied, setCopied] = useState(false);
  const [loginUrl, setLoginUrl] = useState<string | null>(null);
  const isCostsPage = pathname?.includes('/costs');

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const protocol = window.location.protocol;
      const domain = window.location.host;
      setLoginUrl(`${protocol}//${domain}/users/login?tenant_id=${encodeURIComponent(params.id)}`);
    }
  }, [params.id]);

  useEffect(() => {
    const fetchOrganizationData = async () => {
      const data = await fetchOrganization(params.id);
      setOrganizationData(data);
    };
    fetchOrganizationData();
  }, [params.id]);

  const getBreadcrumbItems = () => {
    const items = [
      { label: 'Home', href: '/dashboard', active: false },
      { label: 'All Organizations', href: '/dashboard/organizations', active: false }
    ];

    if (pathname === `/dashboard/organizations`) {
      items[1].active = true;
    } else {
      items.push({
        label: organizationData?.name || 'Loading...',
        href: `/dashboard/organizations/${params.id}`,
        active: true
      });
    }

    return items;
  };

  return (
    <div className="flex flex-col h-[88vh]">
      {/* Fixed Header Section */}
      <div className="flex-none w-full max-w-9xl mx-auto px-2 lg:px-2">
        <Breadcrumb items={getBreadcrumbItems()} />
        <OrganizationHeader
          name={organizationData?.name || 'Loading...'}
          status="active"
          planType="Build Plan"
          onboardedDate={organizationData?.created_at
            ? new Date(organizationData.created_at).toLocaleDateString('en-US',
                { month: 'long', day: 'numeric', year: 'numeric' })
            : 'Loading...'}
        />
        <TabNav organizationId={params.id} />
      </div>

      {/* Content Section - Conditional scrolling based on page */}
      <div className={`flex-1 ${!isCostsPage ? 'overflow-hidden' : ''}`}>
        <div className={`h-full ${!isCostsPage ? 'overflow-y-auto custom-scrollbar' : ''}`}>
          <div className="w-full max-w-9xl mx-auto px-2 sm:px-2 lg:px-3 pt-1">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrganizationLayout;