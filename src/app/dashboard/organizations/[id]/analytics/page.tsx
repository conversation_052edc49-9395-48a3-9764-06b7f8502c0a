// src/app/dashboard/organizations/[id]/usage-analytics/page.tsx
'use client';

import React from 'react';
import Alert from '@/components/UserOnboarding/ui/Alert';
import EmptyStateView from "@/components/Modal/EmptyStateModal";

interface Alert {
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
}

const UsageAnalytics: React.FC = () => {
  // This could come from an API or props
  const recentAlerts: Alert[] = [
    {
      type: 'error',
      message: 'Approaching storage limit (78% used)'
    },
    {
      type: 'success',
      message: 'API usage within normal limits'
    },
    {
      type: 'warning',
      message: 'User limit warning: Added 40 users this month'
    }
  ];

  return (
    <div className="mt-20">
    <EmptyStateView type="featureNotImplemented" onClick={() => {}} />
  </div>
  );
};

export default UsageAnalytics;