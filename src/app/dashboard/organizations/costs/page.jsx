'use client'

import React, { useEffect, useState } from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { getAllOrganizationsCosts } from '@/utils/api'; // Adjust this path to match your project structure

const Page = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await getAllOrganizationsCosts();
        // Transform data to convert string dollar amounts to numbers
        const transformedData = response.map(item => ({
          organization_id: item.organization_id,
          cost: parseFloat(item.organization_cost.replace('$', ''))
        }));
        setData(transformedData);
      } catch (err) {
        
        setError('Failed to load organization costs');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-96 text-red-500">
        {error}
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-96 text-gray-500 typography-body-lg">
        No cost data found
      </div>
    );
  }

  return (
    <div className="p-6 bg-white rounded-lg shadow-sm">
      <h2 className="typography-heading-2 font-weight-bold mb-6 text-gray-800">Organization Costs Overview</h2>
      <div className="h-96 w-full">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart 
            data={data} 
            margin={{ top: 20, right: 30, left: 40, bottom: 20 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="organization_id" 
              label={{ value: 'Organization ID', position: 'bottom', offset: 0 }}
            />
            <YAxis 
              label={{ value: 'Cost ($)', angle: -90, position: 'insideLeft', offset: -10 }}
              tickFormatter={(value) => `$${value.toFixed(6)}`}
            />
            <Tooltip 
              formatter={(value) => [`$${value.toFixed(6)}`, 'Cost']}
              labelFormatter={(label) => `Organization: ${label}`}
            />
            <Bar 
              dataKey="cost" 
              fill="#F97316" 
              name="Cost"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default Page;