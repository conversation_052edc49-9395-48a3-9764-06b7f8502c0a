// src/components/DraftsList/DraftsList.tsx
import { Trash2 } from 'lucide-react';

interface AnnouncementDraft {
  id: string;
  content: string;
  detailed_content?: string;
  type: 'alert' | 'maintenance' | 'announcement';
  created_at: string;
  last_saved: string;
  user_id: string;
  tenant_id: string;
}

interface DraftsListProps {
  drafts: AnnouncementDraft[];
  isLoading: boolean;
  deletingDraftId: string | null;
  onLoadDraft: (draft: AnnouncementDraft) => void;
  onDeleteDraft: (draftId: string) => void;
}

export const DraftsList = ({
  drafts,
  isLoading,
  deletingDraftId,
  onLoadDraft,
  onDeleteDraft
}: DraftsListProps) => {
  if (isLoading) {
    return <div className="text-center py-4">Loading drafts...</div>;
  }

  if (!drafts.length) {
    return null;
  }

  return (
    <div className="mt-8 p-6 bg-white rounded-lg shadow-sm border">
      <h2 className="typography-body-lg font-weight-semibold mb-4">Saved Drafts</h2>
      <div className="space-y-3">
        {drafts.map((draft) => (
          <div
            key={draft.id}
            className="flex items-center justify-between p-4 border rounded-md hover:bg-gray-50"
          >
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <span className="typography-body-sm font-weight-medium text-gray-500">
                  {draft.type === 'alert' ? '⚠️' : 
                   draft.type === 'maintenance' ? '🔧' : '📢'}
                </span>
                <p className="font-weight-medium truncate">{draft.content}</p>
              </div>
              <p className="typography-body-sm text-gray-500">
                Last saved: {new Date(draft.last_saved).toLocaleString()}
              </p>
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => onLoadDraft(draft)}
                className="px-3 py-1 text-primary hover:bg-primary-50 rounded"
              >
                Load
              </button>
              <button
                onClick={() => onDeleteDraft(draft.id)}
                disabled={deletingDraftId === draft.id}
                className="text-red-600 hover:bg-red-50 p-1 rounded disabled:opacity-50"
              >
                {deletingDraftId === draft.id ? (
                  <span className="flex items-center">
                    <svg className="animate-spin h-4 w-4 mr-2" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                    </svg>
                  </span>
                ) : (
                  <Trash2 className="w-4 h-4" />
                )}
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};