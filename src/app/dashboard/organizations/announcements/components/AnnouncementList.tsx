
import { Trash2 } from 'lucide-react';
import { renderHTML } from '@/utils/helpers';

interface Announcement {
    id: string;
    content: string;
    detailed_content?: string;
    type: 'alert' | 'maintenance' | 'announcement';
    is_active: boolean;
    published_at: string;
    expires_at?: string;
    created_at: string;
  }
  
  interface AnnouncementsListProps {
    announcements: Announcement[];
    isLoading: boolean;
    deletingId: string | null;
    onDelete: (id: string) => void;
  }
  
  export const AnnouncementsList = ({
    announcements,
    isLoading,
    deletingId,
    onDelete
  }: AnnouncementsListProps) => {
    return (
      <div className="space-y-4">
        {isLoading ? (
          <div className="text-center py-4">Loading announcements...</div>
        ) : announcements.length === 0 ? (
          <div className="text-center py-4 text-gray-500">No announcements found</div>
        ) : (
          announcements.map((announcement) => (
            <div
              key={announcement.id}
              className={`p-4 border rounded-md ${
                announcement.is_active ? 'bg-white' : 'bg-gray-50'
              }`}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="typography-body-sm font-weight-medium text-gray-500">
                      {announcement.type === 'alert' ? '⚠️' : 
                       announcement.type === 'maintenance' ? '🔧' : '📢'}
                    </span>
                    <p className="font-weight-medium">{announcement.content}</p>
                    {announcement.is_active ? (
                      <span className="px-2 py-1 typography-caption font-weight-medium text-green-700 bg-green-50 rounded-full">
                        Active
                      </span>
                    ) : (
                      <span className="px-2 py-1 typography-caption font-weight-medium text-gray-600 bg-gray-100 rounded-full">
                        Inactive
                      </span>
                    )}
                  </div>
                  
                  <div className="mt-2 typography-body-sm text-gray-500">
                    {announcement.detailed_content && (
                      <div className="mb-2 banner-sp652-div" dangerouslySetInnerHTML={{
                        __html: renderHTML(announcement.detailed_content)
                      }} />
                    )}
                    <div className="flex gap-4 mt-2">
                      <span>Published: {new Date(announcement.published_at).toLocaleString()}</span>
                      {announcement.expires_at && (
                        <span>Expires: {new Date(announcement.expires_at).toLocaleString()}</span>
                      )}
                    </div>
                  </div>
                </div>
                
                <button
                  onClick={() => onDelete(announcement.id)}
                  disabled={deletingId === announcement.id}
                  className="text-red-600 hover:bg-red-50 p-2 rounded disabled:opacity-50"
                >
                  {deletingId === announcement.id ? (
                    <span className="flex items-center">
                      <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                      </svg>
                    </span>
                  ) : (
                    <Trash2 className="w-4 h-4" />
                  )}
                </button>
              </div>
            </div>
          ))
        )}
      </div>
    );
  };