'use client';

import { useState, useEffect } from "react";
import { AddOragnizationForm } from "@/components/UserOnboarding/AddOrganizationForm";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { AdminSetupForm } from "@/components/UserOnboarding/AdminSetupForm";
import ReviewConfirm from "../pending-approval/[orgname]/components/ReviewConfirm";
import { updateSearchParams } from "@/utils/helpers";
import ProgressBar from "@/components/UserOnboarding/Navigation/ProgressBar";
import PlanSelection from "@/components/UserOnboarding/AddOrganization/PlanSelection"
import FeatureConfiguration from "@/components/UserOnboarding/AddOrganization/FeatureConfiguration"
import type { BillingCycle } from '@/types';

  
export default function Page(){
    const [currentTab, setCurrentTab] = useState("organizationdetails")
    const [selectedPlanDetails, setSelectedPlanDetails] = useState<{
        planIndex: number;
        billingCycle: BillingCycle;
      } | null>(null);
    const pathName = usePathname();
    const router = useRouter();
    const searchParams = useSearchParams();
    const queryTab = searchParams.get("tab");

    useEffect (() => {
        if (!queryTab){
            updateSearchParams(router, pathName, searchParams, "tab", "organizationdetails");
        } 
        else {
            setCurrentTab(queryTab);
        }
    }, [queryTab, pathName, router, searchParams]);

    const handleTabClick = (tabId: string) => {
        updateSearchParams(router, pathName, searchParams, "tab", tabId);
        setCurrentTab(tabId)
    };

    const handlePlanSelection = (planIndex: number, billingCycle: BillingCycle) => {
        setSelectedPlanDetails({ planIndex, billingCycle });
        handleTabClick("featureconfiguration")
      };
    const onConfirm = () => {
        return null;
    }

    const progressBarItems = [
        {label: "Organization Details", key: "organizationdetails"},
        {label: "Admin Setup", key: "adminsetup"},
        {label: "Plan Selection", key: "planselection"},
        {label: "Feature Configuration", key: "featureconfiguration"},
        {label: "Review & Confirm", key: "reviewandconfirm"}
    ]

    return(
        <div className="w-full px-3">
            <h1 className="project-panel-heading">Add Organization</h1>

            <ProgressBar items = {progressBarItems} changeTab={handleTabClick}/>

            {currentTab === "organizationdetails" && <AddOragnizationForm
                handleTabClick = {() =>handleTabClick("adminsetup")}
            />}
            {currentTab === "adminsetup" && 
                <AdminSetupForm 
                onBack={() => handleTabClick("organizationdetails")}
                onNext={() => handleTabClick("planselection")}
                />
            }
            {currentTab === "planselection" && 
                <PlanSelection
                    selectedPlan={selectedPlanDetails?.planIndex}
                    billingCycle={selectedPlanDetails?.billingCycle || 'monthly'}
                    onNext={handlePlanSelection}
                    back={() => handleTabClick("adminsetup")}
                />
            }
            {currentTab === "featureconfiguration" && selectedPlanDetails &&
                <FeatureConfiguration
                    selectedPlan={selectedPlanDetails.planIndex}
                    onBack={() => handleTabClick("planselection")}
                    onNext={() => handleTabClick("reviewandconfirm")}
                />
            }
            {currentTab === "reviewandconfirm" && <ReviewConfirm onBack={() => handleTabClick("featureconfiguration")} onConfirm={onConfirm}/>}
        </div>
    );
}