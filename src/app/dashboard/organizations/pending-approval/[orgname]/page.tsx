'use client';

import React from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import DetailsSection from "@/components/UserOnboarding/PendingApproval/DetailsSection"
import Breadcrumb from "@/components/UserOnboarding/Navigation/BreadCrumb";
import { Building, User, CreditCard } from 'lucide-react';
import FeatureConfiguration from "./components/FeatureConfiguration"
import ReviewConfirm from "./components/ReviewConfirm"

interface OrganizationDetails {
  organizationName: string;
  industryType: string;
  companySize: string;
  businessEmail: string;
  primaryAdmin: string;
  email: string;
  contactNumber: string;
  planName: string;
  billingCycle: string;
}

const VerifyOrganizationPage = () => {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Get orgName from URL parameter
  const orgName = decodeURIComponent(String(params.orgname))
    .replace('orgname=', '')
    .replace(/['"]+/g, '')
    .trim();

  const isFeatureConfig = searchParams.get('feature_config') === 'true';
  const isConfirm = searchParams.get('confirm') === 'true';

  
  const handleFeatureBack = React.useCallback(() => {
    router.back();
  }, [router]);

  const handleFeatureNext = React.useCallback(() => {
    router.push(`/dashboard/organizations/pending-approval/${params.orgname}?orgname="${orgName}"&feature_config=true&confirm=true`);
  }, [router, params.orgname, orgName]);

  const handleConfirmBack = React.useCallback(() => {
    router.push(`/dashboard/organizations/pending-approval/${params.orgname}?orgname="${orgName}"&feature_config=true`);
  }, [router, params.orgname, orgName]);
  
  const handleFeatureConfigClick = React.useCallback(() => {
    router.push(`/dashboard/organizations/pending-approval/${params.orgname}?orgname="${orgName}"&feature_config=true`);
  }, [router, params.orgname, orgName]);

  const handleConfirmCreate = React.useCallback(() => {
    router.push('/dashboard/organizations');
  }, [router]);

  // Get current step based on URL parameters
  const getCurrentStep = () => {
    if (isFeatureConfig && isConfirm) return 2;
    if (isFeatureConfig) return 1;
    return 0;
  };

  const currentStep = getCurrentStep();

  const steps = [
    { label: "Organisation Detail", isCompleted: currentStep > 0, isCurrent: currentStep === 0 },
    { label: "Feature Configuration", isCompleted: currentStep > 1, isCurrent: currentStep === 1 },
    { label: "Review & Confirm", isCompleted: false, isCurrent: currentStep === 2 }
  ];

  // Organization Details
  const organizationDetails: OrganizationDetails = {
    organizationName: "Acme Corporation",
    industryType: "Technology",
    companySize: "51-200 employees",
    businessEmail: "acmecorp.com",
    primaryAdmin: "John Doe",
    email: "<EMAIL>",
    contactNumber: "909090909090",
    planName: "Build",
    billingCycle: "Annual"
  };

  const breadcrumbItems = [
    { label: 'Home', href: '/dashboard' },
    { label: 'Pending Approval', href: '/dashboard/organizations/pending-approval' },
    { 
      label: orgName, 
      href: `/dashboard/organizations/pending-approval?orgname="${orgName}"`,
      active: true 
    }
  ];

  // Review & Confirm View
  if (isFeatureConfig && isConfirm) {
    return (
      <div className="max-w-[1200px] p-3">
        <div className="-mt-4">
          <Breadcrumb items={breadcrumbItems} />
        </div>
        <ReviewConfirm 
          onBack={handleConfirmBack}
          onConfirm={handleConfirmCreate}
        />
      </div>
    );
  }

  // Feature Configuration View
  if (isFeatureConfig) {
    return (
      <div className="max-w-[1200px] p-3">
        <div className="-mt-4">
          <Breadcrumb items={breadcrumbItems} />
        </div>
        <FeatureConfiguration 
          onBack={handleFeatureBack}
          onNext={handleFeatureNext}
        />
      </div>
    );
  }

  // Organization Details View
  return (
    <div className="max-w-[1200px] p-3">
      <div className="-mt-4">
        <Breadcrumb items={breadcrumbItems} />
        <h1 className="project-panel-heading">{orgName}</h1>
      </div>

      <div className="mt-8">
        <h2 className="project-panel-heading mb-4">Verify Organisation Details</h2>
        
        <div className="space-y-6">
          <DetailsSection
            title="Organisation Details"
            icon={Building}
            details={[
              { label: "Organization Name", value: organizationDetails.organizationName },
              { label: "Industry Type", value: organizationDetails.industryType },
              { label: "Company Size", value: organizationDetails.companySize },
              { label: "Business Email", value: organizationDetails.businessEmail }
            ]}
          />

          <DetailsSection
            title="Admin Setup"
            icon={User}
            details={[
              { label: "Primary Admin", value: organizationDetails.primaryAdmin },
              { label: "Email", value: organizationDetails.email },
              { label: "Contact Number", value: organizationDetails.contactNumber },
              { label: "Business Email", value: organizationDetails.businessEmail }
            ]}
          />

          <DetailsSection
            title="Selected Plan"
            icon={CreditCard}
            details={[
              { label: "Plan Name", value: organizationDetails.planName },
              { label: "Billing Cycle", value: organizationDetails.billingCycle }
            ]}
          />
        </div>

        <div className="flex justify-between mt-8">
          <DynamicButton
            variant="secondary"
            text="Back"
            onClick={() => window.history.back()}
          />
          <DynamicButton
            variant="primary"
            text="Next : Feature Configuration"
            onClick={handleFeatureConfigClick}
          />
        </div>
      </div>
    </div>
  );
};

export default VerifyOrganizationPage;