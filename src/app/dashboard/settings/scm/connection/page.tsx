// @ts-nocheck
// @ts-ignore
"use client"
import React, { useEffect, useState, useContext } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { listRepositories } from '@/utils/repositoryAPI';
import TableComponent from '@/components/SimpleTable/ScmTableBackendPagination';
import { ScmRepoLoader } from "@/components/UIComponents/Loaders/LoaderGroup";
import EmptyStateView from '@/components/Modal/EmptyStateModal';
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { formatDateTime } from '@/utils/datetime';

type Repository = {
  repositoryName: string;
  repositoryId: string;
  path: string;
  web_url: string;
  organization: string;
  description: string | null;
  default_branch: string;
  visibility: string;
  ssh_url: string;
  http_url: string;
  created_at: string | null;
  last_activity_at: string | null;
  scm_type: string;
};

const Page: React.FC = () => {
  const [repositories, setRepositories] = useState<Repository[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    perPage: 10,
    totalCount: 0
  });
  const searchParams = useSearchParams();
  const scm_type = searchParams.get('scmType') || "github";
  const scm_id = decodeURIComponent(searchParams.get('scmId') || "");
  
  const rowsPerPage = 10;
  const router = useRouter();
  const { showAlert } = useContext(AlertContext);

  const service = scm_type;

  const fetchRepositories = async (page = 1) => {
    try {
      setIsLoading(true);
      const response = await listRepositories(service, scm_id, true, page, rowsPerPage);
      
      if (response?.status === "success" && response?.data?.repositories) {
        // Format repositories with properly formatted dates
        const formattedRepositories = response.data.repositories.map((repo) => ({
          ...repo,
          created_at: repo.created_at ? formatDateTime(repo.created_at, true) : null,
          last_activity_at: repo.last_activity_at ? formatDateTime(repo.last_activity_at, true) : null
        }));
        
        setRepositories(formattedRepositories);
        
        // Update pagination data
        setPagination({
          currentPage: response.data.pagination?.current_page || 1,
          totalPages: response.data.pagination?.total_pages || 1,
          perPage: response.data.pagination?.per_page || 10,
          totalCount: response.data.total_repositories || 0
        });
      } else {
        setRepositories([]);
        setPagination({
          currentPage: 1,
          totalPages: 1,
          perPage: 10,
          totalCount: 0
        });
      }
    } catch (error) {
      
      showAlert("Failed to fetch repositories", "error");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchRepositories();
  }, []);

  const handleRowClick = (id: string) => {
    
  };

  const handlePageChange = (page: number) => {
    fetchRepositories(page);
  };

  if(isLoading) {
    return <ScmRepoLoader />;
  }

  return (
    <div>
      <div className="flex items-center typography-body-sm text-gray-600 space-x-2 mb-6">
        <span
          className="text-gray-800 hover:text-primary hover:underline cursor-pointer transition-colors duration-300"
          onClick={() => router.push('/dashboard/settings/scm')}
        >
          SCM Providers
        </span>
        <span className="text-gray-400">{'>'}</span>
        <span
          className="text-gray-800 hover:text-primary hover:underline cursor-pointer transition-colors duration-300"
          onClick={() => router.push(`/dashboard/settings/scm/${service}`)} 
        >
          SCM Configurations
        </span>
        <span className="text-gray-400">{'>'}</span>
        <span className="text-gray-800 font-weight-medium">Repositories</span>
      </div>
      <div className='flex justify-between items-center'>
        <h1 className="typography-body-lg font-weight-semibold mt-4 mb-5">{`Repositories from ${scm_type.charAt(0).toUpperCase() + scm_type.slice(1)}`}</h1>
      </div>
      {repositories.length === 0 ? (
        <div className="text-center text-gray-500 p-6">
          <EmptyStateView type='repoNotFound' onClick={() => {}}/>
        </div>
      ) : (
        <div className="mb-6">
          <TableComponent
            headers={[
              { key: 'repositoryName', label: 'Name' },
              { key: 'organization', label: 'Organization' },
              { key: 'visibility', label: 'Visibility' },
              { key: 'default_branch', label: 'Default Branch' },
              { key: 'created_at', label: 'Created' },
              { key: 'last_activity_at', label: 'Last Activity' },
            ]}
            data={repositories}
            onRowClick={handleRowClick}
            sortableColumns={{
              repositoryName: true,
              organization: true,
              visibility: true,
              default_branch: true,
              created_at: true,
              last_activity_at: true,
            }}
            itemsPerPage={rowsPerPage}
            title={`All Repositories (${pagination.totalCount})`}
            pagination={{
              currentPage: pagination.currentPage,
              totalPages: pagination.totalPages,
              onPageChange: handlePageChange
            }}
          />
        </div>
      )}
    </div>
  );
};

export default Page;