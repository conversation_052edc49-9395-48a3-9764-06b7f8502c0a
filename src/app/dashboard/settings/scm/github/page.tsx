// @ts-nocheck
// @ts-ignore
"use client"
import React from 'react';
import { getOAuthLoginURL, getSCMConfiguration, deleteSCMConfiguration } from '@/utils/scmAPI';
import SCMConfigurationPage from '@/components/SCM/SCMConfigurationPage';

const Page: React.FC = () => {
  return (
    <SCMConfigurationPage
      providerId="github"
      getOAuthURL={getOAuthLoginURL}
      getSCMConfigs={getSCMConfiguration}
      deleteSCMConfig={deleteSCMConfiguration}
    />
  );
};

export default Page;