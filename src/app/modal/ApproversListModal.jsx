import React, { useState, useEffect, useRef } from 'react';
import { getDiscussionParticipants } from '@/utils/discussionAPI';
import Cookies from 'js-cookie';
import en from "../../en.json"

export default function ApproversListModal({ discussionId, onClose, onApprovalRequested , setModificationFeedback }) {
  const [users, setUsers] = useState([]);
  const [selectedApprover, setSelectedApprover] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const userId = Cookies.get('userId');
  const modalRef = useRef(null);
  
  useEffect(() => {
    async function fetchUsers() {
      setIsLoading(true);
      try {
        const data = await getDiscussionParticipants(discussionId, 'existing');
        const filteredUsers = data.filter(user => user.username !== userId);
        setUsers(filteredUsers);
      } catch (error) {
        
      } finally {
        setIsLoading(false);
      }
    }
    fetchUsers();

    // Add event listener for clicks outside the modal
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    // Clean up the event listener on component unmount
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [discussionId, userId, onClose]);

  const handleApproverSelection = (user) => {
    setSelectedApprover(user);
  };

  const handleRequestApproval = () => {
    if (selectedApprover) {
      onApprovalRequested(selectedApprover);
      onClose();
    }
  };

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
        <div className="bg-white rounded-lg p-6 w-96 flex justify-center items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div ref={modalRef} className="bg-white rounded-lg p-6 w-96">
        <h2 className="cost-table-heading mb-4">Select user for feedback</h2>
        {users.length === 0 ? (
          <p className="text-gray-500 mb-4">{en.NoUsersAvailableForApproval}</p>
        ) : (
          <ul className="mb-4 max-h-60 overflow-y-auto">
            {users.map((user) => (
              <li
                key={user.username}
                className={`p-2 cursor-pointer ${selectedApprover === user ? 'bg-primary-100' : 'hover:bg-gray-100'}`}
                onClick={() => handleApproverSelection(user)}
              >
                {user.name || user.email}
              </li>
            ))}
          </ul>
        )}
        <div className="flex justify-end space-x-2">
          <button
            className="px-4 py-2 bg-gray-200 rounded-md"
            onClick={onClose}
          >
            Cancel
          </button>
          <button
            className="px-4 py-2 bg-primary-600 text-white rounded-md"
            onClick={handleRequestApproval}
            disabled={!selectedApprover || users.length === 0}
          >
            Request Feedback
          </button>
        </div>
      </div>
    </div>
  );
}