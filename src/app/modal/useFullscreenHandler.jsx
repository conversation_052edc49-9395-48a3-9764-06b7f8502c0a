import React, { useState, useRef } from 'react';

// Fullscreen handler for the right panel
const useFullscreenHandler = () => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const rightPanelRef = useRef(null);

  // Function to toggle fullscreen
  const toggleFullscreen = () => {
    if (!rightPanelRef.current) return;
    
    if (!isFullscreen) {
      // Enter fullscreen
      if (rightPanelRef.current.requestFullscreen) {
        rightPanelRef.current.requestFullscreen();
      } else if (rightPanelRef.current.webkitRequestFullscreen) {
        rightPanelRef.current.webkitRequestFullscreen();
      } else if (rightPanelRef.current.msRequestFullscreen) {
        rightPanelRef.current.msRequestFullscreen();
      } else if (rightPanelRef.current.mozRequestFullScreen) {
        rightPanelRef.current.mozRequestFullScreen();
      }
    } else {
      // Exit fullscreen
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      }
    }
  };

  // Handle fullscreen change events
  React.useEffect(() => {
    const handleFullscreenChange = () => {
      const fullscreenElement = 
        document.fullscreenElement || 
        document.webkitFullscreenElement || 
        document.mozFullScreenElement || 
        document.msFullscreenElement;
      
      setIsFullscreen(!!fullscreenElement);
    };

    // Add event listeners for various browsers
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    // Clean up event listeners
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);

  return { isFullscreen, toggleFullscreen, rightPanelRef };
};

export default useFullscreenHandler;