import React, { useState, useEffect, useContext, useRef } from 'react';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { AlertContext } from '@/components/NotificationAlertService/AlertList';
import { useCodeGeneration } from '@/components/Context/CodeGenerationContext';
import { fetchEventSource } from '@microsoft/fetch-event-source';
import { getHeadersRaw } from '@/utils/api';
import { ConfirmationModal } from '@/components/CodeGenrationPanel/LoadingComponents';

const AdvancedQueryHandler = ({ 
  projectId, 
  onComplete,
  selectedRepos,
}) => {
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [isCompleted, setIsCompleted] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const { showAlert } = useContext(AlertContext);
  const router = useRouter();
  const pathname = usePathname();
  const { setIsVisible, setCurrentIframeUrl } = useCodeGeneration();
  const [logginfo, setLogInfo] = useState('');
  const [controller, setController] = useState(null);
  const hasGeneratedCode = useRef(false);
  const searchParams = useSearchParams()
  const handleNavigate = (iframe, taskId) => {
    setIsCompleted(true);
    setTimeout(() => {
      setCurrentIframeUrl(iframe);
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.set("task_id", taskId);
      router.push(`${pathname}?${newSearchParams.toString()}`);
      setIsVisible(true);
      onComplete();
    }, 3000);
  };

  useEffect(() => {
    const startDeepQuery = async () => {
    const abortController = new AbortController();
      setIsGeneratingCode(true);
      try {
        if (!hasGeneratedCode.current) {
          hasGeneratedCode.current = true;
          setController(abortController);
          
          await fetchEventSource(
            `${process.env.NEXT_PUBLIC_API_URL}/batch/start_deep_query/${projectId}/`,
            {
              method: 'POST',
              headers: {
                ...getHeadersRaw(),
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                selected_repos: selectedRepos,
              }),
              signal: abortController.signal,
              openWhenHidden: true,
              onopen: (response) => {
                if(response.status === 402){
                  abortController.abort();
                  confirmClose();
                }
                return Promise.resolve();
              },  
              onmessage: (event) => {
                try {
                  const data = JSON.parse(event.data);
                  if (data.task_id) {
                    const newSearchParams = new URLSearchParams(searchParams);
                    newSearchParams.set("task_id", data.task_id);
                    router.push(`${pathname}?${newSearchParams.toString()}`);
                  }
                  if (data.message) {
                    setLogInfo(data.message);
                  }
                  if (data.end === true) {
                    if (data.task_id) {
                      handleNavigate(data.iframe, data.task_id);
                    } else {
                      showAlert(data.message || "Unable to start task", "error");
                      onComplete();
                    }
                    abortController.abort();
                  }
                } catch (error) {
                  abortController.abort();
                  showAlert("Error processing response", "error");
                }
              },
              onerror: (error) => {
                showAlert("Error in deep analysis" + error, "error");
                abortController.abort();
                onComplete();
                return null;
              },
              onclose: () => {
                if (!isCompleted) {
                  setIsGeneratingCode(false);
                  setController(null);
                  abortController.abort();
                }
              }
            }
          );
        }
      } catch (error) {
        abortController.abort()
        showAlert("Failed to start deep query", "error");
        onComplete();
      }
    };

    startDeepQuery();

    return () => {
      if (controller) {
        controller.abort();
      }
    };
  }, [projectId, selectedRepos]); // Add description to dependencies

  const handleClose = () => setShowConfirmModal(true);
  
  const confirmClose = () => {
    if (controller) controller.abort();
    setShowConfirmModal(false);
    setIsGeneratingCode(false);
    setIsVisible(false);
    setCurrentIframeUrl(null);
    onComplete();
  };

  const cancelClose = () => setShowConfirmModal(false);

  return (
    <>
      {/* {isGeneratingCode && (
        <FullScreenLoader 
          logginfo={logginfo}
          onClose={handleClose}
          isCompleted={isCompleted}
        />
      )} */}
      {showConfirmModal && (
        <ConfirmationModal 
          onConfirm={confirmClose} 
          onCancel={cancelClose} 
        />
      )}
    </>
  );
};

export default AdvancedQueryHandler;