import { UserProvider } from '@/components/Context/UserContext';
import { AlertProvider } from '@/components/NotificationAlertService/AlertList';
import TermsAcceptanceWrapper from '@/components/TermsAcceptanceModal/TermsAcceptanceWrapper';

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body>
        <UserProvider>
          <AlertProvider>
            <TermsAcceptanceWrapper>
              {children}
            </TermsAcceptanceWrapper>
          </AlertProvider>
        </UserProvider>
      </body>
    </html>
  );
} 