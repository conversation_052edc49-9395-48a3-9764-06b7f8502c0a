"use client";
import React from "react";
import dynamic from "next/dynamic";
import ClientSideComponent from "@/components/Home/ClientSideComponent";
import "../../../app/loaders.css";

const Header = dynamic(() => import("@/components/Home/Header"), {
  ssr: false,
  loading: () => <div className="loader"><div className="spinner"></div></div>
});



export default function Page() {
  return (
    <div className="flex flex-col justify-start items-center h-full">
      <div className="tab-effect project-panel-content">
        <span>Get Started</span>
      </div>
      <Header className="w-full flex-shrink-0" />
      <div className="flex flex-col items-center flex-grow w-full mt-4 min-h-0 px-10">
        <div className="w-3/4 h-full">
          <ClientSideComponent />
        </div>
      </div>
    </div>
  );
}
