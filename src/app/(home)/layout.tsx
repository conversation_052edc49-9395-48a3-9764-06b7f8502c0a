'use client';
import '@/styles/home.css';
import '@/styles/theme-transitions.css';
import React, { ReactNode, useEffect } from 'react';
import Sidebar from '@/components/Sidebar';
import DiscussionModal from '../modal/DiscussionModal';
import { DiscussionChatProvider } from '@/components/Context/DiscussionChatContext';
import { ArchitectureProvider } from '@/components/Context/ArchitectureContext';
import { PanelProvider } from '@/components/Context/PanelContext';
import { DiscussionTabProvider} from "@/components/Context/DiscussionTabContext";
import TermsAcceptanceWrapper from '@/components/TermsAcceptanceModal/TermsAcceptanceWrapper';

interface LayoutProps {
  children: ReactNode;
}

// Client component to handle dark theme for home page only
function HomeLayoutClient({ children }: { children: ReactNode }) {
  useEffect(() => {
    // Apply dark theme only for home page
    document.documentElement.classList.add('dark');

    // Cleanup when leaving home page
    return () => {
      document.documentElement.classList.remove('dark');
    };
  }, []);

  return (
    <div className="flex theme-transition home-page-no-scroll">
      <Sidebar isDarkMode={true} />
      <div className="children-wrapper w-full theme-transition content-right-padding home-content-wrapper">
        <PanelProvider>
        <DiscussionTabProvider>

          <DiscussionChatProvider>
            <ArchitectureProvider>
              <DiscussionModal />
            </ArchitectureProvider>
          </DiscussionChatProvider>
          </ DiscussionTabProvider>

        </PanelProvider>
        {children}
      </div>
    </div>
  );
}

export default function Layout({ children }: LayoutProps) {
  return (
    <TermsAcceptanceWrapper>
      <HomeLayoutClient>
        {children}
      </HomeLayoutClient>
    </TermsAcceptanceWrapper>
  );
}