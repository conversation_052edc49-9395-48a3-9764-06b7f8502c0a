{"RecentProject_NoRecentProjectsFound": "You haven't opened any projects recently.", "RecentProject_NoProjectsFound": "You don't have any projects yet. Let's create one!", "DiscussionList_NoDiscussionsFound": "There are no discussions at the moment. Start a new one!", "AssignedTasks_NoTasksFound": "You're all caught up! No tasks assigned right now.", "ProjectsList_ErrorLoadingProjects": "Oops! We had trouble loading your projects. Please try again.", "ProjectsList_NoProjectsFound": "Time to get started! You don't have any projects yet.", "Notification_FailedToLoadNotifications": "We couldn't fetch your notifications. Let's give it another try!", "Modal_UserNotFound": "We couldn't find that user. Double-check the details?", "Modal_NoUserFound": "No matching user found. Try a different search?", "Modal_NoUsersFound": "We couldn't find any users. Try adjusting your search criteria.", "Graph_NoNodesOrEdgesFound": "This graph is empty. Time to add some data!", "ChatList_NoChatsFound": "Your chat list is empty. Start a new conversation!", "ChatList_ErrorLoadingChats": "We had trouble loading your chats. Let's try refreshing.", "BrowsePanel_Architecture_NoChildArchitectureComponents": "This component doesn't have any child elements yet.", "BrowsePanel_Architecture_NoArchitectureDataAvailable": "The architecture details aren't available right now.", "BrowsePanel_NoRepositoriesFound": "No repositories here yet. Time to add some!", "BrowsePanel_NoItemsFound": "We couldn't find any items. Try adjusting your search?", "BrowsePanel_NoItemsInTable": "No data found. Time to add some!", "Settings_FunctionalityWasNotImplemented": "This feature isn't ready yet, but we're working on it!", "ArchitectureRequirementNotFound": "We don't have any data for this yet.", "ArchitectureNotConfigured": "The architecture hasn't been set up yet. Need a hand?", "ArchitectureComponentsNotAvailable": "There aren't any child components here yet.", "WorkitemDetailsNotAvailable": "We don't have any details for this work item yet.", "FilteredDetailsNotFound": "No results match your current filters. Try broadening your search?", "OverViewTaskCount": "You're all clear! No tasks at the moment.", "MermaidChartNotAvailable": "The diagram isn't ready yet. Check back soon!", "ComponentsNotAvailable": "We don't have any components to show right now.", "HighlevelDesignNotFound": "The high-level design hasn't been created yet.", "ChildArchitectureNotAvailable": "This component doesn't have any child elements yet.", "InterfaceTableNotFound": "The interface table is empty at the moment.", "DesignDataNotAvailable": "We don't have any design data to show yet.", "AlgorithmicDetailsNotAvailable": "The algorithmic details haven't been added yet.", "StateManageMentLogicNotFound": "The state management logic isn't available right now.", "SequenceDiagramsNotAvailable": "There aren't any sequence diagrams to show yet.", "StateMachineDiagrams": "We don't have any state machine diagrams at the moment.", "UnitTestCasesNotFound": "No unit test cases have been added yet.", "IntegrationTestScenariosNotFound": "We haven't added any integration test scenarios yet.", "PerformanceTestCasesNotFound": "There aren't any performance test cases yet.", "FaultToleranceNotFound": "We don't have any fault tolerance test cases yet.", "DesignClassDiagramsNotFound": "There aren't any class diagrams to show right now.", "InterfaceNotFound": "We couldn't find any interface details. Need to add some?", "InterfaceDefinitionsNotFound": "No interface definitions have been added yet.", "InterfaceDetailsNotFound": "We don't have any interface details to show at the moment.", "SoftwareArchitectureNotAvailable": "The software architecture document isn't ready yet.", "ApiDocsNotAvailable": "The API documentation hasn't been created yet.", "FunctionalityNotImplemented": "This feature is coming soon! We're working on it.", "NotificationsNotFound": "You're all caught up! No new notifications.", "ConfigStatusNotFound": "No config status found!", "CodeGenerationNOBrowserOutputFound": "No browser output found!", "Nofunctioncallsavailable": "No function calls available", "NoUsersAvailableForApproval": "There are currently no users available for approval.", "NoRequirementsFound": "Currently, there are no requirements to display", "NoRelatedNodesFound": "No related nodes found", "NoResultFound": "No result found", "DataNotFound": "Data not found", "NoComments": "No comments yet", "NoFrames": "No frames found in the Figma file", "NoStatusFound": "No status updates at the moment.", "NoStepsAvailable": "No steps available at the moment", "NoCostDetailsAvailable": "Cost information is not available at the moment", "NoTaskPlanAvailable": "The task plan is not available at the moment.", "NoDocumentsPageAvailable": "We're working on the Documents page - stay tuned!", "NoTestExcecutionPageAvailable": "We're working on the Test Execution page - stay tuned for updates!", "NoTestCasePageAvailable": "The Test Case page is on its way - stay tuned for updates!", "NoPastDiscussionFound": "No past discussions available. Start a new conversation!", "NoContainersFound": "No containers found. It seems there aren't any available at the moment", "NoSystemContextFound": "No system context found. It seems there aren't any available at the moment", "NoChatHistoryFound": "Oops! No previous chats found. Let's start a new conversation!", "NoComponentsFound": "No components found. It seems there aren't any available at the moment", "NoDesignDetailsFound": "No components found. It seems there aren't any available at the moment", "NoChildDesignNodesFound": "  No Design Child Nodes found yet. It seems there aren't any available at the moment", "UnableToLoadSAD": "Unable to load the software architecture documents. Please refresh or try again later.", "UnableToLoadAPIDocs": "Unable to load the API documents. Please refresh or try again later.", "UnableToLoadInterface": "Unable to load the Interfaces. Please refresh or try again later.", "UnableToLoadDesign": "Unable to load the Design. Please refresh or try again later.", "UnableToLoadContainer": "Unable to load the Containers List. Please refresh or try again later.", "UnableToLoadContainerDetails": "Unable to load the container details. Please refresh or try again later.", "UnableToLoadDesignDetails": "Unable to load the Design Details. Please refresh or try again later.", "UnableToLoaComponentDetails": "Unable to load the component details. Please refresh or try again later.", "UnableToLoadComponent": "Unable to load the component Please refresh or try again later.", "UnableToLoadWorkitems": "Unable to load the work items Please refresh or try again later.", "UnableToLoadRequirements": "Unable to load the requirements Please refresh or try again later.", "UnableToLoadOverview": "Unable to load the overview Please refresh or try again later.", "UnableToLoadArchitectureRequirements": "Unable to load the architecture requirements Please refresh or try again later.", "UnableToLoadSystemContext": "Unable to load the system context Please refresh or try again later.", "UnableToLoadDesignDetailsList": "Unable to load the Design Details List. Please refresh or try again later.", "UnableToLoadDesignTypeDetails": "Unable to load the Design Type Details . Please refresh or try again later.", "NoChildComponentsNotFound": "No Component Child Nodes found yet. It seems there aren't any available at the moment", "ArchitectureRelationshipsNotAvailable": "No architecture nodes with implements relationships available.", "NoTerminalOutputAvailable": "No terminal output available", "ChildContainersHeading": "System Containers", "ChildComponentsHeading": "System Components", "ChildSubComponentsHeading": "Sub-Components", "InterfacesHeading": "Related Interfaces", "NoInterfacesFound": "No interfaces found for this component", "SearchLibrary": "Search your library", "AskQuery": "Start a new conversation to analyze your codebase", "MoreIcon": "Additional options", "DocumentUpload": "Document Upload : Upload relevant documentation that could help with your project setup.", "ProjectConfiguration": "", "ProjectConfigurationSub": "Choose the method that works best for you-whether you prefer a guided conversation to clarify your goals, or want <PERSON><PERSON> to automatically set things up for you.", "RequirementConfiguration": "Create Project Requirements : Tell us what your project needs to achieve by defining its key requirements.This step helps ensure your project is built exactly as you expect.", "UserStories": "Create User stories for each epic to define specific requirements from user's perspective.", "ArchitecturalRequirements": "Define the architectural requirements to ensure system scalability, performance, reliability, and maintainability. These include decisions about components, technologies, and structural design.", "SystemContextOverview": "Establish the system context by identifying external systems, users, and interfaces that interact with the system. This includes defining system boundaries, data flow, and key integration points to provide a clear understanding of how the system fits within its operational environment.", "SystemContextContainers": "Establish the system context by identifying external systems, users, and interfaces that interact with the system. This includes defining system boundaries, data flow, and key integration points to provide a clear understanding of how the system fits within its operational environment.", "ContainersDetails": "Configure each container in your system architecture. Containers represent applications or services that work together to implement your system's functionality.", "ListofComponents": "Define and configure the individual components within each container. Components represent distinct parts of an application, such as services, libraries, or modules, that collaborate to deliver specific functionality within the system.", "ComponentsDetails": "Define and configure the individual components within each container. Components represent distinct parts of an application, such as services, libraries, or modules, that collaborate to deliver specific functionality within the system.", "ListofInterfaces": "Configure interfaces between components and containers. Interfaces define how different parts of your system communicate with each other.", "InterfaceDetails": "Configure interfaces between components and containers. Interfaces define how different parts of your system communicate with each other.", "ListofDesigns": "Define the design specifications for your system. Design details include structural layouts, component relationships, data flows, and design principles to ensure a coherent and scalable architecture.", "DesignDetails": "Define the design specifications for your system. Design details include structural layouts, component relationships, data flows, and design principles to ensure a coherent and scalable architecture.", "ProjectUpdate": " Define your project’s scope and goals by having an interactive conversation with our AI assistant. This option guides you step by step, allowing you to clarify requirements and make decisions as you go.", "ProjectAutoConfig": "Let our system automatically generate a complete project plan for you. Using the information you’ve provided, <PERSON><PERSON> will set up everything from initial setup to architecture-no manual input required.", "RequirementUpdate": " Let the LLM assistant refine your project requirements through an interactive conversation. You’ll be guided step by step to capture all the key requirements", "RequirementAutoConfig": "Let <PERSON><PERSON> automatically set up your project requirements based on the information you’ve provided so far. This option quickly prepares your project for the next stages", "EpicUpdate": "Collaborate with our LLM assistant to refine your project details through an interactive conversation", "EpicAutoConfig": "Let our LLM automatically create user stories based on this epic's requirements.", "UserStoryUpdate": "Collaborate with our LLM assistant to refine your project details through an interactive conversation", "UserStoryAutoConfig": "Let our LLM automatically create user stories based on this epic's requirements.", "ArchReqUpdate": " Collaborate with our LLM assistant to refine your project details through interactive conversations", "ArchReqAutoConfig": "Let our LLM automatically configure your entire project based on the information you've provided", "SystemContextUpdate": "Collaborate with our LLM assistant to refine your project details through interactive conversations", "ContainerUpdate": " Collaborate with our LLM assistant to configure this container through interactive conversation", "ComponentUpdate": "Collaborate with our LLM assistant to configure this component through interactive conversation", "DesignUpdate": "Collaborate with our LLM assistant to configure this component through interactive conversation", "InterfaceUpdate": "Collaborate with our LLM assistant to configure this interface through interactive conversation"}