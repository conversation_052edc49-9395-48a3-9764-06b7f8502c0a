const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);

// Extensions to process
const jsExtensions = ['.js', '.jsx', '.ts', '.tsx'];
const cssExtensions = ['.css', '.scss'];

async function getFiles(dir, extensions) {
  const subdirs = await readdir(dir);
  const files = await Promise.all(
    subdirs.map(async (subdir) => {
      const res = path.resolve(dir, subdir);
      return (await stat(res)).isDirectory() ? getFiles(res, extensions) : res;
    })
  );
  return files
    .flat()
    .filter((file) =>
      extensions.includes(path.extname(file)) &&
      !file.includes('node_modules') &&
      !file.includes('.next')
    );
}

// Extract CSS class names from CSS/SCSS content
function extractCSSClasses(content, filePath) {
  const classes = new Set();

  // Remove comments
  content = content.replace(/\/\*[\s\S]*?\*\//g, '');
  content = content.replace(/\/\/.*$/gm, '');

  // Match CSS class selectors
  const classRegex = /\.([a-zA-Z_-][a-zA-Z0-9_-]*)/g;
  let match;

  while ((match = classRegex.exec(content)) !== null) {
    const className = match[1];

    // Skip CSS variables and pseudo-classes
    if (!className.startsWith('-') &&
        !className.includes(':') &&
        !className.includes('(') &&
        !className.includes(')')) {
      classes.add(className);
    }
  }

  return classes;
}

// Check if a class is used in JS/TS files
function isClassUsedInJS(className, allJSContent) {
  // Escape special regex characters
  const escapedClassName = className.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

  // Check for className="class-name" or className='class-name'
  const classNameRegex = new RegExp(`className\\s*=\\s*["'\`][^"'\`]*\\b${escapedClassName}\\b[^"'\`]*["'\`]`, 'g');
  if (classNameRegex.test(allJSContent)) return true;

  // Check for class="class-name" or class='class-name'
  const classRegex = new RegExp(`class\\s*=\\s*["'\`][^"'\`]*\\b${escapedClassName}\\b[^"'\`]*["'\`]`, 'g');
  if (classRegex.test(allJSContent)) return true;

  // Check for template literals and string concatenation
  const templateRegex = new RegExp(`["'\`][^"'\`]*\\b${escapedClassName}\\b[^"'\`]*["'\`]`, 'g');
  if (templateRegex.test(allJSContent)) return true;

  // Check for CSS modules usage (styles.className)
  const cssModuleRegex = new RegExp(`styles\\.${escapedClassName}\\b`, 'g');
  if (cssModuleRegex.test(allJSContent)) return true;

  // Check for dynamic class usage
  const dynamicRegex = new RegExp(`\\b${escapedClassName}\\b`, 'g');
  if (dynamicRegex.test(allJSContent)) return true;

  return false;
}

// Check if a class is part of compound selectors that might be used
function isPartOfCompoundSelector(content, className) {
  const escapedClassName = className.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

  // Check for compound selectors like .class1.class2 or .parent .child
  const compoundRegex = new RegExp(`\\.\\w+[\\s\\.][^{]*\\.${escapedClassName}|\\. ${escapedClassName}[\\s\\.][^{]*\\w+`, 'g');
  return compoundRegex.test(content);
}

// Remove unused CSS rules from content
function removeUnusedCSSRules(content, unusedClasses, filePath) {
  let modifiedContent = content;
  let removedCount = 0;

  for (const className of unusedClasses) {
    // Skip if this class is part of compound selectors (be conservative)
    if (isPartOfCompoundSelector(content, className)) {
      continue;
    }

    const escapedClassName = className.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    // Match complete CSS rules for standalone class selectors
    const standaloneRuleRegex = new RegExp(
      `^\\s*\\.${escapedClassName}\\s*\\{[^{}]*\\}\\s*$`,
      'gm'
    );

    const beforeLength = modifiedContent.length;
    modifiedContent = modifiedContent.replace(standaloneRuleRegex, '');

    if (modifiedContent.length < beforeLength) {
      removedCount++;
      console.log(`   ✅ Removed .${className}`);
    }
  }

  // Clean up empty lines and extra whitespace
  modifiedContent = modifiedContent
    .replace(/\n\s*\n\s*\n/g, '\n\n') // Remove multiple empty lines
    .replace(/^\s*\n/gm, '') // Remove empty lines at start
    .trim();

  return { content: modifiedContent, removedCount };
}

async function removeUnusedCSSClasses(dryRun = true) {
  console.log(`🔍 ${dryRun ? 'Analyzing' : 'Removing'} unused CSS classes...\n`);

  try {
    // Get all CSS and JS files
    const cssFiles = await getFiles('./src', cssExtensions);
    const jsFiles = await getFiles('./src', jsExtensions);

    console.log(`📁 Found ${cssFiles.length} CSS/SCSS files`);
    console.log(`📁 Found ${jsFiles.length} JS/TS files\n`);

    // Read all JS content once
    let allJSContent = '';
    for (const jsFile of jsFiles) {
      try {
        const content = await readFile(jsFile, 'utf8');
        allJSContent += content + '\n';
      } catch (error) {
        console.warn(`⚠️  Warning: Could not read ${jsFile}: ${error.message}`);
      }
    }

    let totalRemoved = 0;
    let totalFiles = 0;

    // Process each CSS file
    for (const cssFile of cssFiles) {
      try {
        const content = await readFile(cssFile, 'utf8');
        const classes = extractCSSClasses(content, cssFile);

        // Find unused classes in this file
        const unusedClasses = new Set();
        for (const className of classes) {
          if (!isClassUsedInJS(className, allJSContent)) {
            unusedClasses.add(className);
          }
        }

        if (unusedClasses.size > 0) {
          console.log(`📄 ${cssFile}: ${unusedClasses.size} unused classes`);

          if (!dryRun) {
            // Remove unused classes and write back
            const result = removeUnusedCSSRules(content, unusedClasses, cssFile);
            if (result.removedCount > 0) {
              await writeFile(cssFile, result.content);
              console.log(`   ✅ Actually removed ${result.removedCount} classes`);
              totalRemoved += result.removedCount;
              totalFiles++;
            } else {
              console.log(`   ⚠️  No classes were removed (all were compound selectors)`);
            }
          } else {
            // Just list the classes
            const sortedClasses = Array.from(unusedClasses).sort();
            sortedClasses.forEach(className => {
              console.log(`   .${className}`);
            });
            totalRemoved += unusedClasses.size;
            totalFiles++;
          }
        }
      } catch (error) {
        console.warn(`⚠️  Warning: Could not process ${cssFile}: ${error.message}`);
      }
    }

    console.log(`\n📊 Summary:`);
    console.log(`   Files with unused classes: ${totalFiles}`);
    console.log(`   Total unused classes: ${totalRemoved}`);

    if (dryRun) {
      console.log(`\n💡 This was a dry run. To actually remove the classes, run:`);
      console.log(`   node remove-unused-css-classes.js --remove`);
    } else {
      console.log(`\n✅ Successfully removed ${totalRemoved} unused CSS classes from ${totalFiles} files!`);
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

// Check command line arguments
const shouldRemove = process.argv.includes('--remove');
removeUnusedCSSClasses(!shouldRemove);

// node remove-unused-css-classes.js --remove
