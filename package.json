{"name": "kavia-ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "analyze": "cross-env ANALYZE=true next build"}, "dependencies": {"@codesandbox/sandpack-react": "^2.20.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@headlessui/react": "^2.1.8", "@microsoft/fetch-event-source": "^2.0.1", "@monaco-editor/react": "^4.7.0", "@mui/material": "^5.17.1", "@mui/x-date-pickers": "^6.20.2", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-slot": "^1.0.2", "@tailwindcss/forms": "^0.5.7", "axios": "^1.7.2", "chart.js": "^4.4.9", "chartjs-adapter-date-fns": "^3.0.0", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "codemirror": "^5.65.19", "cookie": "^1.0.2", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "diff": "^7.0.0", "draggabilly": "^3.0.0", "driver.js": "^1.3.5", "file-saver": "^2.0.5", "firebase": "^11.1.0", "framer-motion": "^11.11.17", "html2pdf.js": "^0.10.2", "js-cookie": "^3.0.5", "json-as-xlsx": "^2.5.6", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lodash.isequal": "^4.5.0", "lucide-react": "^0.383.0", "mermaid": "^10.9.1", "moment-timezone": "^0.5.45", "next": "^14.2.5", "prop-types": "^15.8.1", "react": "^18", "react-arborist": "^3.4.0", "react-chartjs-2": "^5.3.0", "react-codemirror2": "^8.0.1", "react-compare-slider": "^3.1.0", "react-confetti": "^6.1.0", "react-custom-scrollbars-2": "^4.5.0", "react-dom": "^18", "react-force-graph-2d": "^1.25.6", "react-force-graph-3d": "^1.24.4", "react-icons": "^5.4.0", "react-modal": "^3.16.1", "react-pdf": "^9.2.1", "react-quill": "^2.0.0", "react-simple-code-editor": "^0.14.1", "react-syntax-highlighter": "^15.5.0", "react-tooltip": "^5.28.0", "react-transition-group": "^4.4.5", "recharts": "^2.15.0", "sass": "^1.80.6", "showdown": "^2.1.0", "svg-pan-zoom": "^3.6.1", "swagger-ui-react": "^5.18.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "three-spritetext": "^1.9.0", "vis-data": "^7.1.9", "vis-network": "^9.1.9"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@next/bundle-analyzer": "^15.0.3", "@types/cookie": "^0.6.0", "@types/diff": "^7.0.1", "@types/draggabilly": "^2.1.6", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.7", "@types/lodash.isequal": "^4.5.8", "@types/react": "^18.3.3", "@types/react-dom": "^18", "@types/react-modal": "^3.16.3", "@types/swagger-ui-react": "^4.18.3", "@typescript-eslint/eslint-plugin": "^8.16.0", "@typescript-eslint/parser": "^8.31.1", "cross-env": "^7.0.3", "eslint": "^9.15.0", "eslint-config-next": "^15.0.3", "eslint-plugin-react": "^7.37.2", "eslint-plugin-unused-imports": "^4.1.4", "sharp": "^0.34.1", "tailwindcss": "^3.4.15", "typescript": "^5"}}